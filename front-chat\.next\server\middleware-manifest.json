{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_c50ec21a._.js", "server/edge/chunks/9c5b9_@auth_core_16e273d6._.js", "server/edge/chunks/96a70_jose_dist_webapi_68ebb33a._.js", "server/edge/chunks/node_modules__pnpm_60fd9d8f._.js", "server/edge/chunks/[root-of-the-server]__b9a0598e._.js", "server/edge/chunks/edge-wrapper_22e6e254.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api\\/|_next\\/|_static\\/|_vercel|[\\w-]+\\.\\w+).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api/|_next/|_static/|_vercel|[\\w-]+\\.\\w+).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "RzLJEFYUW/CFqaruOjruBpAlZrrsX4lJc3SEBXoIqAE=", "__NEXT_PREVIEW_MODE_ID": "5253857821f0d8c88b13c1d4bf3414fb", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "e557e6e9683627f72b9756762b70918e6a0194f325f99ccb1eff171d98f7fb0a", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "38c1f7a47509986111948f2e1107e18a680fb42228ba485f2e1703c9832ca954"}}}, "sortedMiddleware": ["/"], "functions": {}}
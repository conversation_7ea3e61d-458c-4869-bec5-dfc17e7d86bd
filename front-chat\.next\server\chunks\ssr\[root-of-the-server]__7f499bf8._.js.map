{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/lib/db/schema.ts"], "sourcesContent": ["import { createId } from \"@paralleldrive/cuid2\";\r\nimport { InferSelectModel, relations } from \"drizzle-orm\";\r\nimport {\r\n  boolean,\r\n  index,\r\n  integer,\r\n  json,\r\n  pgTable,\r\n  primaryKey,\r\n  text,\r\n  timestamp,\r\n  uuid,\r\n  varchar,\r\n} from \"drizzle-orm/pg-core\";\r\nimport { generateId } from \"ai\";\r\n\r\n/**\r\n * 사용자, 인증, 인가 관련 테이블은 자유롭게 수정 가능합니다.\r\n * 현재 Chat 테이블 외에는 사용하지 않음.\r\n */\r\nexport const user = pgTable(\"user\", {\r\n  id: text(\"id\")\r\n    .primaryKey()\r\n    .$defaultFn(() => createId()),\r\n  name: text(\"name\"),\r\n  // if you are using Github OAuth, you can get rid of the username attribute (that is for Twitter OAuth)\r\n  username: text(\"username\"),\r\n  gh_username: text(\"gh_username\"),\r\n  email: text(\"email\").unique(),\r\n  emailVerified: timestamp(\"emailVerified\", { mode: \"date\" }),\r\n  image: text(\"image\"),\r\n  createdAt: timestamp(\"createdAt\", { mode: \"date\" }).defaultNow().notNull(),\r\n  updatedAt: timestamp(\"updatedAt\", { mode: \"date\" })\r\n    .notNull()\r\n    .$onUpdate(() => new Date()),\r\n});\r\n\r\nexport const sessions = pgTable(\r\n  \"sessions\",\r\n  {\r\n    sessionToken: text(\"sessionToken\").primaryKey(),\r\n    userId: text(\"userId\")\r\n      .notNull()\r\n      .references(() => user.id, { onDelete: \"cascade\", onUpdate: \"cascade\" }),\r\n    expires: timestamp(\"expires\", { mode: \"date\" }).notNull(),\r\n  },\r\n  (table) => {\r\n    return {\r\n      userIdIdx: index().on(table.userId),\r\n    };\r\n  }\r\n);\r\n\r\nexport const verificationTokens = pgTable(\r\n  \"verificationTokens\",\r\n  {\r\n    identifier: text(\"identifier\").notNull(),\r\n    token: text(\"token\").notNull().unique(),\r\n    expires: timestamp(\"expires\", { mode: \"date\" }).notNull(),\r\n  },\r\n  (table) => {\r\n    return {\r\n      compositePk: primaryKey({ columns: [table.identifier, table.token] }),\r\n    };\r\n  }\r\n);\r\n\r\nexport const accounts = pgTable(\r\n  \"accounts\",\r\n  {\r\n    userId: text(\"userId\")\r\n      .notNull()\r\n      .references(() => user.id, { onDelete: \"cascade\", onUpdate: \"cascade\" }),\r\n    type: text(\"type\").notNull(),\r\n    provider: text(\"provider\").notNull(),\r\n    providerAccountId: text(\"providerAccountId\").notNull(),\r\n    refresh_token: text(\"refresh_token\"),\r\n    refreshTokenExpiresIn: integer(\"refresh_token_expires_in\"),\r\n    access_token: text(\"access_token\"),\r\n    expires_at: integer(\"expires_at\"),\r\n    token_type: text(\"token_type\"),\r\n    scope: text(\"scope\"),\r\n    id_token: text(\"id_token\"),\r\n    session_state: text(\"session_state\"),\r\n    oauth_token_secret: text(\"oauth_token_secret\"),\r\n    oauth_token: text(\"oauth_token\"),\r\n  },\r\n  (table) => {\r\n    return {\r\n      userIdIdx: index().on(table.userId),\r\n      compositePk: primaryKey({\r\n        columns: [table.provider, table.providerAccountId],\r\n      }),\r\n    };\r\n  }\r\n);\r\n\r\nexport type User = InferSelectModel<typeof user>;\r\n\r\nexport const chat = pgTable(\"Chat\", {\r\n  id: text(\"id\").primaryKey(),\r\n  createdAt: timestamp(\"createdAt\").notNull(),\r\n  title: text(\"title\").notNull().default('New Chat'), // 기본값 추가\r\n  userId: text(\"userId\").notNull(),\r\n  visibility: varchar(\"visibility\", { enum: [\"public\", \"private\"] })\r\n    .notNull()\r\n    .default(\"private\"),\r\n  deletedAt: timestamp(\"deletedAt\"), // 소프트 삭제를 위한 컬럼 (nullable)\r\n});\r\n\r\nexport type Chat = InferSelectModel<typeof chat>;\r\n\r\nexport const message = pgTable(\"Message\", {\r\n  id: uuid(\"id\").primaryKey().notNull().defaultRandom(),\r\n  chatId: text(\"chatId\")\r\n    .notNull()\r\n    .references(() => chat.id),\r\n  role: varchar(\"role\").notNull(),\r\n  content: json(\"content\"), // 기존 호환성을 위해 nullable로 변경\r\n  parts: json(\"parts\"), // AI SDK parts 배열\r\n  attachments: json(\"attachments\"), // AI SDK attachments 배열\r\n  createdAt: timestamp(\"createdAt\").notNull(),\r\n  deletedAt: timestamp(\"deletedAt\"), // 소프트 삭제를 위한 컬럼 (nullable)\r\n  enableReasoning: boolean(\"enableReasoning\"), // 추론 활성화 여부 (nullable, user 메시지에만 적용)\r\n  modelId: text(\"modelId\"), // 사용된 모델 ID (nullable, assistant 메시지에만 적용)\r\n});\r\n\r\nexport type Message = InferSelectModel<typeof message>;\r\n\r\nexport const vote = pgTable(\r\n  \"Vote\",\r\n  {\r\n    chatId: text(\"chatId\")\r\n      .notNull()\r\n      .references(() => chat.id),\r\n    messageId: uuid(\"messageId\")\r\n      .notNull()\r\n      .references(() => message.id),\r\n    isUpvoted: boolean(\"isUpvoted\").notNull(),\r\n  },\r\n  (table) => {\r\n    return {\r\n      pk: primaryKey({ columns: [table.chatId, table.messageId] }),\r\n    };\r\n  }\r\n);\r\n\r\nexport type Vote = InferSelectModel<typeof vote>;\r\n\r\nexport const map = pgTable(\"map\", {\r\n  id: text(\"id\")\r\n    .primaryKey()\r\n    .notNull()\r\n    .$defaultFn(() => generateId()),\r\n  name: text(\"name\").notNull(),\r\n  createdBy: text(\"userId\").notNull(),\r\n  createdAt: timestamp(\"createdAt\").notNull().defaultNow(),\r\n  updatedAt: timestamp(\"updatedAt\").notNull().defaultNow(),\r\n  isPublic: boolean(\"isPublic\").default(false),\r\n  // 공유되는 핵심 상태\r\n  layers: json(\"layers\").notNull(), // Layer[]\r\n  version: integer(\"version\").notNull().default(1), // 동시성 제어를 위한 버전\r\n});\r\n\r\n// 사용자별 지도 뷰 상태\r\nexport const mapView = pgTable(\r\n  \"map_view\",\r\n  {\r\n    id: text(\"id\")\r\n      .notNull()\r\n      .$defaultFn(() => generateId()), // PRIMARY KEY 제거\r\n    mapId: text(\"mapId\")\r\n      .notNull()\r\n      .references(() => map.id, { onDelete: \"cascade\" }),\r\n    userId: text(\"userId\").notNull(),\r\n    center: json(\"center\").notNull(),\r\n    zoom: integer(\"zoom\").notNull(),\r\n    basemap: text(\"basemap\").notNull(),\r\n    updatedAt: timestamp(\"updatedAt\").notNull().defaultNow(),\r\n  },\r\n  (table) => {\r\n    return {\r\n      compoundKey: primaryKey({ columns: [table.mapId, table.userId] }), // 복합 키 설정\r\n      idIdx: index(\"map_view_id_idx\").on(table.id), // id는 unique index로 변경\r\n    };\r\n  }\r\n);\r\n\r\n// 채팅-지도 연결 테이블\r\nexport const chatMap = pgTable(\"chat_map\", {\r\n  id: text(\"id\")\r\n    .primaryKey()\r\n    .notNull()\r\n    .$defaultFn(() => generateId()),\r\n  chatId: text(\"chatId\")\r\n    .notNull()\r\n    .references(() => chat.id, { onDelete: \"cascade\" }),\r\n  mapId: text(\"mapId\")\r\n    .notNull()\r\n    .references(() => map.id, { onDelete: \"restrict\" }), // 지도는 보존\r\n  createdAt: timestamp(\"createdAt\").notNull().defaultNow(),\r\n});\r\n\r\n// 지도 접근 권한\r\nexport const mapAccess = pgTable(\"map_access\", {\r\n  id: text(\"id\")\r\n    .primaryKey()\r\n    .notNull()\r\n    .$defaultFn(() => generateId()),\r\n  mapId: text(\"mapId\")\r\n    .notNull()\r\n    .references(() => map.id, { onDelete: \"cascade\" }),\r\n  userId: text(\"userId\").notNull(),\r\n  accessType: text(\"accessType\").notNull(), // 'owner', 'edit', 'view'\r\n  createdAt: timestamp(\"createdAt\").notNull().defaultNow(),\r\n});\r\n\r\n// 실시간 협업 세션\r\nexport const mapSession = pgTable(\"map_session\", {\r\n  id: text(\"id\")\r\n    .primaryKey()\r\n    .notNull()\r\n    .$defaultFn(() => generateId()),\r\n  mapId: text(\"mapId\")\r\n    .notNull()\r\n    .references(() => map.id, { onDelete: \"cascade\" }),\r\n  userId: text(\"userId\").notNull(),\r\n  isActive: boolean(\"isActive\").default(true),\r\n  lastActiveAt: timestamp(\"lastActiveAt\").notNull().defaultNow(),\r\n  // 선택적 view 동기화 설정\r\n  syncView: boolean(\"syncView\").default(false),\r\n  followingUserId: text(\"followingUserId\"), // 다른 사용자의 view를 따라갈 때\r\n});\r\n\r\n// 관계 설정\r\nexport const mapsRelation = relations(map, ({ many }) => ({\r\n  views: many(mapView),\r\n  access: many(mapAccess),\r\n  sessions: many(mapSession),\r\n  chats: many(chatMap),\r\n}));\r\n\r\nexport const chatRelation = relations(chat, ({ many }) => ({\r\n  maps: many(chatMap),\r\n}));\r\n\r\nexport const sessionsRelations = relations(sessions, ({ one }) => ({\r\n  user: one(user, { references: [user.id], fields: [sessions.userId] }),\r\n}));\r\n\r\nexport const accountsRelations = relations(accounts, ({ one }) => ({\r\n  user: one(user, { references: [user.id], fields: [accounts.userId] }),\r\n}));\r\n\r\nexport const userRelations = relations(user, ({ many }) => ({\r\n  accounts: many(accounts),\r\n  sessions: many(sessions),\r\n}));\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAYA;;;;;AAMO,MAAM,OAAO,CAAA,GAAA,8PAAA,CAAA,UAAO,AAAD,EAAE,QAAQ;IAClC,IAAI,CAAA,GAAA,wQAAA,CAAA,OAAI,AAAD,EAAE,MACN,UAAU,GACV,UAAU,CAAC,IAAM,CAAA,GAAA,sNAAA,CAAA,WAAQ,AAAD;IAC3B,MAAM,CAAA,GAAA,wQAAA,CAAA,OAAI,AAAD,EAAE;IACX,uGAAuG;IACvG,UAAU,CAAA,GAAA,wQAAA,CAAA,OAAI,AAAD,EAAE;IACf,aAAa,CAAA,GAAA,wQAAA,CAAA,OAAI,AAAD,EAAE;IAClB,OAAO,CAAA,GAAA,wQAAA,CAAA,OAAI,AAAD,EAAE,SAAS,MAAM;IAC3B,eAAe,CAAA,GAAA,6QAAA,CAAA,YAAS,AAAD,EAAE,iBAAiB;QAAE,MAAM;IAAO;IACzD,OAAO,CAAA,GAAA,wQAAA,CAAA,OAAI,AAAD,EAAE;IACZ,WAAW,CAAA,GAAA,6QAAA,CAAA,YAAS,AAAD,EAAE,aAAa;QAAE,MAAM;IAAO,GAAG,UAAU,GAAG,OAAO;IACxE,WAAW,CAAA,GAAA,6QAAA,CAAA,YAAS,AAAD,EAAE,aAAa;QAAE,MAAM;IAAO,GAC9C,OAAO,GACP,SAAS,CAAC,IAAM,IAAI;AACzB;AAEO,MAAM,WAAW,CAAA,GAAA,8PAAA,CAAA,UAAO,AAAD,EAC5B,YACA;IACE,cAAc,CAAA,GAAA,wQAAA,CAAA,OAAI,AAAD,EAAE,gBAAgB,UAAU;IAC7C,QAAQ,CAAA,GAAA,wQAAA,CAAA,OAAI,AAAD,EAAE,UACV,OAAO,GACP,UAAU,CAAC,IAAM,KAAK,EAAE,EAAE;QAAE,UAAU;QAAW,UAAU;IAAU;IACxE,SAAS,CAAA,GAAA,6QAAA,CAAA,YAAS,AAAD,EAAE,WAAW;QAAE,MAAM;IAAO,GAAG,OAAO;AACzD,GACA,CAAC;IACC,OAAO;QACL,WAAW,CAAA,GAAA,gQAAA,CAAA,QAAK,AAAD,IAAI,EAAE,CAAC,MAAM,MAAM;IACpC;AACF;AAGK,MAAM,qBAAqB,CAAA,GAAA,8PAAA,CAAA,UAAO,AAAD,EACtC,sBACA;IACE,YAAY,CAAA,GAAA,wQAAA,CAAA,OAAI,AAAD,EAAE,cAAc,OAAO;IACtC,OAAO,CAAA,GAAA,wQAAA,CAAA,OAAI,AAAD,EAAE,SAAS,OAAO,GAAG,MAAM;IACrC,SAAS,CAAA,GAAA,6QAAA,CAAA,YAAS,AAAD,EAAE,WAAW;QAAE,MAAM;IAAO,GAAG,OAAO;AACzD,GACA,CAAC;IACC,OAAO;QACL,aAAa,CAAA,GAAA,wQAAA,CAAA,aAAU,AAAD,EAAE;YAAE,SAAS;gBAAC,MAAM,UAAU;gBAAE,MAAM,KAAK;aAAC;QAAC;IACrE;AACF;AAGK,MAAM,WAAW,CAAA,GAAA,8PAAA,CAAA,UAAO,AAAD,EAC5B,YACA;IACE,QAAQ,CAAA,GAAA,wQAAA,CAAA,OAAI,AAAD,EAAE,UACV,OAAO,GACP,UAAU,CAAC,IAAM,KAAK,EAAE,EAAE;QAAE,UAAU;QAAW,UAAU;IAAU;IACxE,MAAM,CAAA,GAAA,wQAAA,CAAA,OAAI,AAAD,EAAE,QAAQ,OAAO;IAC1B,UAAU,CAAA,GAAA,wQAAA,CAAA,OAAI,AAAD,EAAE,YAAY,OAAO;IAClC,mBAAmB,CAAA,GAAA,wQAAA,CAAA,OAAI,AAAD,EAAE,qBAAqB,OAAO;IACpD,eAAe,CAAA,GAAA,wQAAA,CAAA,OAAI,AAAD,EAAE;IACpB,uBAAuB,CAAA,GAAA,2QAAA,CAAA,UAAO,AAAD,EAAE;IAC/B,cAAc,CAAA,GAAA,wQAAA,CAAA,OAAI,AAAD,EAAE;IACnB,YAAY,CAAA,GAAA,2QAAA,CAAA,UAAO,AAAD,EAAE;IACpB,YAAY,CAAA,GAAA,wQAAA,CAAA,OAAI,AAAD,EAAE;IACjB,OAAO,CAAA,GAAA,wQAAA,CAAA,OAAI,AAAD,EAAE;IACZ,UAAU,CAAA,GAAA,wQAAA,CAAA,OAAI,AAAD,EAAE;IACf,eAAe,CAAA,GAAA,wQAAA,CAAA,OAAI,AAAD,EAAE;IACpB,oBAAoB,CAAA,GAAA,wQAAA,CAAA,OAAI,AAAD,EAAE;IACzB,aAAa,CAAA,GAAA,wQAAA,CAAA,OAAI,AAAD,EAAE;AACpB,GACA,CAAC;IACC,OAAO;QACL,WAAW,CAAA,GAAA,gQAAA,CAAA,QAAK,AAAD,IAAI,EAAE,CAAC,MAAM,MAAM;QAClC,aAAa,CAAA,GAAA,wQAAA,CAAA,aAAU,AAAD,EAAE;YACtB,SAAS;gBAAC,MAAM,QAAQ;gBAAE,MAAM,iBAAiB;aAAC;QACpD;IACF;AACF;AAKK,MAAM,OAAO,CAAA,GAAA,8PAAA,CAAA,UAAO,AAAD,EAAE,QAAQ;IAClC,IAAI,CAAA,GAAA,wQAAA,CAAA,OAAI,AAAD,EAAE,MAAM,UAAU;IACzB,WAAW,CAAA,GAAA,6QAAA,CAAA,YAAS,AAAD,EAAE,aAAa,OAAO;IACzC,OAAO,CAAA,GAAA,wQAAA,CAAA,OAAI,AAAD,EAAE,SAAS,OAAO,GAAG,OAAO,CAAC;IACvC,QAAQ,CAAA,GAAA,wQAAA,CAAA,OAAI,AAAD,EAAE,UAAU,OAAO;IAC9B,YAAY,CAAA,GAAA,2QAAA,CAAA,UAAO,AAAD,EAAE,cAAc;QAAE,MAAM;YAAC;YAAU;SAAU;IAAC,GAC7D,OAAO,GACP,OAAO,CAAC;IACX,WAAW,CAAA,GAAA,6QAAA,CAAA,YAAS,AAAD,EAAE;AACvB;AAIO,MAAM,UAAU,CAAA,GAAA,8PAAA,CAAA,UAAO,AAAD,EAAE,WAAW;IACxC,IAAI,CAAA,GAAA,wQAAA,CAAA,OAAI,AAAD,EAAE,MAAM,UAAU,GAAG,OAAO,GAAG,aAAa;IACnD,QAAQ,CAAA,GAAA,wQAAA,CAAA,OAAI,AAAD,EAAE,UACV,OAAO,GACP,UAAU,CAAC,IAAM,KAAK,EAAE;IAC3B,MAAM,CAAA,GAAA,2QAAA,CAAA,UAAO,AAAD,EAAE,QAAQ,OAAO;IAC7B,SAAS,CAAA,GAAA,wQAAA,CAAA,OAAI,AAAD,EAAE;IACd,OAAO,CAAA,GAAA,wQAAA,CAAA,OAAI,AAAD,EAAE;IACZ,aAAa,CAAA,GAAA,wQAAA,CAAA,OAAI,AAAD,EAAE;IAClB,WAAW,CAAA,GAAA,6QAAA,CAAA,YAAS,AAAD,EAAE,aAAa,OAAO;IACzC,WAAW,CAAA,GAAA,6QAAA,CAAA,YAAS,AAAD,EAAE;IACrB,iBAAiB,CAAA,GAAA,2QAAA,CAAA,UAAO,AAAD,EAAE;IACzB,SAAS,CAAA,GAAA,wQAAA,CAAA,OAAI,AAAD,EAAE;AAChB;AAIO,MAAM,OAAO,CAAA,GAAA,8PAAA,CAAA,UAAO,AAAD,EACxB,QACA;IACE,QAAQ,CAAA,GAAA,wQAAA,CAAA,OAAI,AAAD,EAAE,UACV,OAAO,GACP,UAAU,CAAC,IAAM,KAAK,EAAE;IAC3B,WAAW,CAAA,GAAA,wQAAA,CAAA,OAAI,AAAD,EAAE,aACb,OAAO,GACP,UAAU,CAAC,IAAM,QAAQ,EAAE;IAC9B,WAAW,CAAA,GAAA,2QAAA,CAAA,UAAO,AAAD,EAAE,aAAa,OAAO;AACzC,GACA,CAAC;IACC,OAAO;QACL,IAAI,CAAA,GAAA,wQAAA,CAAA,aAAU,AAAD,EAAE;YAAE,SAAS;gBAAC,MAAM,MAAM;gBAAE,MAAM,SAAS;aAAC;QAAC;IAC5D;AACF;AAKK,MAAM,MAAM,CAAA,GAAA,8PAAA,CAAA,UAAO,AAAD,EAAE,OAAO;IAChC,IAAI,CAAA,GAAA,wQAAA,CAAA,OAAI,AAAD,EAAE,MACN,UAAU,GACV,OAAO,GACP,UAAU,CAAC,IAAM,CAAA,GAAA,mQAAA,CAAA,aAAU,AAAD;IAC7B,MAAM,CAAA,GAAA,wQAAA,CAAA,OAAI,AAAD,EAAE,QAAQ,OAAO;IAC1B,WAAW,CAAA,GAAA,wQAAA,CAAA,OAAI,AAAD,EAAE,UAAU,OAAO;IACjC,WAAW,CAAA,GAAA,6QAAA,CAAA,YAAS,AAAD,EAAE,aAAa,OAAO,GAAG,UAAU;IACtD,WAAW,CAAA,GAAA,6QAAA,CAAA,YAAS,AAAD,EAAE,aAAa,OAAO,GAAG,UAAU;IACtD,UAAU,CAAA,GAAA,2QAAA,CAAA,UAAO,AAAD,EAAE,YAAY,OAAO,CAAC;IACtC,aAAa;IACb,QAAQ,CAAA,GAAA,wQAAA,CAAA,OAAI,AAAD,EAAE,UAAU,OAAO;IAC9B,SAAS,CAAA,GAAA,2QAAA,CAAA,UAAO,AAAD,EAAE,WAAW,OAAO,GAAG,OAAO,CAAC;AAChD;AAGO,MAAM,UAAU,CAAA,GAAA,8PAAA,CAAA,UAAO,AAAD,EAC3B,YACA;IACE,IAAI,CAAA,GAAA,wQAAA,CAAA,OAAI,AAAD,EAAE,MACN,OAAO,GACP,UAAU,CAAC,IAAM,CAAA,GAAA,mQAAA,CAAA,aAAU,AAAD;IAC7B,OAAO,CAAA,GAAA,wQAAA,CAAA,OAAI,AAAD,EAAE,SACT,OAAO,GACP,UAAU,CAAC,IAAM,IAAI,EAAE,EAAE;QAAE,UAAU;IAAU;IAClD,QAAQ,CAAA,GAAA,wQAAA,CAAA,OAAI,AAAD,EAAE,UAAU,OAAO;IAC9B,QAAQ,CAAA,GAAA,wQAAA,CAAA,OAAI,AAAD,EAAE,UAAU,OAAO;IAC9B,MAAM,CAAA,GAAA,2QAAA,CAAA,UAAO,AAAD,EAAE,QAAQ,OAAO;IAC7B,SAAS,CAAA,GAAA,wQAAA,CAAA,OAAI,AAAD,EAAE,WAAW,OAAO;IAChC,WAAW,CAAA,GAAA,6QAAA,CAAA,YAAS,AAAD,EAAE,aAAa,OAAO,GAAG,UAAU;AACxD,GACA,CAAC;IACC,OAAO;QACL,aAAa,CAAA,GAAA,wQAAA,CAAA,aAAU,AAAD,EAAE;YAAE,SAAS;gBAAC,MAAM,KAAK;gBAAE,MAAM,MAAM;aAAC;QAAC;QAC/D,OAAO,CAAA,GAAA,gQAAA,CAAA,QAAK,AAAD,EAAE,mBAAmB,EAAE,CAAC,MAAM,EAAE;IAC7C;AACF;AAIK,MAAM,UAAU,CAAA,GAAA,8PAAA,CAAA,UAAO,AAAD,EAAE,YAAY;IACzC,IAAI,CAAA,GAAA,wQAAA,CAAA,OAAI,AAAD,EAAE,MACN,UAAU,GACV,OAAO,GACP,UAAU,CAAC,IAAM,CAAA,GAAA,mQAAA,CAAA,aAAU,AAAD;IAC7B,QAAQ,CAAA,GAAA,wQAAA,CAAA,OAAI,AAAD,EAAE,UACV,OAAO,GACP,UAAU,CAAC,IAAM,KAAK,EAAE,EAAE;QAAE,UAAU;IAAU;IACnD,OAAO,CAAA,GAAA,wQAAA,CAAA,OAAI,AAAD,EAAE,SACT,OAAO,GACP,UAAU,CAAC,IAAM,IAAI,EAAE,EAAE;QAAE,UAAU;IAAW;IACnD,WAAW,CAAA,GAAA,6QAAA,CAAA,YAAS,AAAD,EAAE,aAAa,OAAO,GAAG,UAAU;AACxD;AAGO,MAAM,YAAY,CAAA,GAAA,8PAAA,CAAA,UAAO,AAAD,EAAE,cAAc;IAC7C,IAAI,CAAA,GAAA,wQAAA,CAAA,OAAI,AAAD,EAAE,MACN,UAAU,GACV,OAAO,GACP,UAAU,CAAC,IAAM,CAAA,GAAA,mQAAA,CAAA,aAAU,AAAD;IAC7B,OAAO,CAAA,GAAA,wQAAA,CAAA,OAAI,AAAD,EAAE,SACT,OAAO,GACP,UAAU,CAAC,IAAM,IAAI,EAAE,EAAE;QAAE,UAAU;IAAU;IAClD,QAAQ,CAAA,GAAA,wQAAA,CAAA,OAAI,AAAD,EAAE,UAAU,OAAO;IAC9B,YAAY,CAAA,GAAA,wQAAA,CAAA,OAAI,AAAD,EAAE,cAAc,OAAO;IACtC,WAAW,CAAA,GAAA,6QAAA,CAAA,YAAS,AAAD,EAAE,aAAa,OAAO,GAAG,UAAU;AACxD;AAGO,MAAM,aAAa,CAAA,GAAA,8PAAA,CAAA,UAAO,AAAD,EAAE,eAAe;IAC/C,IAAI,CAAA,GAAA,wQAAA,CAAA,OAAI,AAAD,EAAE,MACN,UAAU,GACV,OAAO,GACP,UAAU,CAAC,IAAM,CAAA,GAAA,mQAAA,CAAA,aAAU,AAAD;IAC7B,OAAO,CAAA,GAAA,wQAAA,CAAA,OAAI,AAAD,EAAE,SACT,OAAO,GACP,UAAU,CAAC,IAAM,IAAI,EAAE,EAAE;QAAE,UAAU;IAAU;IAClD,QAAQ,CAAA,GAAA,wQAAA,CAAA,OAAI,AAAD,EAAE,UAAU,OAAO;IAC9B,UAAU,CAAA,GAAA,2QAAA,CAAA,UAAO,AAAD,EAAE,YAAY,OAAO,CAAC;IACtC,cAAc,CAAA,GAAA,6QAAA,CAAA,YAAS,AAAD,EAAE,gBAAgB,OAAO,GAAG,UAAU;IAC5D,kBAAkB;IAClB,UAAU,CAAA,GAAA,2QAAA,CAAA,UAAO,AAAD,EAAE,YAAY,OAAO,CAAC;IACtC,iBAAiB,CAAA,GAAA,wQAAA,CAAA,OAAI,AAAD,EAAE;AACxB;AAGO,MAAM,eAAe,CAAA,GAAA,oPAAA,CAAA,YAAS,AAAD,EAAE,KAAK,CAAC,EAAE,IAAI,EAAE,GAAK,CAAC;QACxD,OAAO,KAAK;QACZ,QAAQ,KAAK;QACb,UAAU,KAAK;QACf,OAAO,KAAK;IACd,CAAC;AAEM,MAAM,eAAe,CAAA,GAAA,oPAAA,CAAA,YAAS,AAAD,EAAE,MAAM,CAAC,EAAE,IAAI,EAAE,GAAK,CAAC;QACzD,MAAM,KAAK;IACb,CAAC;AAEM,MAAM,oBAAoB,CAAA,GAAA,oPAAA,CAAA,YAAS,AAAD,EAAE,UAAU,CAAC,EAAE,GAAG,EAAE,GAAK,CAAC;QACjE,MAAM,IAAI,MAAM;YAAE,YAAY;gBAAC,KAAK,EAAE;aAAC;YAAE,QAAQ;gBAAC,SAAS,MAAM;aAAC;QAAC;IACrE,CAAC;AAEM,MAAM,oBAAoB,CAAA,GAAA,oPAAA,CAAA,YAAS,AAAD,EAAE,UAAU,CAAC,EAAE,GAAG,EAAE,GAAK,CAAC;QACjE,MAAM,IAAI,MAAM;YAAE,YAAY;gBAAC,KAAK,EAAE;aAAC;YAAE,QAAQ;gBAAC,SAAS,MAAM;aAAC;QAAC;IACrE,CAAC;AAEM,MAAM,gBAAgB,CAAA,GAAA,oPAAA,CAAA,YAAS,AAAD,EAAE,MAAM,CAAC,EAAE,IAAI,EAAE,GAAK,CAAC;QAC1D,UAAU,KAAK;QACf,UAAU,KAAK;IACjB,CAAC", "debugId": null}}, {"offset": {"line": 277, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/lib/db/index.ts"], "sourcesContent": ["import { Pool } from \"pg\";\r\nimport { drizzle } from \"drizzle-orm/node-postgres\";\r\nimport * as schema from \"./schema\";\r\n\r\nconst pool  = new Pool  ({\r\n\tconnectionString: process.env.POSTGRES_URL\r\n});\r\n\r\nconst db = drizzle(pool, { schema, logger: true });\r\n\r\nexport default db;\r\nexport type DrizzleClient = typeof db;\r\n\r\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAEA,MAAM,OAAQ,IAAI,6FAAA,CAAA,OAAI,CAAG;IACxB,kBAAkB,QAAQ,GAAG,CAAC,YAAY;AAC3C;AAEA,MAAM,KAAK,CAAA,GAAA,qQAAA,CAAA,UAAO,AAAD,EAAE,MAAM;IAAE,QAAA;IAAQ,QAAQ;AAAK;uCAEjC", "debugId": null}}, {"offset": {"line": 300, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/lib/db/queries.ts"], "sourcesContent": ["// db/queries.ts\r\nimport { map, mapView, mapAccess, chat, Message, message, vote } from \"@/lib/db/schema\";\r\nimport { desc, eq, and, or, sql, gte, asc } from \"drizzle-orm\";\r\nimport db from \"@/lib/db\";\r\nimport { Layer, MapView } from \"@/types/map\";\r\n\r\n// Chat Queries (기존)\r\nexport async function saveChat({\r\n  id,\r\n  userId,\r\n  title,\r\n}: {\r\n  id: string;\r\n  userId: string;\r\n  title: string;\r\n}) {\r\n  try {\r\n    return await db.insert(chat).values({\r\n      id,\r\n      createdAt: new Date(),\r\n      userId,\r\n      title,\r\n    });\r\n  } catch (error) {\r\n    console.error('Failed to save chat in database');\r\n    throw error;\r\n  }\r\n}\r\n\r\nexport async function saveMessages({ messages }: { messages: Array<Message> }) {\r\n  try {\r\n    return await db.insert(message).values(messages);\r\n  } catch (error) {\r\n    console.error('Failed to save messages in database', error);\r\n    throw error;\r\n  }\r\n}\r\n\r\nexport async function updateMessage({\r\n  id,\r\n  parts,\r\n  content\r\n}: {\r\n  id: string;\r\n  parts?: any[];\r\n  content?: string;\r\n}) {\r\n  try {\r\n    const updateData: any = {};\r\n    if (parts !== undefined) updateData.parts = parts;\r\n    if (content !== undefined) updateData.content = content;\r\n\r\n    return await db.update(message).set(updateData).where(eq(message.id, id));\r\n  } catch (error) {\r\n    console.error('Failed to update message in database', error);\r\n    throw error;\r\n  }\r\n}\r\n\r\nexport async function deleteChatById({ id }: { id: string }) {\r\n  try {\r\n    // 채팅만 소프트 삭제 (투표와 메시지는 그대로 유지하여 복구 가능)\r\n    return await db.update(chat).set({ deletedAt: new Date() }).where(eq(chat.id, id));\r\n  } catch (error) {\r\n    console.error('Failed to delete chat by id from database');\r\n    throw error;\r\n  }\r\n}\r\n\r\nexport async function restoreChatById({ id }: { id: string }) {\r\n  try {\r\n    // 채팅 복구 (deletedAt을 NULL로 설정)\r\n    return await db.update(chat).set({ deletedAt: null }).where(eq(chat.id, id));\r\n  } catch (error) {\r\n    console.error('Failed to restore chat by id from database');\r\n    throw error;\r\n  }\r\n}\r\n\r\nexport async function getChatsByUserId({ id }: { id: string }) {\r\n  try {\r\n    return await db\r\n      .select()\r\n      .from(chat)\r\n      .where(\r\n        and(\r\n          eq(chat.userId, id),\r\n          sql`${chat.deletedAt} IS NULL` // 삭제되지 않은 채팅만 조회\r\n        )\r\n      )\r\n      .orderBy(desc(chat.createdAt));\r\n  } catch (error) {\r\n    console.error(\"Failed to get chats by user from database\");\r\n    throw error;\r\n  }\r\n}\r\n\r\nexport async function getChatById({ id }: { id: string }) {\r\n  try {\r\n    const [selectedChat] = await db\r\n      .select()\r\n      .from(chat)\r\n      .where(\r\n        and(\r\n          eq(chat.id, id),\r\n          sql`${chat.deletedAt} IS NULL` // 삭제되지 않은 채팅만 조회\r\n        )\r\n      );\r\n    return selectedChat;\r\n  } catch (error) {\r\n    console.error(\"Failed to get chat by id from database\");\r\n    throw error;\r\n  }\r\n}\r\n\r\nexport async function getMessageById({ id }: { id: string }) {\r\n  try {\r\n    return await db\r\n      .select()\r\n      .from(message)\r\n      .where(\r\n        and(\r\n          eq(message.id, id),\r\n          sql`${message.deletedAt} IS NULL` // 삭제되지 않은 메시지만 조회\r\n        )\r\n      );\r\n  } catch (error) {\r\n    console.error('Failed to get message by id from database');\r\n    throw error;\r\n  }\r\n}\r\n\r\nexport async function getMessagesByChatId({ id }: { id: string }) {\r\n  try {\r\n    return await db\r\n      .select()\r\n      .from(message)\r\n      .where(\r\n        and(\r\n          eq(message.chatId, id),\r\n          sql`${message.deletedAt} IS NULL` // 삭제되지 않은 메시지만 조회\r\n        )\r\n      )\r\n      .orderBy(asc(message.createdAt));\r\n  } catch (error) {\r\n    console.error('Failed to get messages by chat id from database', error);\r\n    throw error;\r\n  }\r\n}\r\n\r\nexport async function voteMessage({\r\n  chatId,\r\n  messageId,\r\n  type,\r\n}: {\r\n  chatId: string;\r\n  messageId: string;\r\n  type: 'up' | 'down';\r\n}) {\r\n  try {\r\n    const [existingVote] = await db\r\n      .select()\r\n      .from(vote)\r\n      .where(and(eq(vote.messageId, messageId)));\r\n\r\n    if (existingVote) {\r\n      return await db\r\n        .update(vote)\r\n        .set({ isUpvoted: type === 'up' })\r\n        .where(and(eq(vote.messageId, messageId), eq(vote.chatId, chatId)));\r\n    }\r\n    return await db.insert(vote).values({\r\n      chatId,\r\n      messageId,\r\n      isUpvoted: type === 'up',\r\n    });\r\n  } catch (error) {\r\n    console.error('Failed to upvote message in database', error);\r\n    throw error;\r\n  }\r\n}\r\n\r\nexport async function getVotesByChatId({ id }: { id: string }) {\r\n  try {\r\n    return await db.select().from(vote).where(eq(vote.chatId, id));\r\n  } catch (error) {\r\n    console.error('Failed to get votes by chat id from database', error);\r\n    throw error;\r\n  }\r\n}\r\n\r\nexport async function deleteMessagesByChatIdAfterTimestamp({\r\n  chatId,\r\n  timestamp,\r\n}: {\r\n  chatId: string;\r\n  timestamp: Date;\r\n}) {\r\n  try {\r\n    return await db\r\n      .update(message)\r\n      .set({ deletedAt: new Date() })\r\n      .where(\r\n        and(\r\n          eq(message.chatId, chatId),\r\n          gte(message.createdAt, timestamp),\r\n          sql`${message.deletedAt} IS NULL` // 이미 삭제된 메시지는 제외\r\n        ),\r\n      );\r\n  } catch (error) {\r\n    console.error(\r\n      'Failed to soft delete messages by id after timestamp from database',\r\n    );\r\n    throw error;\r\n  }\r\n}\r\n\r\nexport async function updateChatVisiblityById({\r\n  chatId,\r\n  visibility,\r\n}: {\r\n  chatId: string;\r\n  visibility: 'private' | 'public';\r\n}) {\r\n  try {\r\n    return await db.update(chat).set({ visibility }).where(eq(chat.id, chatId));\r\n  } catch (error) {\r\n    console.error('Failed to update chat visibility in database');\r\n    throw error;\r\n  }\r\n}\r\n\r\n\r\n// Map Queries\r\nexport async function getMapsByUserId({ userId }: { userId: string }) {\r\n  try {\r\n    return await db\r\n      .select({\r\n        id: map.id,\r\n        name: map.name,\r\n        createdAt: map.createdAt,\r\n        updatedAt: map.updatedAt,\r\n        layers: map.layers,\r\n        isPublic: map.isPublic,\r\n        activeUsers: sql<number>`\r\n        (\r\n          SELECT COUNT(DISTINCT ${mapView.userId})\r\n          FROM ${mapView}\r\n          WHERE ${mapView.mapId} = ${map.id}\r\n          AND ${mapView.updatedAt} > NOW() - INTERVAL '5 minutes'\r\n        )\r\n        `.as(\"activeUsers\"),\r\n        view: {\r\n          center: mapView.center,\r\n          zoom: mapView.zoom,\r\n          basemap: mapView.basemap,\r\n        },\r\n      })\r\n      .from(map)\r\n      .leftJoin(\r\n        mapAccess,\r\n        and(eq(mapAccess.mapId, map.id), eq(mapAccess.userId, userId))\r\n      )\r\n      .leftJoin(\r\n        mapView,\r\n        and(\r\n          eq(mapView.mapId, map.id),\r\n          eq(mapView.userId, userId)\r\n        )\r\n      )\r\n      .where(or(eq(map.createdBy, userId), eq(mapAccess.userId, userId)))\r\n      .orderBy(desc(map.updatedAt));\r\n  } catch (error) {\r\n    if (error instanceof Error) {\r\n      console.error(\"Failed to get maps by user from database:\", {\r\n        message: error.message,\r\n        stack: error.stack,\r\n      });\r\n    }\r\n    throw error;\r\n  }\r\n}\r\n\r\nexport async function getMapById({\r\n  id,\r\n  userId,\r\n}: {\r\n  id: string;\r\n  userId: string;\r\n}) {\r\n  try {\r\n    const [mapData] = await db\r\n      .select({\r\n        id: map.id,\r\n        name: map.name,\r\n        createdAt: map.createdAt,\r\n        updatedAt: map.updatedAt,\r\n        layers: map.layers,\r\n        isPublic: map.isPublic,\r\n        createdBy: map.createdBy,\r\n        activeUsers: sql<number>`\r\n        (\r\n          SELECT COUNT(DISTINCT ${mapView.userId})\r\n          FROM ${mapView}\r\n          WHERE ${mapView.mapId} = ${map.id}\r\n          AND ${mapView.updatedAt} > NOW() - INTERVAL '5 minutes'\r\n        )\r\n        `.as(\"activeUsers\"),\r\n      })\r\n      .from(map)\r\n      .where(eq(map.id, id));\r\n\r\n    if (!mapData) {\r\n      throw new Error(\"Map not found\");\r\n    }\r\n\r\n    // layers 배열의 style 객체 파싱\r\n    if (Array.isArray(mapData.layers)) {\r\n      mapData.layers = mapData.layers.map((layer: Layer) => ({\r\n        ...layer,\r\n        style: layer.style ? (\r\n          typeof layer.style === 'string' ? JSON.parse(layer.style) : layer.style\r\n        ) : undefined\r\n      }));\r\n    }\r\n\r\n    // 접근 권한 확인\r\n    if (\r\n      mapData.createdBy !== userId &&\r\n      !mapData.isPublic &&\r\n      !(await hasMapAccess({ mapId: id, userId }))\r\n    ) {\r\n      throw new Error(\"Forbidden\");\r\n    }\r\n\r\n    // 사용자별 뷰 상태 조회\r\n    const [view] = await db\r\n      .select()\r\n      .from(mapView)\r\n      .where(and(eq(mapView.mapId, id), eq(mapView.userId, userId)));\r\n\r\n    return {\r\n      ...mapData,\r\n      view: view || null,\r\n    };\r\n  } catch (error) {\r\n    console.error(\"Failed to get map by id from database\");\r\n    throw error;\r\n  }\r\n}\r\n\r\nexport async function saveMap({\r\n  id,\r\n  name,\r\n  layers,\r\n  userId,\r\n}: {\r\n  id: string;\r\n  name: string;\r\n  layers: any[];\r\n  userId: string;\r\n}) {\r\n  try {\r\n    const existingMap = await db.select().from(map).where(eq(map.id, id));\r\n\r\n    if (existingMap.length > 0) {\r\n      return await db\r\n        .update(map)\r\n        .set({\r\n          name,\r\n          layers,\r\n          updatedAt: new Date(),\r\n        })\r\n        .where(eq(map.id, id));\r\n    }\r\n\r\n    return await db.insert(map).values({\r\n      id,\r\n      name,\r\n      layers,\r\n      createdBy: userId,\r\n      createdAt: new Date(),\r\n      updatedAt: new Date(),\r\n    });\r\n  } catch (error) {\r\n    console.error(\"Failed to save map in database\");\r\n    throw error;\r\n  }\r\n}\r\n\r\nexport async function updateMapView({\r\n  mapId,\r\n  userId,\r\n  view,\r\n}: {\r\n  mapId: string;\r\n  userId: string;\r\n  view: MapView;\r\n}) {\r\n  try {\r\n    return await db\r\n      .insert(mapView)\r\n      .values({\r\n        mapId,\r\n        userId,\r\n        center: view.center ?? { lat: 36.5, lng: 127.5 },\r\n        zoom: view.zoom ?? 7,\r\n        basemap: view.basemap ?? \"eMapBasic\",\r\n        updatedAt: new Date(),\r\n      })\r\n      .onConflictDoUpdate({\r\n        target: [mapView.mapId, mapView.userId],\r\n        set: {\r\n          ...view,\r\n          updatedAt: new Date(),\r\n        },\r\n      });\r\n  } catch (error) {\r\n    console.error(\"Failed to update map view state\");\r\n    throw error;\r\n  }\r\n}\r\n\r\nexport async function deleteMapById({\r\n  id,\r\n  userId,\r\n}: {\r\n  id: string;\r\n  userId: string;\r\n}) {\r\n  try {\r\n    const [mapData] = await db.select().from(map).where(eq(map.id, id));\r\n\r\n    if (!mapData) {\r\n      throw new Error(\"Map not found\");\r\n    }\r\n\r\n    if (mapData.createdBy !== userId) {\r\n      throw new Error(\"Forbidden\");\r\n    }\r\n\r\n    return await db.delete(map).where(eq(map.id, id));\r\n  } catch (error) {\r\n    console.error(\"Failed to delete map from database\");\r\n    throw error;\r\n  }\r\n}\r\n\r\nexport async function hasMapAccess({\r\n  mapId,\r\n  userId,\r\n}: {\r\n  mapId: string;\r\n  userId: string;\r\n}) {\r\n  try {\r\n    const access = await db\r\n      .select()\r\n      .from(mapAccess)\r\n      .where(and(eq(mapAccess.mapId, mapId), eq(mapAccess.userId, userId)));\r\n\r\n    return access.length > 0;\r\n  } catch (error) {\r\n    console.error(\"Failed to check map access\");\r\n    throw error;\r\n  }\r\n}\r\n\r\nexport async function shareMap({\r\n  mapId,\r\n  userId,\r\n  targetUserId,\r\n  accessType = \"view\",\r\n}: {\r\n  mapId: string;\r\n  userId: string;\r\n  targetUserId: string;\r\n  accessType?: \"view\" | \"edit\";\r\n}) {\r\n  try {\r\n    // 공유 권한 확인\r\n    const [mapData] = await db.select().from(map).where(eq(map.id, mapId));\r\n\r\n    if (!mapData || mapData.createdBy !== userId) {\r\n      throw new Error(\"Forbidden\");\r\n    }\r\n\r\n    return await db.insert(mapAccess).values({\r\n      mapId,\r\n      userId: targetUserId,\r\n      accessType,\r\n      createdAt: new Date(),\r\n    });\r\n  } catch (error) {\r\n    console.error(\"Failed to share map\");\r\n    throw error;\r\n  }\r\n}\r\n"], "names": [], "mappings": "AAAA,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;AAChB;AACA;AAAA;AAAA;AACA;;;;AAIO,eAAe,SAAS,EAC7B,EAAE,EACF,MAAM,EACN,KAAK,EAKN;IACC,IAAI;QACF,OAAO,MAAM,kHAAA,CAAA,UAAE,CAAC,MAAM,CAAC,mHAAA,CAAA,OAAI,EAAE,MAAM,CAAC;YAClC;YACA,WAAW,IAAI;YACf;YACA;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC;QACd,MAAM;IACR;AACF;AAEO,eAAe,aAAa,EAAE,QAAQ,EAAgC;IAC3E,IAAI;QACF,OAAO,MAAM,kHAAA,CAAA,UAAE,CAAC,MAAM,CAAC,mHAAA,CAAA,UAAO,EAAE,MAAM,CAAC;IACzC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,uCAAuC;QACrD,MAAM;IACR;AACF;AAEO,eAAe,cAAc,EAClC,EAAE,EACF,KAAK,EACL,OAAO,EAKR;IACC,IAAI;QACF,MAAM,aAAkB,CAAC;QACzB,IAAI,UAAU,WAAW,WAAW,KAAK,GAAG;QAC5C,IAAI,YAAY,WAAW,WAAW,OAAO,GAAG;QAEhD,OAAO,MAAM,kHAAA,CAAA,UAAE,CAAC,MAAM,CAAC,mHAAA,CAAA,UAAO,EAAE,GAAG,CAAC,YAAY,KAAK,CAAC,CAAA,GAAA,2QAAA,CAAA,KAAE,AAAD,EAAE,mHAAA,CAAA,UAAO,CAAC,EAAE,EAAE;IACvE,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,wCAAwC;QACtD,MAAM;IACR;AACF;AAEO,eAAe,eAAe,EAAE,EAAE,EAAkB;IACzD,IAAI;QACF,uCAAuC;QACvC,OAAO,MAAM,kHAAA,CAAA,UAAE,CAAC,MAAM,CAAC,mHAAA,CAAA,OAAI,EAAE,GAAG,CAAC;YAAE,WAAW,IAAI;QAAO,GAAG,KAAK,CAAC,CAAA,GAAA,2QAAA,CAAA,KAAE,AAAD,EAAE,mHAAA,CAAA,OAAI,CAAC,EAAE,EAAE;IAChF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC;QACd,MAAM;IACR;AACF;AAEO,eAAe,gBAAgB,EAAE,EAAE,EAAkB;IAC1D,IAAI;QACF,8BAA8B;QAC9B,OAAO,MAAM,kHAAA,CAAA,UAAE,CAAC,MAAM,CAAC,mHAAA,CAAA,OAAI,EAAE,GAAG,CAAC;YAAE,WAAW;QAAK,GAAG,KAAK,CAAC,CAAA,GAAA,2QAAA,CAAA,KAAE,AAAD,EAAE,mHAAA,CAAA,OAAI,CAAC,EAAE,EAAE;IAC1E,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC;QACd,MAAM;IACR;AACF;AAEO,eAAe,iBAAiB,EAAE,EAAE,EAAkB;IAC3D,IAAI;QACF,OAAO,MAAM,kHAAA,CAAA,UAAE,CACZ,MAAM,GACN,IAAI,CAAC,mHAAA,CAAA,OAAI,EACT,KAAK,CACJ,CAAA,GAAA,2QAAA,CAAA,MAAG,AAAD,EACA,CAAA,GAAA,2QAAA,CAAA,KAAE,AAAD,EAAE,mHAAA,CAAA,OAAI,CAAC,MAAM,EAAE,KAChB,qPAAA,CAAA,MAAG,CAAC,EAAE,mHAAA,CAAA,OAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,iBAAiB;WAGnD,OAAO,CAAC,CAAA,GAAA,uQAAA,CAAA,OAAI,AAAD,EAAE,mHAAA,CAAA,OAAI,CAAC,SAAS;IAChC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC;QACd,MAAM;IACR;AACF;AAEO,eAAe,YAAY,EAAE,EAAE,EAAkB;IACtD,IAAI;QACF,MAAM,CAAC,aAAa,GAAG,MAAM,kHAAA,CAAA,UAAE,CAC5B,MAAM,GACN,IAAI,CAAC,mHAAA,CAAA,OAAI,EACT,KAAK,CACJ,CAAA,GAAA,2QAAA,CAAA,MAAG,AAAD,EACA,CAAA,GAAA,2QAAA,CAAA,KAAE,AAAD,EAAE,mHAAA,CAAA,OAAI,CAAC,EAAE,EAAE,KACZ,qPAAA,CAAA,MAAG,CAAC,EAAE,mHAAA,CAAA,OAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,iBAAiB;;QAGtD,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC;QACd,MAAM;IACR;AACF;AAEO,eAAe,eAAe,EAAE,EAAE,EAAkB;IACzD,IAAI;QACF,OAAO,MAAM,kHAAA,CAAA,UAAE,CACZ,MAAM,GACN,IAAI,CAAC,mHAAA,CAAA,UAAO,EACZ,KAAK,CACJ,CAAA,GAAA,2QAAA,CAAA,MAAG,AAAD,EACA,CAAA,GAAA,2QAAA,CAAA,KAAE,AAAD,EAAE,mHAAA,CAAA,UAAO,CAAC,EAAE,EAAE,KACf,qPAAA,CAAA,MAAG,CAAC,EAAE,mHAAA,CAAA,UAAO,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,kBAAkB;;IAG5D,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC;QACd,MAAM;IACR;AACF;AAEO,eAAe,oBAAoB,EAAE,EAAE,EAAkB;IAC9D,IAAI;QACF,OAAO,MAAM,kHAAA,CAAA,UAAE,CACZ,MAAM,GACN,IAAI,CAAC,mHAAA,CAAA,UAAO,EACZ,KAAK,CACJ,CAAA,GAAA,2QAAA,CAAA,MAAG,AAAD,EACA,CAAA,GAAA,2QAAA,CAAA,KAAE,AAAD,EAAE,mHAAA,CAAA,UAAO,CAAC,MAAM,EAAE,KACnB,qPAAA,CAAA,MAAG,CAAC,EAAE,mHAAA,CAAA,UAAO,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,kBAAkB;WAGvD,OAAO,CAAC,CAAA,GAAA,uQAAA,CAAA,MAAG,AAAD,EAAE,mHAAA,CAAA,UAAO,CAAC,SAAS;IAClC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,mDAAmD;QACjE,MAAM;IACR;AACF;AAEO,eAAe,YAAY,EAChC,MAAM,EACN,SAAS,EACT,IAAI,EAKL;IACC,IAAI;QACF,MAAM,CAAC,aAAa,GAAG,MAAM,kHAAA,CAAA,UAAE,CAC5B,MAAM,GACN,IAAI,CAAC,mHAAA,CAAA,OAAI,EACT,KAAK,CAAC,CAAA,GAAA,2QAAA,CAAA,MAAG,AAAD,EAAE,CAAA,GAAA,2QAAA,CAAA,KAAE,AAAD,EAAE,mHAAA,CAAA,OAAI,CAAC,SAAS,EAAE;QAEhC,IAAI,cAAc;YAChB,OAAO,MAAM,kHAAA,CAAA,UAAE,CACZ,MAAM,CAAC,mHAAA,CAAA,OAAI,EACX,GAAG,CAAC;gBAAE,WAAW,SAAS;YAAK,GAC/B,KAAK,CAAC,CAAA,GAAA,2QAAA,CAAA,MAAG,AAAD,EAAE,CAAA,GAAA,2QAAA,CAAA,KAAE,AAAD,EAAE,mHAAA,CAAA,OAAI,CAAC,SAAS,EAAE,YAAY,CAAA,GAAA,2QAAA,CAAA,KAAE,AAAD,EAAE,mHAAA,CAAA,OAAI,CAAC,MAAM,EAAE;QAC9D;QACA,OAAO,MAAM,kHAAA,CAAA,UAAE,CAAC,MAAM,CAAC,mHAAA,CAAA,OAAI,EAAE,MAAM,CAAC;YAClC;YACA;YACA,WAAW,SAAS;QACtB;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,wCAAwC;QACtD,MAAM;IACR;AACF;AAEO,eAAe,iBAAiB,EAAE,EAAE,EAAkB;IAC3D,IAAI;QACF,OAAO,MAAM,kHAAA,CAAA,UAAE,CAAC,MAAM,GAAG,IAAI,CAAC,mHAAA,CAAA,OAAI,EAAE,KAAK,CAAC,CAAA,GAAA,2QAAA,CAAA,KAAE,AAAD,EAAE,mHAAA,CAAA,OAAI,CAAC,MAAM,EAAE;IAC5D,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gDAAgD;QAC9D,MAAM;IACR;AACF;AAEO,eAAe,qCAAqC,EACzD,MAAM,EACN,SAAS,EAIV;IACC,IAAI;QACF,OAAO,MAAM,kHAAA,CAAA,UAAE,CACZ,MAAM,CAAC,mHAAA,CAAA,UAAO,EACd,GAAG,CAAC;YAAE,WAAW,IAAI;QAAO,GAC5B,KAAK,CACJ,CAAA,GAAA,2QAAA,CAAA,MAAG,AAAD,EACA,CAAA,GAAA,2QAAA,CAAA,KAAE,AAAD,EAAE,mHAAA,CAAA,UAAO,CAAC,MAAM,EAAE,SACnB,CAAA,GAAA,2QAAA,CAAA,MAAG,AAAD,EAAE,mHAAA,CAAA,UAAO,CAAC,SAAS,EAAE,YACvB,qPAAA,CAAA,MAAG,CAAC,EAAE,mHAAA,CAAA,UAAO,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,iBAAiB;;IAG3D,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CACX;QAEF,MAAM;IACR;AACF;AAEO,eAAe,wBAAwB,EAC5C,MAAM,EACN,UAAU,EAIX;IACC,IAAI;QACF,OAAO,MAAM,kHAAA,CAAA,UAAE,CAAC,MAAM,CAAC,mHAAA,CAAA,OAAI,EAAE,GAAG,CAAC;YAAE;QAAW,GAAG,KAAK,CAAC,CAAA,GAAA,2QAAA,CAAA,KAAE,AAAD,EAAE,mHAAA,CAAA,OAAI,CAAC,EAAE,EAAE;IACrE,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC;QACd,MAAM;IACR;AACF;AAIO,eAAe,gBAAgB,EAAE,MAAM,EAAsB;IAClE,IAAI;QACF,OAAO,MAAM,kHAAA,CAAA,UAAE,CACZ,MAAM,CAAC;YACN,IAAI,mHAAA,CAAA,MAAG,CAAC,EAAE;YACV,MAAM,mHAAA,CAAA,MAAG,CAAC,IAAI;YACd,WAAW,mHAAA,CAAA,MAAG,CAAC,SAAS;YACxB,WAAW,mHAAA,CAAA,MAAG,CAAC,SAAS;YACxB,QAAQ,mHAAA,CAAA,MAAG,CAAC,MAAM;YAClB,UAAU,mHAAA,CAAA,MAAG,CAAC,QAAQ;YACtB,aAAa,qPAAA,CAAA,MAAG,AAAQ,CAAC;;gCAED,EAAE,mHAAA,CAAA,UAAO,CAAC,MAAM,CAAC;eAClC,EAAE,mHAAA,CAAA,UAAO,CAAC;gBACT,EAAE,mHAAA,CAAA,UAAO,CAAC,KAAK,CAAC,GAAG,EAAE,mHAAA,CAAA,MAAG,CAAC,EAAE,CAAC;cAC9B,EAAE,mHAAA,CAAA,UAAO,CAAC,SAAS,CAAC;;QAE1B,CAAC,CAAC,EAAE,CAAC;YACL,MAAM;gBACJ,QAAQ,mHAAA,CAAA,UAAO,CAAC,MAAM;gBACtB,MAAM,mHAAA,CAAA,UAAO,CAAC,IAAI;gBAClB,SAAS,mHAAA,CAAA,UAAO,CAAC,OAAO;YAC1B;QACF,GACC,IAAI,CAAC,mHAAA,CAAA,MAAG,EACR,QAAQ,CACP,mHAAA,CAAA,YAAS,EACT,CAAA,GAAA,2QAAA,CAAA,MAAG,AAAD,EAAE,CAAA,GAAA,2QAAA,CAAA,KAAE,AAAD,EAAE,mHAAA,CAAA,YAAS,CAAC,KAAK,EAAE,mHAAA,CAAA,MAAG,CAAC,EAAE,GAAG,CAAA,GAAA,2QAAA,CAAA,KAAE,AAAD,EAAE,mHAAA,CAAA,YAAS,CAAC,MAAM,EAAE,UAEvD,QAAQ,CACP,mHAAA,CAAA,UAAO,EACP,CAAA,GAAA,2QAAA,CAAA,MAAG,AAAD,EACA,CAAA,GAAA,2QAAA,CAAA,KAAE,AAAD,EAAE,mHAAA,CAAA,UAAO,CAAC,KAAK,EAAE,mHAAA,CAAA,MAAG,CAAC,EAAE,GACxB,CAAA,GAAA,2QAAA,CAAA,KAAE,AAAD,EAAE,mHAAA,CAAA,UAAO,CAAC,MAAM,EAAE,UAGtB,KAAK,CAAC,CAAA,GAAA,2QAAA,CAAA,KAAE,AAAD,EAAE,CAAA,GAAA,2QAAA,CAAA,KAAE,AAAD,EAAE,mHAAA,CAAA,MAAG,CAAC,SAAS,EAAE,SAAS,CAAA,GAAA,2QAAA,CAAA,KAAE,AAAD,EAAE,mHAAA,CAAA,YAAS,CAAC,MAAM,EAAE,UACzD,OAAO,CAAC,CAAA,GAAA,uQAAA,CAAA,OAAI,AAAD,EAAE,mHAAA,CAAA,MAAG,CAAC,SAAS;IAC/B,EAAE,OAAO,OAAO;QACd,IAAI,iBAAiB,OAAO;YAC1B,QAAQ,KAAK,CAAC,6CAA6C;gBACzD,SAAS,MAAM,OAAO;gBACtB,OAAO,MAAM,KAAK;YACpB;QACF;QACA,MAAM;IACR;AACF;AAEO,eAAe,WAAW,EAC/B,EAAE,EACF,MAAM,EAIP;IACC,IAAI;QACF,MAAM,CAAC,QAAQ,GAAG,MAAM,kHAAA,CAAA,UAAE,CACvB,MAAM,CAAC;YACN,IAAI,mHAAA,CAAA,MAAG,CAAC,EAAE;YACV,MAAM,mHAAA,CAAA,MAAG,CAAC,IAAI;YACd,WAAW,mHAAA,CAAA,MAAG,CAAC,SAAS;YACxB,WAAW,mHAAA,CAAA,MAAG,CAAC,SAAS;YACxB,QAAQ,mHAAA,CAAA,MAAG,CAAC,MAAM;YAClB,UAAU,mHAAA,CAAA,MAAG,CAAC,QAAQ;YACtB,WAAW,mHAAA,CAAA,MAAG,CAAC,SAAS;YACxB,aAAa,qPAAA,CAAA,MAAG,AAAQ,CAAC;;gCAED,EAAE,mHAAA,CAAA,UAAO,CAAC,MAAM,CAAC;eAClC,EAAE,mHAAA,CAAA,UAAO,CAAC;gBACT,EAAE,mHAAA,CAAA,UAAO,CAAC,KAAK,CAAC,GAAG,EAAE,mHAAA,CAAA,MAAG,CAAC,EAAE,CAAC;cAC9B,EAAE,mHAAA,CAAA,UAAO,CAAC,SAAS,CAAC;;QAE1B,CAAC,CAAC,EAAE,CAAC;QACP,GACC,IAAI,CAAC,mHAAA,CAAA,MAAG,EACR,KAAK,CAAC,CAAA,GAAA,2QAAA,CAAA,KAAE,AAAD,EAAE,mHAAA,CAAA,MAAG,CAAC,EAAE,EAAE;QAEpB,IAAI,CAAC,SAAS;YACZ,MAAM,IAAI,MAAM;QAClB;QAEA,yBAAyB;QACzB,IAAI,MAAM,OAAO,CAAC,QAAQ,MAAM,GAAG;YACjC,QAAQ,MAAM,GAAG,QAAQ,MAAM,CAAC,GAAG,CAAC,CAAC,QAAiB,CAAC;oBACrD,GAAG,KAAK;oBACR,OAAO,MAAM,KAAK,GAChB,OAAO,MAAM,KAAK,KAAK,WAAW,KAAK,KAAK,CAAC,MAAM,KAAK,IAAI,MAAM,KAAK,GACrE;gBACN,CAAC;QACH;QAEA,WAAW;QACX,IACE,QAAQ,SAAS,KAAK,UACtB,CAAC,QAAQ,QAAQ,IACjB,CAAE,MAAM,aAAa;YAAE,OAAO;YAAI;QAAO,IACzC;YACA,MAAM,IAAI,MAAM;QAClB;QAEA,eAAe;QACf,MAAM,CAAC,KAAK,GAAG,MAAM,kHAAA,CAAA,UAAE,CACpB,MAAM,GACN,IAAI,CAAC,mHAAA,CAAA,UAAO,EACZ,KAAK,CAAC,CAAA,GAAA,2QAAA,CAAA,MAAG,AAAD,EAAE,CAAA,GAAA,2QAAA,CAAA,KAAE,AAAD,EAAE,mHAAA,CAAA,UAAO,CAAC,KAAK,EAAE,KAAK,CAAA,GAAA,2QAAA,CAAA,KAAE,AAAD,EAAE,mHAAA,CAAA,UAAO,CAAC,MAAM,EAAE;QAEvD,OAAO;YACL,GAAG,OAAO;YACV,MAAM,QAAQ;QAChB;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC;QACd,MAAM;IACR;AACF;AAEO,eAAe,QAAQ,EAC5B,EAAE,EACF,IAAI,EACJ,MAAM,EACN,MAAM,EAMP;IACC,IAAI;QACF,MAAM,cAAc,MAAM,kHAAA,CAAA,UAAE,CAAC,MAAM,GAAG,IAAI,CAAC,mHAAA,CAAA,MAAG,EAAE,KAAK,CAAC,CAAA,GAAA,2QAAA,CAAA,KAAE,AAAD,EAAE,mHAAA,CAAA,MAAG,CAAC,EAAE,EAAE;QAEjE,IAAI,YAAY,MAAM,GAAG,GAAG;YAC1B,OAAO,MAAM,kHAAA,CAAA,UAAE,CACZ,MAAM,CAAC,mHAAA,CAAA,MAAG,EACV,GAAG,CAAC;gBACH;gBACA;gBACA,WAAW,IAAI;YACjB,GACC,KAAK,CAAC,CAAA,GAAA,2QAAA,CAAA,KAAE,AAAD,EAAE,mHAAA,CAAA,MAAG,CAAC,EAAE,EAAE;QACtB;QAEA,OAAO,MAAM,kHAAA,CAAA,UAAE,CAAC,MAAM,CAAC,mHAAA,CAAA,MAAG,EAAE,MAAM,CAAC;YACjC;YACA;YACA;YACA,WAAW;YACX,WAAW,IAAI;YACf,WAAW,IAAI;QACjB;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC;QACd,MAAM;IACR;AACF;AAEO,eAAe,cAAc,EAClC,KAAK,EACL,MAAM,EACN,IAAI,EAKL;IACC,IAAI;QACF,OAAO,MAAM,kHAAA,CAAA,UAAE,CACZ,MAAM,CAAC,mHAAA,CAAA,UAAO,EACd,MAAM,CAAC;YACN;YACA;YACA,QAAQ,KAAK,MAAM,IAAI;gBAAE,KAAK;gBAAM,KAAK;YAAM;YAC/C,MAAM,KAAK,IAAI,IAAI;YACnB,SAAS,KAAK,OAAO,IAAI;YACzB,WAAW,IAAI;QACjB,GACC,kBAAkB,CAAC;YAClB,QAAQ;gBAAC,mHAAA,CAAA,UAAO,CAAC,KAAK;gBAAE,mHAAA,CAAA,UAAO,CAAC,MAAM;aAAC;YACvC,KAAK;gBACH,GAAG,IAAI;gBACP,WAAW,IAAI;YACjB;QACF;IACJ,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC;QACd,MAAM;IACR;AACF;AAEO,eAAe,cAAc,EAClC,EAAE,EACF,MAAM,EAIP;IACC,IAAI;QACF,MAAM,CAAC,QAAQ,GAAG,MAAM,kHAAA,CAAA,UAAE,CAAC,MAAM,GAAG,IAAI,CAAC,mHAAA,CAAA,MAAG,EAAE,KAAK,CAAC,CAAA,GAAA,2QAAA,CAAA,KAAE,AAAD,EAAE,mHAAA,CAAA,MAAG,CAAC,EAAE,EAAE;QAE/D,IAAI,CAAC,SAAS;YACZ,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI,QAAQ,SAAS,KAAK,QAAQ;YAChC,MAAM,IAAI,MAAM;QAClB;QAEA,OAAO,MAAM,kHAAA,CAAA,UAAE,CAAC,MAAM,CAAC,mHAAA,CAAA,MAAG,EAAE,KAAK,CAAC,CAAA,GAAA,2QAAA,CAAA,KAAE,AAAD,EAAE,mHAAA,CAAA,MAAG,CAAC,EAAE,EAAE;IAC/C,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC;QACd,MAAM;IACR;AACF;AAEO,eAAe,aAAa,EACjC,KAAK,EACL,MAAM,EAIP;IACC,IAAI;QACF,MAAM,SAAS,MAAM,kHAAA,CAAA,UAAE,CACpB,MAAM,GACN,IAAI,CAAC,mHAAA,CAAA,YAAS,EACd,KAAK,CAAC,CAAA,GAAA,2QAAA,CAAA,MAAG,AAAD,EAAE,CAAA,GAAA,2QAAA,CAAA,KAAE,AAAD,EAAE,mHAAA,CAAA,YAAS,CAAC,KAAK,EAAE,QAAQ,CAAA,GAAA,2QAAA,CAAA,KAAE,AAAD,EAAE,mHAAA,CAAA,YAAS,CAAC,MAAM,EAAE;QAE9D,OAAO,OAAO,MAAM,GAAG;IACzB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC;QACd,MAAM;IACR;AACF;AAEO,eAAe,SAAS,EAC7B,KAAK,EACL,MAAM,EACN,YAAY,EACZ,aAAa,MAAM,EAMpB;IACC,IAAI;QACF,WAAW;QACX,MAAM,CAAC,QAAQ,GAAG,MAAM,kHAAA,CAAA,UAAE,CAAC,MAAM,GAAG,IAAI,CAAC,mHAAA,CAAA,MAAG,EAAE,KAAK,CAAC,CAAA,GAAA,2QAAA,CAAA,KAAE,AAAD,EAAE,mHAAA,CAAA,MAAG,CAAC,EAAE,EAAE;QAE/D,IAAI,CAAC,WAAW,QAAQ,SAAS,KAAK,QAAQ;YAC5C,MAAM,IAAI,MAAM;QAClB;QAEA,OAAO,MAAM,kHAAA,CAAA,UAAE,CAAC,MAAM,CAAC,mHAAA,CAAA,YAAS,EAAE,MAAM,CAAC;YACvC;YACA,QAAQ;YACR;YACA,WAAW,IAAI;QACjB;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC;QACd,MAAM;IACR;AACF", "debugId": null}}, {"offset": {"line": 647, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/app/%28map%29/actions.ts"], "sourcesContent": ["'use server';\r\n\r\nimport { geon } from '@/lib/ai';\r\nimport { openai } from '@ai-sdk/openai';\r\nimport { VisibilityType } from '@/components/visibility-selector';\r\nimport { deleteMessagesByChatIdAfterTimestamp, getMessageById, updateChatVisiblityById } from '@/lib/db/queries';\r\nimport { CoreUserMessage, generateText } from 'ai';\r\nimport { cookies } from 'next/headers';\r\n\r\nexport async function saveModelId(model: string) {\r\n  const cookieStore = await cookies();\r\n  cookieStore.set('model-id', model);\r\n}\r\nexport async function saveDevModelId(model: string) {\r\n  const cookieStore = await cookies();\r\n  cookieStore.set('dev-model-id', model);\r\n}\r\n\r\nexport async function generateTitleFromUserMessage({\r\n  message,\r\n}: {\r\n  message: CoreUserMessage;\r\n}) {\r\n  const { text: title } = await generateText({\r\n    model: openai('gpt-4o-mini'),\r\n    system: `\\n\r\n    - you will generate a short title based on the first message a user begins a conversation with\r\n    - ensure it is not more than 80 characters long\r\n    - the title should be a summary of the user's message\r\n    - do not use quotes or colons`,\r\n    prompt: JSON.stringify(message),\r\n  });\r\n\r\n  return title;\r\n}\r\n\r\nexport async function deleteTrailingMessages({ id }: { id: string }) {\r\n  const [message] = await getMessageById({ id });\r\n\r\n  await deleteMessagesByChatIdAfterTimestamp({\r\n    chatId: message.chatId,\r\n    timestamp: message.createdAt,\r\n  });\r\n}\r\n\r\nexport async function updateChatVisibility({\r\n  chatId,\r\n  visibility,\r\n}: {\r\n  chatId: string;\r\n  visibility: VisibilityType;\r\n}) {\r\n  await updateChatVisiblityById({ chatId, visibility });\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;AAGA;AAEA;AACA;AACA;;;;;;;;AAEO,eAAe,YAAY,KAAa;IAC7C,MAAM,cAAc,MAAM,CAAA,GAAA,wOAAA,CAAA,UAAO,AAAD;IAChC,YAAY,GAAG,CAAC,YAAY;AAC9B;AACO,eAAe,eAAe,KAAa;IAChD,MAAM,cAAc,MAAM,CAAA,GAAA,wOAAA,CAAA,UAAO,AAAD;IAChC,YAAY,GAAG,CAAC,gBAAgB;AAClC;AAEO,eAAe,6BAA6B,EACjD,OAAO,EAGR;IACC,MAAM,EAAE,MAAM,KAAK,EAAE,GAAG,MAAM,CAAA,GAAA,8OAAA,CAAA,eAAY,AAAD,EAAE;QACzC,OAAO,CAAA,GAAA,8OAAA,CAAA,SAAM,AAAD,EAAE;QACd,QAAQ,CAAC;;;;iCAIoB,CAAC;QAC9B,QAAQ,KAAK,SAAS,CAAC;IACzB;IAEA,OAAO;AACT;AAEO,eAAe,uBAAuB,EAAE,EAAE,EAAkB;IACjE,MAAM,CAAC,QAAQ,GAAG,MAAM,CAAA,GAAA,oHAAA,CAAA,iBAAc,AAAD,EAAE;QAAE;IAAG;IAE5C,MAAM,CAAA,GAAA,oHAAA,CAAA,uCAAoC,AAAD,EAAE;QACzC,QAAQ,QAAQ,MAAM;QACtB,WAAW,QAAQ,SAAS;IAC9B;AACF;AAEO,eAAe,qBAAqB,EACzC,MAAM,EACN,UAAU,EAIX;IACC,MAAM,CAAA,GAAA,oHAAA,CAAA,0BAAuB,AAAD,EAAE;QAAE;QAAQ;IAAW;AACrD;;;IA5CsB;IAIA;IAKA;IAkBA;IASA;;AApCA,wVAAA;AAIA,wVAAA;AAKA,wVAAA;AAkBA,wVAAA;AASA,wVAAA", "debugId": null}}, {"offset": {"line": 721, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/.next-internal/server/app/%28map%29/geon-2d-map/page/actions.js%20%28server%20actions%20loader%29"], "sourcesContent": ["export {saveModelId as '406613c6f217a0744b02093d3e5a3c9a71f308ff53'} from 'ACTIONS_MODULE0'\nexport {deleteTrailingMessages as '40764eb81a775d0486660fff2140578c0e1f884c4e'} from 'ACTIONS_MODULE0'\n"], "names": [], "mappings": ";AAAA", "debugId": null}}, {"offset": {"line": 788, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/components/chat-map/chat-map.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const ChatMap = registerClientReference(\n    function() { throw new Error(\"Attempted to call ChatMap() from the server but ChatMap is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/chat-map/chat-map.tsx <module evaluation>\",\n    \"ChatMap\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,UAAU,CAAA,GAAA,8VAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,kEACA", "debugId": null}}, {"offset": {"line": 802, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/components/chat-map/chat-map.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const ChatMap = registerClientReference(\n    function() { throw new Error(\"Attempted to call ChatMap() from the server but ChatMap is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/chat-map/chat-map.tsx\",\n    \"ChatMap\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,UAAU,CAAA,GAAA,8VAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,8CACA", "debugId": null}}, {"offset": {"line": 816, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 826, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/lib/utils.ts"], "sourcesContent": ["import { Chat } from \"@/lib/db/schema\"\r\nimport { CoreMessage, generateId, Message } from \"ai\"\r\nimport { type ClassValue, clsx } from \"clsx\"\r\nimport { twMerge } from \"tailwind-merge\"\r\n\r\nimport type { Message as DBMessage } from '@/lib/db/schema';\r\n/**\r\n * Merge tailwind\r\n * @param inputs\r\n */\r\nexport function cn(...inputs: ClassValue[]) {\r\n  return twMerge(clsx(inputs))\r\n}\r\n\r\nexport function formatDate(input: string | number | Date): string {\r\n  const date = new Date(input)\r\n  return date.toLocaleDateString('korean', {\r\n    month: 'long',\r\n    day: 'numeric',\r\n    year: 'numeric'\r\n  })\r\n}\r\n\r\ninterface ApplicationError extends Error {\r\n  info: string;\r\n  status: number;\r\n}\r\n\r\nexport const fetcher = async (url: string) => {\r\n  const res = await fetch(url);\r\n\r\n  if (!res.ok) {\r\n    // 401 에러 시 로그인 페이지로 리다이렉트\r\n    if (res.status === 401) {\r\n      // 클라이언트 사이드에서만 리다이렉트 실행\r\n      if (typeof window !== 'undefined') {\r\n        const currentUrl = window.location.pathname + window.location.search;\r\n        window.location.href = `/login?callbackUrl=${encodeURIComponent(currentUrl)}`;\r\n        return; // 리다이렉트 후 함수 종료\r\n      }\r\n    }\r\n\r\n    const error = new Error(\r\n      'An error occurred while fetching the data.'\r\n    ) as ApplicationError;\r\n\r\n    error.info = await res.json();\r\n    error.status = res.status;\r\n\r\n    throw error;\r\n  }\r\n\r\n  return res.json();\r\n};\r\n\r\nexport function generateUUID(): string {\r\n  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {\r\n    const r = (Math.random() * 16) | 0;\r\n    const v = c === 'x' ? r : (r & 0x3) | 0x8;\r\n    return v.toString(16);\r\n  });\r\n}\r\n\r\n\r\n\r\nexport function convertToUIMessages(\r\n  messages: Array<DBMessage>,\r\n): Array<Message> {\r\n  // Vercel AI Chatbot 예제 패턴: parts 기반으로 메시지 변환\r\n  return messages.map((message) => {\r\n    // parts 필드가 문자열인 경우 JSON 파싱 시도\r\n    let parsedParts: Message['parts'];\r\n    try {\r\n      if (typeof message.parts === 'string') {\r\n        parsedParts = JSON.parse(message.parts);\r\n      } else {\r\n        parsedParts = message.parts as Message['parts'];\r\n      }\r\n    } catch (error) {\r\n      console.warn('Failed to parse message parts:', error);\r\n      parsedParts = [];\r\n    }\r\n\r\n    // attachments 필드도 동일하게 처리\r\n    let parsedAttachments: Array<any>;\r\n    try {\r\n      if (typeof message.attachments === 'string') {\r\n        parsedAttachments = JSON.parse(message.attachments);\r\n      } else {\r\n        parsedAttachments = (message.attachments as Array<any>) ?? [];\r\n      }\r\n    } catch (error) {\r\n      console.warn('Failed to parse message attachments:', error);\r\n      parsedAttachments = [];\r\n    }\r\n\r\n    return {\r\n      id: message.id,\r\n      parts: parsedParts,\r\n      role: message.role as Message['role'],\r\n      // Note: content will soon be deprecated in @ai-sdk/react\r\n      content: '',\r\n      createdAt: message.createdAt,\r\n      experimental_attachments: parsedAttachments,\r\n    };\r\n  });\r\n}\r\n\r\nexport function getMostRecentUserMessage(messages: Array<CoreMessage>) {\r\n  const userMessages = messages.filter((message) => message.role === 'user');\r\n  return userMessages.at(-1);\r\n}\r\n\r\nexport function getMessageIdFromAnnotations(message: Message) {\r\n  if (!message.annotations) return message.id;\r\n\r\n  const [annotation] = message.annotations;\r\n  if (!annotation) return message.id;\r\n\r\n  // @ts-expect-error messageIdFromServer is not defined in MessageAnnotation\r\n  return annotation.messageIdFromServer;\r\n}\r\n\r\n\r\n\r\nexport const defaultSystemMessage = ``\r\nexport const defaultModelId = '2JgxF9fviqd4cezxuT4UAuJCcRP2';\r\nexport const defaultChatbotId = 'chbt_CRNiaMw';\r\nexport const V2ModelId = 'Lk5521ILdWe9t18yi0zcJKU0teE3';\r\nexport const V2ChatbotId = 'chbt_ahiNhfU';\r\n\r\nexport const prunedMessages = (messages: Message[]): Message[] => {\r\n  // 1. 마지막 4개 메시지만 선택\r\n  const recentMessages = messages.slice(-4);\r\n\r\n  // 2. Human-in-the-loop 도구 호출 후 사용자가 tool-result 없이 채팅한 경우 처리\r\n  const processedMessages = recentMessages.map((message, index) => {\r\n    // Assistant 메시지에서 미완료된 tool-invocation 확인 (parts 배열 사용)\r\n    if (message.role === 'assistant' && Array.isArray(message.parts)) {\r\n      const hasIncompleteToolCall = message.parts.some((part: any) =>\r\n        part.type === 'tool-invocation' &&\r\n        part.toolInvocation?.state === 'call' &&\r\n        !part.toolInvocation?.result\r\n      );\r\n\r\n      // 미완료된 tool-invocation이 있고, 다음 메시지가 user 메시지인 경우\r\n      if (hasIncompleteToolCall && index < recentMessages.length - 1) {\r\n        const nextMessage = recentMessages[index + 1];\r\n        if (nextMessage?.role === 'user') {\r\n          // 미완료된 tool-invocation을 제거하고 텍스트 응답만 유지\r\n          const filteredParts = message.parts.filter((part: any) =>\r\n            part.type !== 'tool-invocation' ||\r\n            (part.type === 'tool-invocation' && part.toolInvocation?.result)\r\n          );\r\n\r\n          // 텍스트 응답이 없으면 기본 응답 추가\r\n          if (filteredParts.length === 0 || !filteredParts.some((p: any) => p.type === 'text')) {\r\n            filteredParts.unshift({\r\n              type: 'text',\r\n              text: '요청을 처리하고 있습니다. 추가로 도움이 필요한 것이 있나요?'\r\n            });\r\n          }\r\n\r\n          return { ...message, parts: filteredParts };\r\n        }\r\n      }\r\n\r\n      // 3. 도구 결과에서 불필요한 대용량 필드 제거\r\n      const cleanedParts = message.parts.map((part: any) => {\r\n        if (part.type === 'tool-invocation' && part.toolInvocation?.state === 'result') {\r\n          const toolName = part.toolInvocation?.toolName;\r\n          const result = part.toolInvocation.result;\r\n\r\n          // searchAddress, searchOrigin, searchDestination: geom, buildGeom 필드 제거\r\n          if (['searchAddress', 'searchOrigin', 'searchDestination'].includes(toolName)) {\r\n            if (result && typeof result === 'object' && result.result?.jusoList) {\r\n              // jusoList 배열의 각 항목에서 geom, buildGeom 필드 제거\r\n              const cleanedJusoList = result.result.jusoList.map((item: any) => {\r\n                const { geom, buildGeom, ...cleanedItem } = item;\r\n                return cleanedItem;\r\n              });\r\n\r\n              return {\r\n                ...part,\r\n                toolInvocation: {\r\n                  ...part.toolInvocation,\r\n                  result: {\r\n                    ...result,\r\n                    result: {\r\n                      ...result.result,\r\n                      jusoList: cleanedJusoList\r\n                    }\r\n                  }\r\n                }\r\n              };\r\n            }\r\n          }\r\n\r\n          // searchDirections: sections 필드 제거 (상세 경로 데이터)\r\n          if (toolName === 'searchDirections') {\r\n            if (result && typeof result === 'object' && result.routes) {\r\n              const cleanedRoutes = result.routes.map((route: any) => {\r\n                const { sections, ...cleanedRoute } = route;\r\n                return cleanedRoute;\r\n              });\r\n\r\n              return {\r\n                ...part,\r\n                toolInvocation: {\r\n                  ...part.toolInvocation,\r\n                  result: {\r\n                    ...result,\r\n                    routes: cleanedRoutes\r\n                  }\r\n                }\r\n              };\r\n            }\r\n          }\r\n\r\n          // densityAnalysis: features 필드 제거 (GeoJSON 형상 데이터)\r\n          if (toolName === 'performDensityAnalysis') {\r\n            if (result && typeof result === 'object') {\r\n              const { features, ...cleanedResult } = result;\r\n\r\n              return {\r\n                ...part,\r\n                toolInvocation: {\r\n                  ...part.toolInvocation,\r\n                  result: {\r\n                    ...cleanedResult,\r\n                    // features 배열 크기만 유지 (실제 데이터는 제거)\r\n                    featuresCount: Array.isArray(features) ? features.length : 0\r\n                  }\r\n                }\r\n              };\r\n            }\r\n          }\r\n\r\n        }\r\n        return part;\r\n      });\r\n\r\n      return { ...message, parts: cleanedParts };\r\n    }\r\n\r\n    return message;\r\n  });\r\n\r\n  return processedMessages;\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAEA;AACA;;;AAOO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,yNAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,sLAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,WAAW,KAA6B;IACtD,MAAM,OAAO,IAAI,KAAK;IACtB,OAAO,KAAK,kBAAkB,CAAC,UAAU;QACvC,OAAO;QACP,KAAK;QACL,MAAM;IACR;AACF;AAOO,MAAM,UAAU,OAAO;IAC5B,MAAM,MAAM,MAAM,MAAM;IAExB,IAAI,CAAC,IAAI,EAAE,EAAE;QACX,0BAA0B;QAC1B,IAAI,IAAI,MAAM,KAAK,KAAK;YACtB,wBAAwB;YACxB,uCAAmC;;YAInC;QACF;QAEA,MAAM,QAAQ,IAAI,MAChB;QAGF,MAAM,IAAI,GAAG,MAAM,IAAI,IAAI;QAC3B,MAAM,MAAM,GAAG,IAAI,MAAM;QAEzB,MAAM;IACR;IAEA,OAAO,IAAI,IAAI;AACjB;AAEO,SAAS;IACd,OAAO,uCAAuC,OAAO,CAAC,SAAS,CAAC;QAC9D,MAAM,IAAI,AAAC,KAAK,MAAM,KAAK,KAAM;QACjC,MAAM,IAAI,MAAM,MAAM,IAAI,AAAC,IAAI,MAAO;QACtC,OAAO,EAAE,QAAQ,CAAC;IACpB;AACF;AAIO,SAAS,oBACd,QAA0B;IAE1B,6CAA6C;IAC7C,OAAO,SAAS,GAAG,CAAC,CAAC;QACnB,+BAA+B;QAC/B,IAAI;QACJ,IAAI;YACF,IAAI,OAAO,QAAQ,KAAK,KAAK,UAAU;gBACrC,cAAc,KAAK,KAAK,CAAC,QAAQ,KAAK;YACxC,OAAO;gBACL,cAAc,QAAQ,KAAK;YAC7B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,IAAI,CAAC,kCAAkC;YAC/C,cAAc,EAAE;QAClB;QAEA,0BAA0B;QAC1B,IAAI;QACJ,IAAI;YACF,IAAI,OAAO,QAAQ,WAAW,KAAK,UAAU;gBAC3C,oBAAoB,KAAK,KAAK,CAAC,QAAQ,WAAW;YACpD,OAAO;gBACL,oBAAoB,AAAC,QAAQ,WAAW,IAAmB,EAAE;YAC/D;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,IAAI,CAAC,wCAAwC;YACrD,oBAAoB,EAAE;QACxB;QAEA,OAAO;YACL,IAAI,QAAQ,EAAE;YACd,OAAO;YACP,MAAM,QAAQ,IAAI;YAClB,yDAAyD;YACzD,SAAS;YACT,WAAW,QAAQ,SAAS;YAC5B,0BAA0B;QAC5B;IACF;AACF;AAEO,SAAS,yBAAyB,QAA4B;IACnE,MAAM,eAAe,SAAS,MAAM,CAAC,CAAC,UAAY,QAAQ,IAAI,KAAK;IACnE,OAAO,aAAa,EAAE,CAAC,CAAC;AAC1B;AAEO,SAAS,4BAA4B,OAAgB;IAC1D,IAAI,CAAC,QAAQ,WAAW,EAAE,OAAO,QAAQ,EAAE;IAE3C,MAAM,CAAC,WAAW,GAAG,QAAQ,WAAW;IACxC,IAAI,CAAC,YAAY,OAAO,QAAQ,EAAE;IAElC,2EAA2E;IAC3E,OAAO,WAAW,mBAAmB;AACvC;AAIO,MAAM,uBAAuB,EAAE;AAC/B,MAAM,iBAAiB;AACvB,MAAM,mBAAmB;AACzB,MAAM,YAAY;AAClB,MAAM,cAAc;AAEpB,MAAM,iBAAiB,CAAC;IAC7B,oBAAoB;IACpB,MAAM,iBAAiB,SAAS,KAAK,CAAC,CAAC;IAEvC,6DAA6D;IAC7D,MAAM,oBAAoB,eAAe,GAAG,CAAC,CAAC,SAAS;QACrD,wDAAwD;QACxD,IAAI,QAAQ,IAAI,KAAK,eAAe,MAAM,OAAO,CAAC,QAAQ,KAAK,GAAG;YAChE,MAAM,wBAAwB,QAAQ,KAAK,CAAC,IAAI,CAAC,CAAC,OAChD,KAAK,IAAI,KAAK,qBACd,KAAK,cAAc,EAAE,UAAU,UAC/B,CAAC,KAAK,cAAc,EAAE;YAGxB,iDAAiD;YACjD,IAAI,yBAAyB,QAAQ,eAAe,MAAM,GAAG,GAAG;gBAC9D,MAAM,cAAc,cAAc,CAAC,QAAQ,EAAE;gBAC7C,IAAI,aAAa,SAAS,QAAQ;oBAChC,wCAAwC;oBACxC,MAAM,gBAAgB,QAAQ,KAAK,CAAC,MAAM,CAAC,CAAC,OAC1C,KAAK,IAAI,KAAK,qBACb,KAAK,IAAI,KAAK,qBAAqB,KAAK,cAAc,EAAE;oBAG3D,uBAAuB;oBACvB,IAAI,cAAc,MAAM,KAAK,KAAK,CAAC,cAAc,IAAI,CAAC,CAAC,IAAW,EAAE,IAAI,KAAK,SAAS;wBACpF,cAAc,OAAO,CAAC;4BACpB,MAAM;4BACN,MAAM;wBACR;oBACF;oBAEA,OAAO;wBAAE,GAAG,OAAO;wBAAE,OAAO;oBAAc;gBAC5C;YACF;YAEA,4BAA4B;YAC5B,MAAM,eAAe,QAAQ,KAAK,CAAC,GAAG,CAAC,CAAC;gBACtC,IAAI,KAAK,IAAI,KAAK,qBAAqB,KAAK,cAAc,EAAE,UAAU,UAAU;oBAC9E,MAAM,WAAW,KAAK,cAAc,EAAE;oBACtC,MAAM,SAAS,KAAK,cAAc,CAAC,MAAM;oBAEzC,wEAAwE;oBACxE,IAAI;wBAAC;wBAAiB;wBAAgB;qBAAoB,CAAC,QAAQ,CAAC,WAAW;wBAC7E,IAAI,UAAU,OAAO,WAAW,YAAY,OAAO,MAAM,EAAE,UAAU;4BACnE,4CAA4C;4BAC5C,MAAM,kBAAkB,OAAO,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;gCAClD,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,GAAG,aAAa,GAAG;gCAC5C,OAAO;4BACT;4BAEA,OAAO;gCACL,GAAG,IAAI;gCACP,gBAAgB;oCACd,GAAG,KAAK,cAAc;oCACtB,QAAQ;wCACN,GAAG,MAAM;wCACT,QAAQ;4CACN,GAAG,OAAO,MAAM;4CAChB,UAAU;wCACZ;oCACF;gCACF;4BACF;wBACF;oBACF;oBAEA,+CAA+C;oBAC/C,IAAI,aAAa,oBAAoB;wBACnC,IAAI,UAAU,OAAO,WAAW,YAAY,OAAO,MAAM,EAAE;4BACzD,MAAM,gBAAgB,OAAO,MAAM,CAAC,GAAG,CAAC,CAAC;gCACvC,MAAM,EAAE,QAAQ,EAAE,GAAG,cAAc,GAAG;gCACtC,OAAO;4BACT;4BAEA,OAAO;gCACL,GAAG,IAAI;gCACP,gBAAgB;oCACd,GAAG,KAAK,cAAc;oCACtB,QAAQ;wCACN,GAAG,MAAM;wCACT,QAAQ;oCACV;gCACF;4BACF;wBACF;oBACF;oBAEA,mDAAmD;oBACnD,IAAI,aAAa,0BAA0B;wBACzC,IAAI,UAAU,OAAO,WAAW,UAAU;4BACxC,MAAM,EAAE,QAAQ,EAAE,GAAG,eAAe,GAAG;4BAEvC,OAAO;gCACL,GAAG,IAAI;gCACP,gBAAgB;oCACd,GAAG,KAAK,cAAc;oCACtB,QAAQ;wCACN,GAAG,aAAa;wCAChB,kCAAkC;wCAClC,eAAe,MAAM,OAAO,CAAC,YAAY,SAAS,MAAM,GAAG;oCAC7D;gCACF;4BACF;wBACF;oBACF;gBAEF;gBACA,OAAO;YACT;YAEA,OAAO;gBAAE,GAAG,OAAO;gBAAE,OAAO;YAAa;QAC3C;QAEA,OAAO;IACT;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 1047, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/app/%28map%29/geon-2d-map/page.tsx"], "sourcesContent": ["import { ChatMap } from \"@/components/chat-map/chat-map\";\r\nimport { cookies } from 'next/headers';\r\nimport { DEFAULT_MODEL_NAME, models } from '@/lib/ai/models';\r\nimport { generateUUID } from \"@/lib/utils\";\r\nimport type { Metadata } from \"next\";\r\nimport { auth } from \"@/app/(auth)/auth\";\r\nimport { unauthorized } from \"next/navigation\";\r\n\r\nexport const metadata: Metadata = {\r\n  title: \"말로 만드는 지도\",\r\n  description: \"AI와 대화하며 지도를 탐색하고 분석하세요. 자연어로 지리 정보를 검색하고 시각화할 수 있는 혁신적인 지도 서비스입니다.\",\r\n  keywords: [\"지도\", \"AI 지도\", \"대화형 지도\", \"지리정보\", \"GIS\", \"자연어 검색\", \"지도 분석\"],\r\n  openGraph: {\r\n    title: \"말로 만드는 지도 | 업무지원(챗봇)\",\r\n    description: \"AI와 대화하며 지도를 탐색하고 분석하세요. 자연어로 지리 정보를 검색하고 시각화할 수 있는 혁신적인 지도 서비스입니다.\",\r\n    type: \"website\",\r\n  },\r\n  twitter: {\r\n    card: \"summary_large_image\",\r\n    title: \"말로 만드는 지도 | 업무지원(챗봇)\",\r\n    description: \"AI와 대화하며 지도를 탐색하고 분석하세요. 자연어로 지리 정보를 검색하고 시각화할 수 있는 혁신적인 지도 서비스입니다.\",\r\n  },\r\n};\r\n\r\nexport default async function AIPage() {\r\n\r\n  const session = await auth();\r\n\r\n  if (!session) {\r\n    unauthorized();\r\n  }\r\n\r\n\r\n\tconst id = generateUUID();\r\n\r\n\tconst cookieStore = await cookies();\r\n\tconst modelIdFromCookie = cookieStore.get('model-id')?.value;\r\n\r\n\tconst selectedModelId =\r\n\t\tmodels.find((model) => model.id === modelIdFromCookie)?.id ||\r\n\t\tDEFAULT_MODEL_NAME;\r\n\r\n\treturn (\r\n\t\t<ChatMap\r\n\t\t\tkey={id}\r\n\t\t\tid={id}\r\n\t\t\tinitialMessages={[]}\r\n\t\t\tselectedModelId={selectedModelId}\r\n\t\t\tselectedVisibilityType=\"private\"\r\n\t\t\tisReadOnly={false}\r\n\t\t/>\r\n\t)\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;AAEA;AACA;AAAA;;;;;;;;AAEO,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;IACb,UAAU;QAAC;QAAM;QAAS;QAAU;QAAQ;QAAO;QAAU;KAAQ;IACrE,WAAW;QACT,OAAO;QACP,aAAa;QACb,MAAM;IACR;IACA,SAAS;QACP,MAAM;QACN,OAAO;QACP,aAAa;IACf;AACF;AAEe,eAAe;IAE5B,MAAM,UAAU,MAAM,CAAA,GAAA,uHAAA,CAAA,OAAI,AAAD;IAEzB,IAAI,CAAC,SAAS;QACZ,CAAA,GAAA,8RAAA,CAAA,eAAY,AAAD;IACb;IAGD,MAAM,KAAK,CAAA,GAAA,4GAAA,CAAA,eAAY,AAAD;IAEtB,MAAM,cAAc,MAAM,CAAA,GAAA,wOAAA,CAAA,UAAO,AAAD;IAChC,MAAM,oBAAoB,YAAY,GAAG,CAAC,aAAa;IAEvD,MAAM,kBACL,mHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,CAAC,QAAU,MAAM,EAAE,KAAK,oBAAoB,MACxD,mHAAA,CAAA,qBAAkB;IAEnB,qBACC,uVAAC,yIAAA,CAAA,UAAO;QAEP,IAAI;QACJ,iBAAiB,EAAE;QACnB,iBAAiB;QACjB,wBAAuB;QACvB,YAAY;OALP;;;;;AAQR", "debugId": null}}]}
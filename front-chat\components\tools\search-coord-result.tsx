"use client";

import React, { useState, useCallback } from "react";
import {
  MapPin, Check, Navigation, Building2, Copy, ChevronDown, ChevronUp,
  Route, MoreHorizontal, Star
} from "lucide-react";
import { AddressResponse } from "@/types/tools";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator
} from "@/components/ui/dropdown-menu";
import { toast } from "sonner";
import { cn } from "@/lib/utils";
import { highlightPointOnMap, type UseMapReturn } from "@/lib/map-utils";
import { componentStyles } from "@/lib/design-tokens";
import { CompactResultTrigger } from "./common/compact-result-trigger";
import { getToolDisplayInfo } from "../annotations";
import { Badge } from "../ui/badge";

interface SearchCoordResultProps {
  content: AddressResponse;
  className?: string;
  mapState?: UseMapReturn;
  onDirectionsRequest?: (origin: string, destination: string) => void;
}

// 주소 아이템 타입 정의
interface AddressItem {
  roadAddr: string;
  jibunAddr: string;
  buildName: string;
  buildLo: string;
  buildLa: string;
  parcelLo: string;
  parcelLa: string;
  poiName: string;
  buildGeom?: string;
  geom?: string;
}

export function SearchCoordResult({
  content,
  className,
  mapState,
  onDirectionsRequest
}: SearchCoordResultProps) {
  const [showAll, setShowAll] = useState(false);
  const [selectedOrigin, setSelectedOrigin] = useState<AddressItem | null>(null);
  const [selectedDestination, setSelectedDestination] = useState<AddressItem | null>(null);

  let result: AddressResponse;

  try {
    result = content as AddressResponse;
  } catch (e) {
    return null; // 파싱 실패시 기본 UI로 fallback
  }

  // 좌표 형식 변환 함수
  const formatCoordinateForDirections = useCallback((address: AddressItem) => {
    const lon = address.buildLo || address.parcelLo;
    const lat = address.buildLa || address.parcelLa;
    const name = address.buildName || address.poiName || "위치";
    return `${lon},${lat},name=${name}`;
  }, []);

  // 지도에서 위치 강조 표시
  const handleHighlightOnMap = useCallback((address: AddressItem) => {
    if (!mapState) return;
    
    const lon = parseFloat(address.buildLo || address.parcelLo);
    const lat = parseFloat(address.buildLa || address.parcelLa);
    
    if (!isNaN(lon) && !isNaN(lat)) {
      highlightPointOnMap(mapState, lon, lat);
      toast.success("지도에서 위치를 확인하세요");
    }
  }, [mapState]);

  // 길찾기 요청 함수
  const handleDirectionsRequest = useCallback((origin: AddressItem, destination: AddressItem) => {
    const originStr = formatCoordinateForDirections(origin);
    const destinationStr = formatCoordinateForDirections(destination);

    if (onDirectionsRequest) {
      onDirectionsRequest(originStr, destinationStr);
    }

    toast.success(`${origin.buildName || '출발지'}에서 ${destination.buildName || '목적지'}까지 경로를 검색합니다`);
  }, [formatCoordinateForDirections, onDirectionsRequest]);

  const handleCopyAddress = useCallback((address: string) => {
    navigator.clipboard.writeText(address);
    toast.success("주소가 복사되었습니다");
  }, []);

  const handleCopyCoordinates = useCallback((lon: string, lat: string) => {
    const coordinates = `${lon}, ${lat}`;
    navigator.clipboard.writeText(coordinates);
    toast.success("좌표가 복사되었습니다");
  }, []);

  // 출발지/목적지 선택 토글
  const toggleOriginSelection = useCallback((address: AddressItem) => {
    setSelectedOrigin(prev => prev?.roadAddr === address.roadAddr ? null : address);
  }, []);

  const toggleDestinationSelection = useCallback((address: AddressItem) => {
    setSelectedDestination(prev => prev?.roadAddr === address.roadAddr ? null : address);
  }, []);

  // 길찾기 실행
  const executeDirections = useCallback(() => {
    if (selectedOrigin && selectedDestination) {
      handleDirectionsRequest(selectedOrigin, selectedDestination);
      // 선택 초기화
      setSelectedOrigin(null);
      setSelectedDestination(null);
    }
  }, [selectedOrigin, selectedDestination, handleDirectionsRequest]);

  if (!result.result?.jusoList?.length) {
    return (
      <div className={cn("rounded-xl border border-amber-200/60 bg-gradient-to-r from-amber-50/80 to-orange-50/80 backdrop-blur-sm p-3", className)}>
        <div className="flex items-center gap-3">
          <div className="flex h-7 w-7 items-center justify-center rounded-full bg-amber-100/80">
            <MapPin className="h-3.5 w-3.5 text-amber-600" />
          </div>
          <div>
            <p className="font-medium text-amber-900 text-sm">좌표 검색 결과가 없습니다</p>
            <p className="text-xs text-amber-700">다른 좌표로 다시 검색해보세요</p>
          </div>
        </div>
      </div>
    );
  }

  const addresses = result.result.jusoList;
  const displayAddresses = showAll ? addresses : addresses.slice(0, 3);

  const toolInfo = getToolDisplayInfo("searchCoord");

  return (
    <div className={cn(componentStyles.card.default, "overflow-hidden", className)}>
      <CompactResultTrigger
        title={toolInfo.name}
        icon={toolInfo.icon}
        description={`좌표 검색 완료 - ${addresses.length}개 위치 발견`}
        resultCount={addresses.length}
        className="border-b border-blue-100/60"
      />
      
      <CardContent className="p-0">
        <div className="space-y-2 p-3">
          {displayAddresses.map((address: AddressItem, index: number) => {
            const isOriginSelected = selectedOrigin?.roadAddr === address.roadAddr;
            const isDestinationSelected = selectedDestination?.roadAddr === address.roadAddr;
            
            return (
              <div
                key={index}
                className={cn(
                  "group relative rounded-lg border transition-all duration-200",
                  "border-gray-200/60 bg-white/80 hover:border-blue-200/80 hover:bg-blue-50/30",
                  isOriginSelected && "border-green-300/80 bg-green-50/50",
                  isDestinationSelected && "border-red-300/80 bg-red-50/50"
                )}
              >
                <div className="p-3">
                  <div className="flex items-start justify-between gap-3">
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2 mb-1">
                        <div className={cn(componentStyles.iconContainer.xs, "bg-blue-100/80 text-blue-600")}>
                          <MapPin className="h-3 w-3" />
                        </div>
                        {address.buildName && (
                          <Badge variant="secondary" className="text-xs px-1.5 py-0.5">
                            {address.buildName}
                          </Badge>
                        )}
                        {isOriginSelected && (
                          <Badge className="text-xs px-1.5 py-0.5 bg-green-100 text-green-700 border-green-200">
                            출발지
                          </Badge>
                        )}
                        {isDestinationSelected && (
                          <Badge className="text-xs px-1.5 py-0.5 bg-red-100 text-red-700 border-red-200">
                            목적지
                          </Badge>
                        )}
                      </div>
                      
                      <p className="font-medium text-gray-900 text-sm leading-tight mb-1">
                        {address.roadAddr}
                      </p>
                      
                      {address.jibunAddr && address.jibunAddr !== address.roadAddr && (
                        <p className="text-xs text-gray-600 mb-1">
                          지번: {address.jibunAddr}
                        </p>
                      )}
                      
                      <div className="flex items-center gap-2 text-xs text-gray-500">
                        <span>좌표: {address.buildLo || address.parcelLo}, {address.buildLa || address.parcelLa}</span>
                      </div>
                    </div>

                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="h-7 w-7 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
                        >
                          <MoreHorizontal className="h-3.5 w-3.5" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end" className="w-48">
                        <DropdownMenuItem onClick={() => handleHighlightOnMap(address)}>
                          <MapPin className="h-3.5 w-3.5 mr-2" />
                          지도에서 보기
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleCopyAddress(address.roadAddr)}>
                          <Copy className="h-3.5 w-3.5 mr-2" />
                          주소 복사
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleCopyCoordinates(
                          address.buildLo || address.parcelLo,
                          address.buildLa || address.parcelLa
                        )}>
                          <Copy className="h-3.5 w-3.5 mr-2" />
                          좌표 복사
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem onClick={() => toggleOriginSelection(address)}>
                          <Navigation className="h-3.5 w-3.5 mr-2" />
                          {isOriginSelected ? '출발지 해제' : '출발지로 설정'}
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => toggleDestinationSelection(address)}>
                          <Route className="h-3.5 w-3.5 mr-2" />
                          {isDestinationSelected ? '목적지 해제' : '목적지로 설정'}
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </div>
              </div>
            );
          })}
        </div>

        {addresses.length > 3 && (
          <div className="border-t border-gray-100/60 p-3">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowAll(!showAll)}
              className="w-full justify-center text-xs"
            >
              {showAll ? (
                <>
                  <ChevronUp className="h-3.5 w-3.5 mr-1" />
                  간단히 보기
                </>
              ) : (
                <>
                  <ChevronDown className="h-3.5 w-3.5 mr-1" />
                  {addresses.length - 3}개 더 보기
                </>
              )}
            </Button>
          </div>
        )}

        {(selectedOrigin && selectedDestination) && (
          <div className="border-t border-gray-100/60 p-3 bg-gradient-to-r from-blue-50/50 to-indigo-50/50">
            <Button
              onClick={executeDirections}
              size="sm"
              className="w-full"
            >
              <Route className="h-3.5 w-3.5 mr-2" />
              길찾기 실행
            </Button>
          </div>
        )}
      </CardContent>
    </div>
  );
}

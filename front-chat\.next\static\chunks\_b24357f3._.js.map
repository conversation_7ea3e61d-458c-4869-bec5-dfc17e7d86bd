{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/app/%28map%29/actions.ts"], "sourcesContent": ["'use server';\r\n\r\nimport { geon } from '@/lib/ai';\r\nimport { openai } from '@ai-sdk/openai';\r\nimport { VisibilityType } from '@/components/visibility-selector';\r\nimport { deleteMessagesByChatIdAfterTimestamp, getMessageById, updateChatVisiblityById } from '@/lib/db/queries';\r\nimport { CoreUserMessage, generateText } from 'ai';\r\nimport { cookies } from 'next/headers';\r\n\r\nexport async function saveModelId(model: string) {\r\n  const cookieStore = await cookies();\r\n  cookieStore.set('model-id', model);\r\n}\r\nexport async function saveDevModelId(model: string) {\r\n  const cookieStore = await cookies();\r\n  cookieStore.set('dev-model-id', model);\r\n}\r\n\r\nexport async function generateTitleFromUserMessage({\r\n  message,\r\n}: {\r\n  message: CoreUserMessage;\r\n}) {\r\n  const { text: title } = await generateText({\r\n    model: openai('gpt-4o-mini'),\r\n    system: `\\n\r\n    - you will generate a short title based on the first message a user begins a conversation with\r\n    - ensure it is not more than 80 characters long\r\n    - the title should be a summary of the user's message\r\n    - do not use quotes or colons`,\r\n    prompt: JSON.stringify(message),\r\n  });\r\n\r\n  return title;\r\n}\r\n\r\nexport async function deleteTrailingMessages({ id }: { id: string }) {\r\n  const [message] = await getMessageById({ id });\r\n\r\n  await deleteMessagesByChatIdAfterTimestamp({\r\n    chatId: message.chatId,\r\n    timestamp: message.createdAt,\r\n  });\r\n}\r\n\r\nexport async function updateChatVisibility({\r\n  chatId,\r\n  visibility,\r\n}: {\r\n  chatId: string;\r\n  visibility: VisibilityType;\r\n}) {\r\n  await updateChatVisiblityById({ chatId, visibility });\r\n}\r\n"], "names": [], "mappings": ";;;;;;IAasB,iBAAA,WAAA,GAAA,CAAA,GAAA,kUAAA,CAAA,wBAAA,EAAA,8CAAA,kUAAA,CAAA,aAAA,EAAA,KAAA,GAAA,kUAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { Slot } from \"@radix-ui/react-slot\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst buttonVariants = cva(\r\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\r\n        destructive:\r\n          \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\r\n        outline:\r\n          \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\r\n        secondary:\r\n          \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\r\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\r\n        link: \"text-primary underline-offset-4 hover:underline\",\r\n      },\r\n      size: {\r\n        default: \"h-10 px-4 py-2\",\r\n        sm: \"h-9 rounded-md px-3\",\r\n        lg: \"h-11 rounded-md px-8\",\r\n        icon: \"h-10 w-10\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n      size: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nexport interface ButtonProps\r\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\r\n    VariantProps<typeof buttonVariants> {\r\n  asChild?: boolean\r\n}\r\n\r\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\r\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\r\n    const Comp = asChild ? Slot : \"button\"\r\n    return (\r\n      <Comp\r\n        className={cn(buttonVariants({ variant, size, className }))}\r\n        ref={ref}\r\n        {...props}\r\n      />\r\n    )\r\n  }\r\n)\r\nButton.displayName = \"Button\"\r\n\r\nexport { Button, buttonVariants }\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AAEA;;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,8OAAA,CAAA,MAAG,AAAD,EACvB,4VACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,CAAA,GAAA,sQAAA,CAAA,aAAgB,AAAD,OAC5B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,MAAM,OAAO,UAAU,uSAAA,CAAA,OAAI,GAAG;IAC9B,qBACE,sSAAC;QACC,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 90, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/components/ui/dropdown-menu.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as DropdownMenuPrimitive from \"@radix-ui/react-dropdown-menu\"\r\nimport { Check, ChevronRight, Circle } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst DropdownMenu = DropdownMenuPrimitive.Root\r\n\r\nconst DropdownMenuTrigger = DropdownMenuPrimitive.Trigger\r\n\r\nconst DropdownMenuGroup = DropdownMenuPrimitive.Group\r\n\r\nconst DropdownMenuPortal = DropdownMenuPrimitive.Portal\r\n\r\nconst DropdownMenuSub = DropdownMenuPrimitive.Sub\r\n\r\nconst DropdownMenuRadioGroup = DropdownMenuPrimitive.RadioGroup\r\n\r\nconst DropdownMenuSubTrigger = React.forwardRef<\r\n  React.ElementRef<typeof DropdownMenuPrimitive.SubTrigger>,\r\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.SubTrigger> & {\r\n    inset?: boolean\r\n  }\r\n>(({ className, inset, children, ...props }, ref) => (\r\n  <DropdownMenuPrimitive.SubTrigger\r\n    ref={ref}\r\n    className={cn(\r\n      \"flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent\",\r\n      inset && \"pl-8\",\r\n      className\r\n    )}\r\n    {...props}\r\n  >\r\n    {children}\r\n    <ChevronRight className=\"ml-auto h-4 w-4\" />\r\n  </DropdownMenuPrimitive.SubTrigger>\r\n))\r\nDropdownMenuSubTrigger.displayName =\r\n  DropdownMenuPrimitive.SubTrigger.displayName\r\n\r\nconst DropdownMenuSubContent = React.forwardRef<\r\n  React.ElementRef<typeof DropdownMenuPrimitive.SubContent>,\r\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.SubContent>\r\n>(({ className, ...props }, ref) => (\r\n  <DropdownMenuPrimitive.SubContent\r\n    ref={ref}\r\n    className={cn(\r\n      \"z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nDropdownMenuSubContent.displayName =\r\n  DropdownMenuPrimitive.SubContent.displayName\r\n\r\nconst DropdownMenuContent = React.forwardRef<\r\n  React.ElementRef<typeof DropdownMenuPrimitive.Content>,\r\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Content>\r\n>(({ className, sideOffset = 4, ...props }, ref) => (\r\n  <DropdownMenuPrimitive.Portal>\r\n    <DropdownMenuPrimitive.Content\r\n      ref={ref}\r\n      sideOffset={sideOffset}\r\n      className={cn(\r\n        \"z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  </DropdownMenuPrimitive.Portal>\r\n))\r\nDropdownMenuContent.displayName = DropdownMenuPrimitive.Content.displayName\r\n\r\nconst DropdownMenuItem = React.forwardRef<\r\n  React.ElementRef<typeof DropdownMenuPrimitive.Item>,\r\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Item> & {\r\n    inset?: boolean\r\n  }\r\n>(({ className, inset, ...props }, ref) => (\r\n  <DropdownMenuPrimitive.Item\r\n    ref={ref}\r\n    className={cn(\r\n      \"relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\r\n      inset && \"pl-8\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nDropdownMenuItem.displayName = DropdownMenuPrimitive.Item.displayName\r\n\r\nconst DropdownMenuCheckboxItem = React.forwardRef<\r\n  React.ElementRef<typeof DropdownMenuPrimitive.CheckboxItem>,\r\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.CheckboxItem>\r\n>(({ className, children, checked, ...props }, ref) => (\r\n  <DropdownMenuPrimitive.CheckboxItem\r\n    ref={ref}\r\n    className={cn(\r\n      \"relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\r\n      className\r\n    )}\r\n    checked={checked}\r\n    {...props}\r\n  >\r\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\r\n      <DropdownMenuPrimitive.ItemIndicator>\r\n        <Check className=\"h-4 w-4\" />\r\n      </DropdownMenuPrimitive.ItemIndicator>\r\n    </span>\r\n    {children}\r\n  </DropdownMenuPrimitive.CheckboxItem>\r\n))\r\nDropdownMenuCheckboxItem.displayName =\r\n  DropdownMenuPrimitive.CheckboxItem.displayName\r\n\r\nconst DropdownMenuRadioItem = React.forwardRef<\r\n  React.ElementRef<typeof DropdownMenuPrimitive.RadioItem>,\r\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.RadioItem>\r\n>(({ className, children, ...props }, ref) => (\r\n  <DropdownMenuPrimitive.RadioItem\r\n    ref={ref}\r\n    className={cn(\r\n      \"relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\r\n      className\r\n    )}\r\n    {...props}\r\n  >\r\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\r\n      <DropdownMenuPrimitive.ItemIndicator>\r\n        <Circle className=\"h-2 w-2 fill-current\" />\r\n      </DropdownMenuPrimitive.ItemIndicator>\r\n    </span>\r\n    {children}\r\n  </DropdownMenuPrimitive.RadioItem>\r\n))\r\nDropdownMenuRadioItem.displayName = DropdownMenuPrimitive.RadioItem.displayName\r\n\r\nconst DropdownMenuLabel = React.forwardRef<\r\n  React.ElementRef<typeof DropdownMenuPrimitive.Label>,\r\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Label> & {\r\n    inset?: boolean\r\n  }\r\n>(({ className, inset, ...props }, ref) => (\r\n  <DropdownMenuPrimitive.Label\r\n    ref={ref}\r\n    className={cn(\r\n      \"px-2 py-1.5 text-sm font-semibold\",\r\n      inset && \"pl-8\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nDropdownMenuLabel.displayName = DropdownMenuPrimitive.Label.displayName\r\n\r\nconst DropdownMenuSeparator = React.forwardRef<\r\n  React.ElementRef<typeof DropdownMenuPrimitive.Separator>,\r\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Separator>\r\n>(({ className, ...props }, ref) => (\r\n  <DropdownMenuPrimitive.Separator\r\n    ref={ref}\r\n    className={cn(\"-mx-1 my-1 h-px bg-muted\", className)}\r\n    {...props}\r\n  />\r\n))\r\nDropdownMenuSeparator.displayName = DropdownMenuPrimitive.Separator.displayName\r\n\r\nconst DropdownMenuShortcut = ({\r\n  className,\r\n  ...props\r\n}: React.HTMLAttributes<HTMLSpanElement>) => {\r\n  return (\r\n    <span\r\n      className={cn(\"ml-auto text-xs tracking-widest opacity-60\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\nDropdownMenuShortcut.displayName = \"DropdownMenuShortcut\"\r\n\r\nexport {\r\n  DropdownMenu,\r\n  DropdownMenuTrigger,\r\n  DropdownMenuContent,\r\n  DropdownMenuItem,\r\n  DropdownMenuCheckboxItem,\r\n  DropdownMenuRadioItem,\r\n  DropdownMenuLabel,\r\n  DropdownMenuSeparator,\r\n  DropdownMenuShortcut,\r\n  DropdownMenuGroup,\r\n  DropdownMenuPortal,\r\n  DropdownMenuSub,\r\n  DropdownMenuSubContent,\r\n  DropdownMenuSubTrigger,\r\n  DropdownMenuRadioGroup,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAEA;AACA;AACA;AAAA;AAAA;AAEA;AANA;;;;;;AAQA,MAAM,eAAe,sRAAA,CAAA,OAA0B;AAE/C,MAAM,sBAAsB,sRAAA,CAAA,UAA6B;AAEzD,MAAM,oBAAoB,sRAAA,CAAA,QAA2B;AAErD,MAAM,qBAAqB,sRAAA,CAAA,SAA4B;AAEvD,MAAM,kBAAkB,sRAAA,CAAA,MAAyB;AAEjD,MAAM,yBAAyB,sRAAA,CAAA,aAAgC;AAE/D,MAAM,uCAAyB,CAAA,GAAA,sQAAA,CAAA,aAAgB,AAAD,OAK5C,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBAC3C,sSAAC,sRAAA,CAAA,aAAgC;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,wIACA,SAAS,QACT;QAED,GAAG,KAAK;;YAER;0BACD,sSAAC,6SAAA,CAAA,eAAY;gBAAC,WAAU;;;;;;;;;;;;;AAG5B,uBAAuB,WAAW,GAChC,sRAAA,CAAA,aAAgC,CAAC,WAAW;AAE9C,MAAM,uCAAyB,CAAA,GAAA,sQAAA,CAAA,aAAgB,AAAD,QAG5C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,sSAAC,sRAAA,CAAA,aAAgC;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,ybACA;QAED,GAAG,KAAK;;;;;;;AAGb,uBAAuB,WAAW,GAChC,sRAAA,CAAA,aAAgC,CAAC,WAAW;AAE9C,MAAM,oCAAsB,CAAA,GAAA,sQAAA,CAAA,aAAgB,AAAD,QAGzC,CAAC,EAAE,SAAS,EAAE,aAAa,CAAC,EAAE,GAAG,OAAO,EAAE,oBAC1C,sSAAC,sRAAA,CAAA,SAA4B;kBAC3B,cAAA,sSAAC,sRAAA,CAAA,UAA6B;YAC5B,KAAK;YACL,YAAY;YACZ,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,ybACA;YAED,GAAG,KAAK;;;;;;;;;;;;AAIf,oBAAoB,WAAW,GAAG,sRAAA,CAAA,UAA6B,CAAC,WAAW;AAE3E,MAAM,iCAAmB,CAAA,GAAA,sQAAA,CAAA,aAAgB,AAAD,QAKtC,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,OAAO,EAAE,oBACjC,sSAAC,sRAAA,CAAA,OAA0B;QACzB,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,mOACA,SAAS,QACT;QAED,GAAG,KAAK;;;;;;;AAGb,iBAAiB,WAAW,GAAG,sRAAA,CAAA,OAA0B,CAAC,WAAW;AAErE,MAAM,yCAA2B,CAAA,GAAA,sQAAA,CAAA,aAAgB,AAAD,QAG9C,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG,OAAO,EAAE,oBAC7C,sSAAC,sRAAA,CAAA,eAAkC;QACjC,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,wOACA;QAEF,SAAS;QACR,GAAG,KAAK;;0BAET,sSAAC;gBAAK,WAAU;0BACd,cAAA,sSAAC,sRAAA,CAAA,gBAAmC;8BAClC,cAAA,sSAAC,2RAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGpB;;;;;;;;AAGL,yBAAyB,WAAW,GAClC,sRAAA,CAAA,eAAkC,CAAC,WAAW;AAEhD,MAAM,sCAAwB,CAAA,GAAA,sQAAA,CAAA,aAAgB,AAAD,SAG3C,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,sSAAC,sRAAA,CAAA,YAA+B;QAC9B,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,wOACA;QAED,GAAG,KAAK;;0BAET,sSAAC;gBAAK,WAAU;0BACd,cAAA,sSAAC,sRAAA,CAAA,gBAAmC;8BAClC,cAAA,sSAAC,6RAAA,CAAA,SAAM;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGrB;;;;;;;;AAGL,sBAAsB,WAAW,GAAG,sRAAA,CAAA,YAA+B,CAAC,WAAW;AAE/E,MAAM,kCAAoB,CAAA,GAAA,sQAAA,CAAA,aAAgB,AAAD,SAKvC,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,OAAO,EAAE,oBACjC,sSAAC,sRAAA,CAAA,QAA2B;QAC1B,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,qCACA,SAAS,QACT;QAED,GAAG,KAAK;;;;;;;AAGb,kBAAkB,WAAW,GAAG,sRAAA,CAAA,QAA2B,CAAC,WAAW;AAEvE,MAAM,sCAAwB,CAAA,GAAA,sQAAA,CAAA,aAAgB,AAAD,SAG3C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,sSAAC,sRAAA,CAAA,YAA+B;QAC9B,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;;;;;;AAGb,sBAAsB,WAAW,GAAG,sRAAA,CAAA,YAA+B,CAAC,WAAW;AAE/E,MAAM,uBAAuB,CAAC,EAC5B,SAAS,EACT,GAAG,OACmC;IACtC,qBACE,sSAAC;QACC,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,8CAA8C;QAC3D,GAAG,KAAK;;;;;;AAGf;OAVM;AAWN,qBAAqB,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 318, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/lib/ai/dev-models.ts"], "sourcesContent": ["// Define your models here.\r\n\r\nexport interface Model {\r\n  id: string;\r\n  label: string;\r\n  apiIdentifier: string; // Dify Application ID\r\n  description: string;\r\n  apiKey?: string;\r\n}\r\n\r\nexport const models: Array<Model> = [\r\n  {\r\n    id: '지자체 공간정보 플랫폼 챗봇',\r\n    label: '지자체 공간정보 플랫폼 챗봇',\r\n    apiIdentifier: 'EIjFYMz0dmL2HxkQJuBifqvF', // Dify Application ID (app- 제거)\r\n    description: '지자체 공간정보 플랫폼 챗봇',\r\n    apiKey: 'app-Hd682MZtRJh95QtTUe5H9aCl', // Dify Assistant API 키\r\n  },\r\n  {\r\n    id: '지도개발 어시스턴트',\r\n    label: '지도개발 어시스턴트',\r\n    apiIdentifier: 'EIjFYMz0dmL2HxkQJuBifqvF', // Dify Application ID (app- 제거)\r\n    description: '지도개발을 위한 문서를 학습한 어시스턴트',\r\n    apiKey: 'app-EIjFYMz0dmL2HxkQJuBifqvF', // Dify Assistant API 키\r\n  },\r\n] as const;\r\n\r\nexport const DEFAULT_MODEL_NAME: string = '지자체 공간정보 플랫폼 챗봇';\r\n\r\n// API 키를 모델 ID로 매핑하는 함수\r\nexport function getApiKeyByModelId(modelId: string): string | undefined {\r\n  const model = models.find(m => m.id === modelId);\r\n  return model?.apiKey;\r\n}\r\n"], "names": [], "mappings": "AAAA,2BAA2B;;;;;;AAUpB,MAAM,SAAuB;IAClC;QACE,IAAI;QACJ,OAAO;QACP,eAAe;QACf,aAAa;QACb,QAAQ;IACV;IACA;QACE,IAAI;QACJ,OAAO;QACP,eAAe;QACf,aAAa;QACb,QAAQ;IACV;CACD;AAEM,MAAM,qBAA6B;AAGnC,SAAS,mBAAmB,OAAe;IAChD,MAAM,QAAQ,OAAO,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;IACxC,OAAO,OAAO;AAChB", "debugId": null}}, {"offset": {"line": 354, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/components/model-selector.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { startTransition, useMemo, useOptimistic, useState } from 'react';\r\n\r\nimport { saveDevModelId } from '@/app/(map)/actions';\r\nimport { Button } from '@/components/ui/button';\r\nimport {\r\n  DropdownMenu,\r\n  DropdownMenuContent,\r\n  DropdownMenuItem,\r\n  DropdownMenuTrigger,\r\n} from '@/components/ui/dropdown-menu';\r\nimport { models } from '@/lib/ai/dev-models';\r\nimport { cn } from '@/lib/utils';\r\n\r\nimport { CheckCircle2Icon, ChevronDownIcon } from 'lucide-react';\r\n\r\nexport function ModelSelector({\r\n  selectedModelId,\r\n  className,\r\n}: {\r\n  selectedModelId: string;\r\n} & React.ComponentProps<typeof Button>) {\r\n  const [open, setOpen] = useState(false);\r\n  const [optimisticModelId, setOptimisticModelId] =\r\n    useOptimistic(selectedModelId);\r\n\r\n  const selectedModel = useMemo(\r\n    () => models.find((model) => model.id === optimisticModelId),\r\n    [optimisticModelId],\r\n  );\r\n\r\n  return (\r\n    <DropdownMenu open={open} onOpenChange={setOpen}>\r\n      <DropdownMenuTrigger\r\n        asChild\r\n        className={cn(\r\n          'w-fit data-[state=open]:bg-accent data-[state=open]:text-accent-foreground',\r\n          className,\r\n        )}\r\n      >\r\n        <Button variant=\"outline\" className=\"md:px-2 md:h-[34px]\">\r\n          {selectedModel?.label}\r\n          <ChevronDownIcon />\r\n        </Button>\r\n      </DropdownMenuTrigger>\r\n      <DropdownMenuContent align=\"start\" className=\"min-w-[300px]\">\r\n        {models.map((model) => (\r\n          <DropdownMenuItem\r\n            key={model.id}\r\n            onSelect={() => {\r\n              setOpen(false);\r\n\r\n              startTransition(() => {\r\n                setOptimisticModelId(model.id);\r\n                saveDevModelId(model.id);\r\n              });\r\n            }}\r\n            className=\"gap-4 group/item flex flex-row justify-between items-center\"\r\n            data-active={model.id === optimisticModelId}\r\n          >\r\n            <div className=\"flex flex-col gap-1 items-start\">\r\n              {model.label}\r\n              {model.description && (\r\n                <div className=\"text-xs text-muted-foreground\">\r\n                  {model.description}\r\n                </div>\r\n              )}\r\n            </div>\r\n            <div className=\"text-primary dark:text-primary-foreground opacity-0 group-data-[active=true]/item:opacity-100\">\r\n              <CheckCircle2Icon />\r\n            </div>\r\n          </DropdownMenuItem>\r\n        ))}\r\n      </DropdownMenuContent>\r\n    </DropdownMenu>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AACA;AACA;AAMA;AACA;AAEA;AAAA;;;AAfA;;;;;;;;AAiBO,SAAS,cAAc,EAC5B,eAAe,EACf,SAAS,EAG4B;;IACrC,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,CAAC,mBAAmB,qBAAqB,GAC7C,CAAA,GAAA,sQAAA,CAAA,gBAAa,AAAD,EAAE;IAEhB,MAAM,gBAAgB,CAAA,GAAA,sQAAA,CAAA,UAAO,AAAD;gDAC1B,IAAM,6HAAA,CAAA,SAAM,CAAC,IAAI;wDAAC,CAAC,QAAU,MAAM,EAAE,KAAK;;+CAC1C;QAAC;KAAkB;IAGrB,qBACE,sSAAC,wIAAA,CAAA,eAAY;QAAC,MAAM;QAAM,cAAc;;0BACtC,sSAAC,wIAAA,CAAA,sBAAmB;gBAClB,OAAO;gBACP,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,8EACA;0BAGF,cAAA,sSAAC,8HAAA,CAAA,SAAM;oBAAC,SAAQ;oBAAU,WAAU;;wBACjC,eAAe;sCAChB,sSAAC,+SAAA,CAAA,kBAAe;;;;;;;;;;;;;;;;0BAGpB,sSAAC,wIAAA,CAAA,sBAAmB;gBAAC,OAAM;gBAAQ,WAAU;0BAC1C,6HAAA,CAAA,SAAM,CAAC,GAAG,CAAC,CAAC,sBACX,sSAAC,wIAAA,CAAA,mBAAgB;wBAEf,UAAU;4BACR,QAAQ;4BAER,CAAA,GAAA,sQAAA,CAAA,kBAAe,AAAD,EAAE;gCACd,qBAAqB,MAAM,EAAE;gCAC7B,CAAA,GAAA,yJAAA,CAAA,iBAAc,AAAD,EAAE,MAAM,EAAE;4BACzB;wBACF;wBACA,WAAU;wBACV,eAAa,MAAM,EAAE,KAAK;;0CAE1B,sSAAC;gCAAI,WAAU;;oCACZ,MAAM,KAAK;oCACX,MAAM,WAAW,kBAChB,sSAAC;wCAAI,WAAU;kDACZ,MAAM,WAAW;;;;;;;;;;;;0CAIxB,sSAAC;gCAAI,WAAU;0CACb,cAAA,sSAAC,gTAAA,CAAA,mBAAgB;;;;;;;;;;;uBArBd,MAAM,EAAE;;;;;;;;;;;;;;;;AA4BzB;GA5DgB;;QAQZ,sQAAA,CAAA,gBAAa;;;KARD", "debugId": null}}, {"offset": {"line": 494, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/components/ui/icons.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport * as React from 'react'\r\n\r\nimport { cn } from '@/lib/utils'\r\n\r\nfunction IconNextChat({\r\n  className,\r\n  inverted,\r\n  ...props\r\n}: React.ComponentProps<'svg'> & { inverted?: boolean }) {\r\n  const id = React.useId()\r\n\r\n  return (\r\n    <svg\r\n      viewBox=\"0 0 17 17\"\r\n      fill=\"none\"\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n      className={cn('size-4', className)}\r\n      {...props}\r\n    >\r\n      <defs>\r\n        <linearGradient\r\n          id={`gradient-${id}-1`}\r\n          x1=\"10.6889\"\r\n          y1=\"10.3556\"\r\n          x2=\"13.8445\"\r\n          y2=\"14.2667\"\r\n          gradientUnits=\"userSpaceOnUse\"\r\n        >\r\n          <stop stopColor={inverted ? 'white' : 'black'} />\r\n          <stop\r\n            offset={1}\r\n            stopColor={inverted ? 'white' : 'black'}\r\n            stopOpacity={0}\r\n          />\r\n        </linearGradient>\r\n        <linearGradient\r\n          id={`gradient-${id}-2`}\r\n          x1=\"11.7555\"\r\n          y1=\"4.8\"\r\n          x2=\"11.7376\"\r\n          y2=\"9.50002\"\r\n          gradientUnits=\"userSpaceOnUse\"\r\n        >\r\n          <stop stopColor={inverted ? 'white' : 'black'} />\r\n          <stop\r\n            offset={1}\r\n            stopColor={inverted ? 'white' : 'black'}\r\n            stopOpacity={0}\r\n          />\r\n        </linearGradient>\r\n      </defs>\r\n      <path\r\n        d=\"M1 16L2.58314 11.2506C1.83084 9.74642 1.63835 8.02363 2.04013 6.39052C2.4419 4.75741 3.41171 3.32057 4.776 2.33712C6.1403 1.35367 7.81003 0.887808 9.4864 1.02289C11.1628 1.15798 12.7364 1.8852 13.9256 3.07442C15.1148 4.26363 15.842 5.83723 15.9771 7.5136C16.1122 9.18997 15.6463 10.8597 14.6629 12.224C13.6794 13.5883 12.2426 14.5581 10.6095 14.9599C8.97637 15.3616 7.25358 15.1692 5.74942 14.4169L1 16Z\"\r\n        fill={inverted ? 'black' : 'white'}\r\n        stroke={inverted ? 'black' : 'white'}\r\n        strokeWidth={2}\r\n        strokeLinecap=\"round\"\r\n        strokeLinejoin=\"round\"\r\n      />\r\n      <mask\r\n        id=\"mask0_91_2047\"\r\n        style={{ maskType: 'alpha' }}\r\n        maskUnits=\"userSpaceOnUse\"\r\n        x={1}\r\n        y={0}\r\n        width={16}\r\n        height={16}\r\n      >\r\n        <circle cx={9} cy={8} r={8} fill={inverted ? 'black' : 'white'} />\r\n      </mask>\r\n      <g mask=\"url(#mask0_91_2047)\">\r\n        <circle cx={9} cy={8} r={8} fill={inverted ? 'black' : 'white'} />\r\n        <path\r\n          d=\"M14.2896 14.0018L7.146 4.8H5.80005V11.1973H6.87681V6.16743L13.4444 14.6529C13.7407 14.4545 14.0231 14.2369 14.2896 14.0018Z\"\r\n          fill={`url(#gradient-${id}-1)`}\r\n        />\r\n        <rect\r\n          x=\"11.2222\"\r\n          y=\"4.8\"\r\n          width=\"1.06667\"\r\n          height=\"6.4\"\r\n          fill={`url(#gradient-${id}-2)`}\r\n        />\r\n      </g>\r\n    </svg>\r\n  )\r\n}\r\n\r\nfunction IconOpenAI({ className, ...props }: React.ComponentProps<'svg'>) {\r\n  return (\r\n    <svg\r\n      fill=\"currentColor\"\r\n      viewBox=\"0 0 24 24\"\r\n      role=\"img\"\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n      className={cn('size-4', className)}\r\n      {...props}\r\n    >\r\n      <title>OpenAI icon</title>\r\n      <path d=\"M22.2819 9.8211a5.9847 5.9847 0 0 0-.5157-4.9108 6.0462 6.0462 0 0 0-6.5098-2.9A6.0651 6.0651 0 0 0 4.9807 4.1818a5.9847 5.9847 0 0 0-3.9977 2.9 6.0462 6.0462 0 0 0 .7427 7.0966 5.98 5.98 0 0 0 .511 4.9107 6.051 6.051 0 0 0 6.5146 2.9001A5.9847 5.9847 0 0 0 13.2599 24a6.0557 6.0557 0 0 0 5.7718-4.2058 5.9894 5.9894 0 0 0 3.9977-2.9001 6.0557 6.0557 0 0 0-.7475-7.0729zm-9.022 12.6081a4.4755 4.4755 0 0 1-2.8764-1.0408l.1419-.0804 4.7783-2.7582a.7948.7948 0 0 0 .3927-.6813v-6.7369l2.02 1.1686a.071.071 0 0 1 .038.052v5.5826a4.504 4.504 0 0 1-4.4945 4.4944zm-9.6607-4.1254a4.4708 4.4708 0 0 1-.5346-3.0137l.142.0852 4.783 2.7582a.7712.7712 0 0 0 .7806 0l5.8428-3.3685v2.3324a.0804.0804 0 0 1-.0332.0615L9.74 19.9502a4.4992 4.4992 0 0 1-6.1408-1.6464zM2.3408 7.8956a4.485 4.485 0 0 1 2.3655-1.9728V11.6a.7664.7664 0 0 0 .3879.6765l5.8144 3.3543-2.0201 1.1685a.0757.0757 0 0 1-.071 0l-4.8303-2.7865A4.504 4.504 0 0 1 2.3408 7.872zm16.5963 3.8558L13.1038 8.364 15.1192 7.2a.0757.0757 0 0 1 .071 0l4.8303 2.7913a4.4944 4.4944 0 0 1-.6765 8.1042v-5.6772a.79.79 0 0 0-.407-.667zm2.0107-3.0231l-.142-.0852-4.7735-2.7818a.7759.7759 0 0 0-.7854 0L9.409 9.2297V6.8974a.0662.0662 0 0 1 .0284-.0615l4.8303-2.7866a4.4992 4.4992 0 0 1 6.6802 4.66zM8.3065 12.863l-2.02-1.1638a.0804.0804 0 0 1-.038-.0567V6.0742a4.4992 4.4992 0 0 1 7.3757-3.4537l-.142.0805L8.704 5.459a.7948.7948 0 0 0-.3927.6813zm1.0976-2.3654l2.602-1.4998 2.6069 1.4998v2.9994l-2.5974 1.4997-2.6067-1.4997Z\" />\r\n    </svg>\r\n  )\r\n}\r\n\r\nfunction IconVercel({ className, ...props }: React.ComponentProps<'svg'>) {\r\n  return (\r\n    <svg\r\n      aria-label=\"Vercel logomark\"\r\n      role=\"img\"\r\n      viewBox=\"0 0 74 64\"\r\n      className={cn('size-4', className)}\r\n      {...props}\r\n    >\r\n      <path\r\n        d=\"M37.5896 0.25L74.5396 64.25H0.639648L37.5896 0.25Z\"\r\n        fill=\"currentColor\"\r\n      ></path>\r\n    </svg>\r\n  )\r\n}\r\n\r\nfunction IconGitHub({ className, ...props }: React.ComponentProps<'svg'>) {\r\n  return (\r\n    <svg\r\n      role=\"img\"\r\n      viewBox=\"0 0 24 24\"\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n      fill=\"currentColor\"\r\n      className={cn('size-4', className)}\r\n      {...props}\r\n    >\r\n      <title>GitHub</title>\r\n      <path d=\"M12 .297c-6.63 0-12 5.373-12 12 0 5.303 3.438 9.8 8.205 11.385.6.113.82-.258.82-.577 0-.285-.01-1.04-.015-2.04-3.338.724-4.042-1.61-4.042-1.61C4.422 18.07 3.633 17.7 3.633 17.7c-1.087-.744.084-.729.084-.729 1.205.084 1.838 1.236 1.838 1.236 1.07 1.835 2.809 1.305 3.495.998.108-.776.417-1.305.76-1.605-2.665-.3-5.466-1.332-5.466-5.93 0-1.31.465-2.38 1.235-3.22-.135-.303-.54-1.523.105-3.176 0 0 1.005-.322 3.3 1.23.96-.267 1.98-.399 3-.405 1.02.006 2.04.138 3 .405 2.28-1.552 3.285-1.23 3.285-1.23.645 1.653.24 2.873.12 3.176.765.84 1.23 1.91 1.23 3.22 0 4.61-2.805 5.625-5.475 5.92.42.36.81 1.096.81 2.22 0 1.606-.015 2.896-.015 3.286 0 .315.21.69.825.57C20.565 22.092 24 17.592 24 12.297c0-6.627-5.373-12-12-12\" />\r\n    </svg>\r\n  )\r\n}\r\n\r\nfunction IconSeparator({ className, ...props }: React.ComponentProps<'svg'>) {\r\n  return (\r\n    <svg\r\n      fill=\"none\"\r\n      shapeRendering=\"geometricPrecision\"\r\n      stroke=\"currentColor\"\r\n      strokeLinecap=\"round\"\r\n      strokeLinejoin=\"round\"\r\n      strokeWidth=\"1\"\r\n      viewBox=\"0 0 24 24\"\r\n      aria-hidden=\"true\"\r\n      className={cn('size-4', className)}\r\n      {...props}\r\n    >\r\n      <path d=\"M16.88 3.549L7.12 20.451\"></path>\r\n    </svg>\r\n  )\r\n}\r\n\r\nfunction IconArrowDown({ className, ...props }: React.ComponentProps<'svg'>) {\r\n  return (\r\n    <svg\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n      viewBox=\"0 0 256 256\"\r\n      fill=\"currentColor\"\r\n      className={cn('size-4', className)}\r\n      {...props}\r\n    >\r\n      <path d=\"m205.66 149.66-72 72a8 8 0 0 1-11.32 0l-72-72a8 8 0 0 1 11.32-11.32L120 196.69V40a8 8 0 0 1 16 0v156.69l58.34-58.35a8 8 0 0 1 11.32 11.32Z\" />\r\n    </svg>\r\n  )\r\n}\r\n\r\nfunction IconArrowRight({ className, ...props }: React.ComponentProps<'svg'>) {\r\n  return (\r\n    <svg\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n      viewBox=\"0 0 256 256\"\r\n      fill=\"currentColor\"\r\n      className={cn('size-4', className)}\r\n      {...props}\r\n    >\r\n      <path d=\"m221.66 133.66-72 72a8 8 0 0 1-11.32-11.32L196.69 136H40a8 8 0 0 1 0-16h156.69l-58.35-58.34a8 8 0 0 1 11.32-11.32l72 72a8 8 0 0 1 0 11.32Z\" />\r\n    </svg>\r\n  )\r\n}\r\n\r\nfunction IconUser({ className, ...props }: React.ComponentProps<'svg'>) {\r\n  return (\r\n    <svg\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n      viewBox=\"0 0 256 256\"\r\n      fill=\"currentColor\"\r\n      className={cn('size-4', className)}\r\n      {...props}\r\n    >\r\n      <path d=\"M230.92 212c-15.23-26.33-38.7-45.21-66.09-54.16a72 72 0 1 0-73.66 0c-27.39 8.94-50.86 27.82-66.09 54.16a8 8 0 1 0 13.85 8c18.84-32.56 52.14-52 89.07-52s70.23 19.44 89.07 52a8 8 0 1 0 13.85-8ZM72 96a56 56 0 1 1 56 56 56.06 56.06 0 0 1-56-56Z\" />\r\n    </svg>\r\n  )\r\n}\r\n\r\nfunction IconPlus({ className, ...props }: React.ComponentProps<'svg'>) {\r\n  return (\r\n    <svg\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n      viewBox=\"0 0 256 256\"\r\n      fill=\"currentColor\"\r\n      className={cn('size-4', className)}\r\n      {...props}\r\n    >\r\n      <path d=\"M224 128a8 8 0 0 1-8 8h-80v80a8 8 0 0 1-16 0v-80H40a8 8 0 0 1 0-16h80V40a8 8 0 0 1 16 0v80h80a8 8 0 0 1 8 8Z\" />\r\n    </svg>\r\n  )\r\n}\r\n\r\nfunction IconArrowElbow({ className, ...props }: React.ComponentProps<'svg'>) {\r\n  return (\r\n    <svg\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n      viewBox=\"0 0 256 256\"\r\n      fill=\"currentColor\"\r\n      className={cn('size-4', className)}\r\n      {...props}\r\n    >\r\n      <path d=\"M200 32v144a8 8 0 0 1-8 8H67.31l34.35 34.34a8 8 0 0 1-11.32 11.32l-48-48a8 8 0 0 1 0-11.32l48-48a8 8 0 0 1 11.32 11.32L67.31 168H184V32a8 8 0 0 1 16 0Z\" />\r\n    </svg>\r\n  )\r\n}\r\n\r\nfunction IconSpinner({ className, ...props }: React.ComponentProps<'svg'>) {\r\n  return (\r\n    <svg\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n      viewBox=\"0 0 256 256\"\r\n      fill=\"currentColor\"\r\n      className={cn('size-4 animate-spin', className)}\r\n      {...props}\r\n    >\r\n      <path d=\"M232 128a104 104 0 0 1-208 0c0-41 23.81-78.36 60.66-95.27a8 8 0 0 1 6.68 14.54C60.15 61.59 40 93.27 40 128a88 88 0 0 0 176 0c0-34.73-20.15-66.41-51.34-80.73a8 8 0 0 1 6.68-14.54C208.19 49.64 232 87 232 128Z\" />\r\n    </svg>\r\n  )\r\n}\r\n\r\nfunction IconMessage({ className, ...props }: React.ComponentProps<'svg'>) {\r\n  return (\r\n    <svg\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n      viewBox=\"0 0 256 256\"\r\n      fill=\"currentColor\"\r\n      className={cn('size-4', className)}\r\n      {...props}\r\n    >\r\n      <path d=\"M216 48H40a16 16 0 0 0-16 16v160a15.84 15.84 0 0 0 9.25 14.5A16.05 16.05 0 0 0 40 240a15.89 15.89 0 0 0 10.25-3.78.69.69 0 0 0 .13-.11L82.5 208H216a16 16 0 0 0 16-16V64a16 16 0 0 0-16-16ZM40 224Zm176-32H82.5a16 16 0 0 0-10.3 3.75l-.12.11L40 224V64h176Z\" />\r\n    </svg>\r\n  )\r\n}\r\n\r\nfunction IconTrash({ className, ...props }: React.ComponentProps<'svg'>) {\r\n  return (\r\n    <svg\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n      viewBox=\"0 0 256 256\"\r\n      fill=\"currentColor\"\r\n      className={cn('size-4', className)}\r\n      {...props}\r\n    >\r\n      <path d=\"M216 48h-40v-8a24 24 0 0 0-24-24h-48a24 24 0 0 0-24 24v8H40a8 8 0 0 0 0 16h8v144a16 16 0 0 0 16 16h128a16 16 0 0 0 16-16V64h8a8 8 0 0 0 0-16ZM96 40a8 8 0 0 1 8-8h48a8 8 0 0 1 8 8v8H96Zm96 168H64V64h128Zm-80-104v64a8 8 0 0 1-16 0v-64a8 8 0 0 1 16 0Zm48 0v64a8 8 0 0 1-16 0v-64a8 8 0 0 1 16 0Z\" />\r\n    </svg>\r\n  )\r\n}\r\n\r\nfunction IconRefresh({ className, ...props }: React.ComponentProps<'svg'>) {\r\n  return (\r\n    <svg\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n      viewBox=\"0 0 256 256\"\r\n      fill=\"currentColor\"\r\n      className={cn('size-4', className)}\r\n      {...props}\r\n    >\r\n      <path d=\"M197.67 186.37a8 8 0 0 1 0 11.29C196.58 198.73 170.82 224 128 224c-37.39 0-64.53-22.4-80-39.85V208a8 8 0 0 1-16 0v-48a8 8 0 0 1 8-8h48a8 8 0 0 1 0 16H55.44C67.76 183.35 93 208 128 208c36 0 58.14-21.46 58.36-21.68a8 8 0 0 1 11.31.05ZM216 40a8 8 0 0 0-8 8v23.85C192.53 54.4 165.39 32 128 32c-42.82 0-68.58 25.27-69.66 26.34a8 8 0 0 0 11.3 11.34C69.86 69.46 92 48 128 48c35 0 60.24 24.65 72.56 40H168a8 8 0 0 0 0 16h48a8 8 0 0 0 8-8V48a8 8 0 0 0-8-8Z\" />\r\n    </svg>\r\n  )\r\n}\r\n\r\nfunction IconStop({ className, ...props }: React.ComponentProps<'svg'>) {\r\n  return (\r\n    <svg\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n      viewBox=\"0 0 256 256\"\r\n      fill=\"currentColor\"\r\n      className={cn('size-4', className)}\r\n      {...props}\r\n    >\r\n      <path d=\"M128 24a104 104 0 1 0 104 104A104.11 104.11 0 0 0 128 24Zm0 192a88 88 0 1 1 88-88 88.1 88.1 0 0 1-88 88Zm24-120h-48a8 8 0 0 0-8 8v48a8 8 0 0 0 8 8h48a8 8 0 0 0 8-8v-48a8 8 0 0 0-8-8Zm-8 48h-32v-32h32Z\" />\r\n    </svg>\r\n  )\r\n}\r\n\r\nfunction IconSidebar({ className, ...props }: React.ComponentProps<'svg'>) {\r\n  return (\r\n    <svg\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n      viewBox=\"0 0 256 256\"\r\n      fill=\"currentColor\"\r\n      className={cn('size-4', className)}\r\n      {...props}\r\n    >\r\n      <path d=\"M216 40H40a16 16 0 0 0-16 16v144a16 16 0 0 0 16 16h176a16 16 0 0 0 16-16V56a16 16 0 0 0-16-16ZM40 56h40v144H40Zm176 144H96V56h120v144Z\" />\r\n    </svg>\r\n  )\r\n}\r\n\r\nfunction IconMoon({ className, ...props }: React.ComponentProps<'svg'>) {\r\n  return (\r\n    <svg\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n      viewBox=\"0 0 256 256\"\r\n      fill=\"currentColor\"\r\n      className={cn('size-4', className)}\r\n      {...props}\r\n    >\r\n      <path d=\"M233.54 142.23a8 8 0 0 0-8-2 88.08 88.08 0 0 1-109.8-109.8 8 8 0 0 0-10-10 104.84 104.84 0 0 0-52.91 37A104 104 0 0 0 136 224a103.09 103.09 0 0 0 62.52-20.88 104.84 104.84 0 0 0 37-52.91 8 8 0 0 0-1.98-7.98Zm-44.64 48.11A88 88 0 0 1 65.66 67.11a89 89 0 0 1 31.4-26A106 106 0 0 0 96 56a104.11 104.11 0 0 0 104 104 106 106 0 0 0 14.92-1.06 89 89 0 0 1-26.02 31.4Z\" />\r\n    </svg>\r\n  )\r\n}\r\n\r\nfunction IconSun({ className, ...props }: React.ComponentProps<'svg'>) {\r\n  return (\r\n    <svg\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n      viewBox=\"0 0 256 256\"\r\n      fill=\"currentColor\"\r\n      className={cn('size-4', className)}\r\n      {...props}\r\n    >\r\n      <path d=\"M120 40V16a8 8 0 0 1 16 0v24a8 8 0 0 1-16 0Zm72 88a64 64 0 1 1-64-64 64.07 64.07 0 0 1 64 64Zm-16 0a48 48 0 1 0-48 48 48.05 48.05 0 0 0 48-48ZM58.34 69.66a8 8 0 0 0 11.32-11.32l-16-16a8 8 0 0 0-11.32 11.32Zm0 116.68-16 16a8 8 0 0 0 11.32 11.32l16-16a8 8 0 0 0-11.32-11.32ZM192 72a8 8 0 0 0 5.66-2.34l16-16a8 8 0 0 0-11.32-11.32l-16 16A8 8 0 0 0 192 72Zm5.66 114.34a8 8 0 0 0-11.32 11.32l16 16a8 8 0 0 0 11.32-11.32ZM48 128a8 8 0 0 0-8-8H16a8 8 0 0 0 0 16h24a8 8 0 0 0 8-8Zm80 80a8 8 0 0 0-8 8v24a8 8 0 0 0 16 0v-24a8 8 0 0 0-8-8Zm112-88h-24a8 8 0 0 0 0 16h24a8 8 0 0 0 0-16Z\" />\r\n    </svg>\r\n  )\r\n}\r\n\r\nfunction IconCopy({ className, ...props }: React.ComponentProps<'svg'>) {\r\n  return (\r\n    <svg\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n      viewBox=\"0 0 256 256\"\r\n      fill=\"currentColor\"\r\n      className={cn('size-4', className)}\r\n      {...props}\r\n    >\r\n      <path d=\"M216 32H88a8 8 0 0 0-8 8v40H40a8 8 0 0 0-8 8v128a8 8 0 0 0 8 8h128a8 8 0 0 0 8-8v-40h40a8 8 0 0 0 8-8V40a8 8 0 0 0-8-8Zm-56 176H48V96h112Zm48-48h-32V88a8 8 0 0 0-8-8H96V48h112Z\" />\r\n    </svg>\r\n  )\r\n}\r\n\r\nfunction IconCheck({ className, ...props }: React.ComponentProps<'svg'>) {\r\n  return (\r\n    <svg\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n      viewBox=\"0 0 256 256\"\r\n      fill=\"currentColor\"\r\n      className={cn('size-4', className)}\r\n      {...props}\r\n    >\r\n      <path d=\"m229.66 77.66-128 128a8 8 0 0 1-11.32 0l-56-56a8 8 0 0 1 11.32-11.32L96 188.69 218.34 66.34a8 8 0 0 1 11.32 11.32Z\" />\r\n    </svg>\r\n  )\r\n}\r\n\r\nfunction IconDownload({ className, ...props }: React.ComponentProps<'svg'>) {\r\n  return (\r\n    <svg\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n      viewBox=\"0 0 256 256\"\r\n      fill=\"currentColor\"\r\n      className={cn('size-4', className)}\r\n      {...props}\r\n    >\r\n      <path d=\"M224 152v56a16 16 0 0 1-16 16H48a16 16 0 0 1-16-16v-56a8 8 0 0 1 16 0v56h160v-56a8 8 0 0 1 16 0Zm-101.66 5.66a8 8 0 0 0 11.32 0l40-40a8 8 0 0 0-11.32-11.32L136 132.69V40a8 8 0 0 0-16 0v92.69l-26.34-26.35a8 8 0 0 0-11.32 11.32Z\" />\r\n    </svg>\r\n  )\r\n}\r\n\r\nfunction IconClose({ className, ...props }: React.ComponentProps<'svg'>) {\r\n  return (\r\n    <svg\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n      viewBox=\"0 0 256 256\"\r\n      fill=\"currentColor\"\r\n      className={cn('size-4', className)}\r\n      {...props}\r\n    >\r\n      <path d=\"M205.66 194.34a8 8 0 0 1-11.32 11.32L128 139.31l-66.34 66.35a8 8 0 0 1-11.32-11.32L116.69 128 50.34 61.66a8 8 0 0 1 11.32-11.32L128 116.69l66.34-66.35a8 8 0 0 1 11.32 11.32L139.31 128Z\" />\r\n    </svg>\r\n  )\r\n}\r\n\r\nfunction IconEdit({ className, ...props }: React.ComponentProps<'svg'>) {\r\n  return (\r\n    <svg\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n      fill=\"none\"\r\n      viewBox=\"0 0 24 24\"\r\n      strokeWidth={1.5}\r\n      stroke=\"currentColor\"\r\n      className={cn('size-4', className)}\r\n      {...props}\r\n    >\r\n      <path\r\n        strokeLinecap=\"round\"\r\n        strokeLinejoin=\"round\"\r\n        d=\"M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L10.582 16.07a4.5 4.5 0 01-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 011.13-1.897l8.932-8.931zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0115.75 21H5.25A2.25 2.25 0 013 18.75V8.25A2.25 2.25 0 015.25 6H10\"\r\n      />\r\n    </svg>\r\n  )\r\n}\r\n\r\nfunction IconShare({ className, ...props }: React.ComponentProps<'svg'>) {\r\n  return (\r\n    <svg\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n      fill=\"currentColor\"\r\n      className={cn('size-4', className)}\r\n      viewBox=\"0 0 256 256\"\r\n      {...props}\r\n    >\r\n      <path d=\"m237.66 106.35-80-80A8 8 0 0 0 144 32v40.35c-25.94 2.22-54.59 14.92-78.16 34.91-28.38 24.08-46.05 55.11-49.76 87.37a12 12 0 0 0 20.68 9.58c11-11.71 50.14-48.74 107.24-52V192a8 8 0 0 0 13.66 5.65l80-80a8 8 0 0 0 0-11.3ZM160 172.69V144a8 8 0 0 0-8-8c-28.08 0-55.43 7.33-81.29 21.8a196.17 196.17 0 0 0-36.57 26.52c5.8-23.84 20.42-46.51 42.05-64.86C99.41 99.77 127.75 88 152 88a8 8 0 0 0 8-8V51.32L220.69 112Z\" />\r\n    </svg>\r\n  )\r\n}\r\n\r\nfunction IconUsers({ className, ...props }: React.ComponentProps<'svg'>) {\r\n  return (\r\n    <svg\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n      fill=\"currentColor\"\r\n      className={cn('size-4', className)}\r\n      viewBox=\"0 0 256 256\"\r\n      {...props}\r\n    >\r\n      <path d=\"M117.25 157.92a60 60 0 1 0-66.5 0 95.83 95.83 0 0 0-47.22 37.71 8 8 0 1 0 13.4 8.74 80 80 0 0 1 134.14 0 8 8 0 0 0 13.4-8.74 95.83 95.83 0 0 0-47.22-37.71ZM40 108a44 44 0 1 1 44 44 44.05 44.05 0 0 1-44-44Zm210.14 98.7a8 8 0 0 1-11.07-2.33A79.83 79.83 0 0 0 172 168a8 8 0 0 1 0-16 44 44 0 1 0-16.34-84.87 8 8 0 1 1-5.94-14.85 60 60 0 0 1 55.53 105.64 95.83 95.83 0 0 1 47.22 37.71 8 8 0 0 1-2.33 11.07Z\" />\r\n    </svg>\r\n  )\r\n}\r\n\r\nfunction IconExternalLink({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<'svg'>) {\r\n  return (\r\n    <svg\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n      fill=\"currentColor\"\r\n      className={cn('size-4', className)}\r\n      viewBox=\"0 0 256 256\"\r\n      {...props}\r\n    >\r\n      <path d=\"M224 104a8 8 0 0 1-16 0V59.32l-66.33 66.34a8 8 0 0 1-11.32-11.32L196.68 48H152a8 8 0 0 1 0-16h64a8 8 0 0 1 8 8Zm-40 24a8 8 0 0 0-8 8v72H48V80h72a8 8 0 0 0 0-16H48a16 16 0 0 0-16 16v128a16 16 0 0 0 16 16h128a16 16 0 0 0 16-16v-72a8 8 0 0 0-8-8Z\" />\r\n    </svg>\r\n  )\r\n}\r\n\r\nfunction IconChevronUpDown({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<'svg'>) {\r\n  return (\r\n    <svg\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n      fill=\"currentColor\"\r\n      className={cn('size-4', className)}\r\n      viewBox=\"0 0 256 256\"\r\n      {...props}\r\n    >\r\n      <path d=\"M181.66 170.34a8 8 0 0 1 0 11.32l-48 48a8 8 0 0 1-11.32 0l-48-48a8 8 0 0 1 11.32-11.32L128 212.69l42.34-42.35a8 8 0 0 1 11.32 0Zm-96-84.68L128 43.31l42.34 42.35a8 8 0 0 0 11.32-11.32l-48-48a8 8 0 0 0-11.32 0l-48 48a8 8 0 0 0 11.32 11.32Z\" />\r\n    </svg>\r\n  )\r\n}\r\n\r\nexport const StopIcon = ({ size = 16 }: { size?: number }) => {\r\n  return (\r\n    <svg\r\n      height={size}\r\n      viewBox=\"0 0 16 16\"\r\n      width={size}\r\n      style={{ color: \"currentcolor\" }}\r\n    >\r\n      <path\r\n        fillRule=\"evenodd\"\r\n        clipRule=\"evenodd\"\r\n        d=\"M3 3H13V13H3V3Z\"\r\n        fill=\"currentColor\"\r\n      ></path>\r\n    </svg>\r\n  );\r\n};\r\n\r\nexport const ThumbUpIcon = ({ size = 16 }: { size?: number }) => (\r\n  <svg\r\n    height={size}\r\n    strokeLinejoin=\"round\"\r\n    viewBox=\"0 0 16 16\"\r\n    width={size}\r\n    style={{ color: 'currentcolor' }}\r\n  >\r\n    <path\r\n      fillRule=\"evenodd\"\r\n      clipRule=\"evenodd\"\r\n      d=\"M6.89531 2.23972C6.72984 2.12153 6.5 2.23981 6.5 2.44315V5.25001C6.5 6.21651 5.7165 7.00001 4.75 7.00001H2.5V13.5H12.1884C12.762 13.5 13.262 13.1096 13.4011 12.5532L14.4011 8.55318C14.5984 7.76425 14.0017 7.00001 13.1884 7.00001H9.25H8.5V6.25001V3.51458C8.5 3.43384 8.46101 3.35807 8.39531 3.31114L6.89531 2.23972ZM5 2.44315C5 1.01975 6.6089 0.191779 7.76717 1.01912L9.26717 2.09054C9.72706 2.41904 10 2.94941 10 3.51458V5.50001H13.1884C14.9775 5.50001 16.2903 7.18133 15.8563 8.91698L14.8563 12.917C14.5503 14.1412 13.4503 15 12.1884 15H1.75H1V14.25V6.25001V5.50001H1.75H4.75C4.88807 5.50001 5 5.38808 5 5.25001V2.44315Z\"\r\n      fill=\"currentColor\"\r\n    />\r\n  </svg>\r\n);\r\n\r\nexport const ThumbDownIcon = ({ size = 16 }: { size?: number }) => (\r\n  <svg\r\n    height={size}\r\n    strokeLinejoin=\"round\"\r\n    viewBox=\"0 0 16 16\"\r\n    width={size}\r\n    style={{ color: 'currentcolor' }}\r\n  >\r\n    <path\r\n      fillRule=\"evenodd\"\r\n      clipRule=\"evenodd\"\r\n      d=\"M6.89531 13.7603C6.72984 13.8785 6.5 13.7602 6.5 13.5569V10.75C6.5 9.7835 5.7165 9 4.75 9H2.5V2.5H12.1884C12.762 2.5 13.262 2.89037 13.4011 3.44683L14.4011 7.44683C14.5984 8.23576 14.0017 9 13.1884 9H9.25H8.5V9.75V12.4854C8.5 12.5662 8.46101 12.6419 8.39531 12.6889L6.89531 13.7603ZM5 13.5569C5 14.9803 6.6089 15.8082 7.76717 14.9809L9.26717 13.9095C9.72706 13.581 10 13.0506 10 12.4854V10.5H13.1884C14.9775 10.5 16.2903 8.81868 15.8563 7.08303L14.8563 3.08303C14.5503 1.85882 13.4503 1 12.1884 1H1.75H1V1.75V9.75V10.5H1.75H4.75C4.88807 10.5 5 10.6119 5 10.75V13.5569Z\"\r\n      fill=\"currentColor\"\r\n    />\r\n  </svg>\r\n);\r\n\r\n\r\nexport {\r\n  IconEdit,\r\n  IconNextChat,\r\n  IconOpenAI,\r\n  IconVercel,\r\n  IconGitHub,\r\n  IconSeparator,\r\n  IconArrowDown,\r\n  IconArrowRight,\r\n  IconUser,\r\n  IconPlus,\r\n  IconArrowElbow,\r\n  IconSpinner,\r\n  IconMessage,\r\n  IconTrash,\r\n  IconRefresh,\r\n  IconStop,\r\n  IconSidebar,\r\n  IconMoon,\r\n  IconSun,\r\n  IconCopy,\r\n  IconCheck,\r\n  IconDownload,\r\n  IconClose,\r\n  IconShare,\r\n  IconUsers,\r\n  IconExternalLink,\r\n  IconChevronUpDown\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA;AAEA;;;AAJA;;;AAMA,SAAS,aAAa,EACpB,SAAS,EACT,QAAQ,EACR,GAAG,OACkD;;IACrD,MAAM,KAAK,CAAA,GAAA,sQAAA,CAAA,QAAW,AAAD;IAErB,qBACE,sSAAC;QACC,SAAQ;QACR,MAAK;QACL,OAAM;QACN,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,UAAU;QACvB,GAAG,KAAK;;0BAET,sSAAC;;kCACC,sSAAC;wBACC,IAAI,CAAC,SAAS,EAAE,GAAG,EAAE,CAAC;wBACtB,IAAG;wBACH,IAAG;wBACH,IAAG;wBACH,IAAG;wBACH,eAAc;;0CAEd,sSAAC;gCAAK,WAAW,WAAW,UAAU;;;;;;0CACtC,sSAAC;gCACC,QAAQ;gCACR,WAAW,WAAW,UAAU;gCAChC,aAAa;;;;;;;;;;;;kCAGjB,sSAAC;wBACC,IAAI,CAAC,SAAS,EAAE,GAAG,EAAE,CAAC;wBACtB,IAAG;wBACH,IAAG;wBACH,IAAG;wBACH,IAAG;wBACH,eAAc;;0CAEd,sSAAC;gCAAK,WAAW,WAAW,UAAU;;;;;;0CACtC,sSAAC;gCACC,QAAQ;gCACR,WAAW,WAAW,UAAU;gCAChC,aAAa;;;;;;;;;;;;;;;;;;0BAInB,sSAAC;gBACC,GAAE;gBACF,MAAM,WAAW,UAAU;gBAC3B,QAAQ,WAAW,UAAU;gBAC7B,aAAa;gBACb,eAAc;gBACd,gBAAe;;;;;;0BAEjB,sSAAC;gBACC,IAAG;gBACH,OAAO;oBAAE,UAAU;gBAAQ;gBAC3B,WAAU;gBACV,GAAG;gBACH,GAAG;gBACH,OAAO;gBACP,QAAQ;0BAER,cAAA,sSAAC;oBAAO,IAAI;oBAAG,IAAI;oBAAG,GAAG;oBAAG,MAAM,WAAW,UAAU;;;;;;;;;;;0BAEzD,sSAAC;gBAAE,MAAK;;kCACN,sSAAC;wBAAO,IAAI;wBAAG,IAAI;wBAAG,GAAG;wBAAG,MAAM,WAAW,UAAU;;;;;;kCACvD,sSAAC;wBACC,GAAE;wBACF,MAAM,CAAC,cAAc,EAAE,GAAG,GAAG,CAAC;;;;;;kCAEhC,sSAAC;wBACC,GAAE;wBACF,GAAE;wBACF,OAAM;wBACN,QAAO;wBACP,MAAM,CAAC,cAAc,EAAE,GAAG,GAAG,CAAC;;;;;;;;;;;;;;;;;;AAKxC;GAlFS;KAAA;AAoFT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,sSAAC;QACC,MAAK;QACL,SAAQ;QACR,MAAK;QACL,OAAM;QACN,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,UAAU;QACvB,GAAG,KAAK;;0BAET,sSAAC;0BAAM;;;;;;0BACP,sSAAC;gBAAK,GAAE;;;;;;;;;;;;AAGd;MAdS;AAgBT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,sSAAC;QACC,cAAW;QACX,MAAK;QACL,SAAQ;QACR,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,UAAU;QACvB,GAAG,KAAK;kBAET,cAAA,sSAAC;YACC,GAAE;YACF,MAAK;;;;;;;;;;;AAIb;MAfS;AAiBT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,sSAAC;QACC,MAAK;QACL,SAAQ;QACR,OAAM;QACN,MAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,UAAU;QACvB,GAAG,KAAK;;0BAET,sSAAC;0BAAM;;;;;;0BACP,sSAAC;gBAAK,GAAE;;;;;;;;;;;;AAGd;MAdS;AAgBT,SAAS,cAAc,EAAE,SAAS,EAAE,GAAG,OAAoC;IACzE,qBACE,sSAAC;QACC,MAAK;QACL,gBAAe;QACf,QAAO;QACP,eAAc;QACd,gBAAe;QACf,aAAY;QACZ,SAAQ;QACR,eAAY;QACZ,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,UAAU;QACvB,GAAG,KAAK;kBAET,cAAA,sSAAC;YAAK,GAAE;;;;;;;;;;;AAGd;MAjBS;AAmBT,SAAS,cAAc,EAAE,SAAS,EAAE,GAAG,OAAoC;IACzE,qBACE,sSAAC;QACC,OAAM;QACN,SAAQ;QACR,MAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,UAAU;QACvB,GAAG,KAAK;kBAET,cAAA,sSAAC;YAAK,GAAE;;;;;;;;;;;AAGd;MAZS;AAcT,SAAS,eAAe,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC1E,qBACE,sSAAC;QACC,OAAM;QACN,SAAQ;QACR,MAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,UAAU;QACvB,GAAG,KAAK;kBAET,cAAA,sSAAC;YAAK,GAAE;;;;;;;;;;;AAGd;MAZS;AAcT,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAoC;IACpE,qBACE,sSAAC;QACC,OAAM;QACN,SAAQ;QACR,MAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,UAAU;QACvB,GAAG,KAAK;kBAET,cAAA,sSAAC;YAAK,GAAE;;;;;;;;;;;AAGd;MAZS;AAcT,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAoC;IACpE,qBACE,sSAAC;QACC,OAAM;QACN,SAAQ;QACR,MAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,UAAU;QACvB,GAAG,KAAK;kBAET,cAAA,sSAAC;YAAK,GAAE;;;;;;;;;;;AAGd;MAZS;AAcT,SAAS,eAAe,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC1E,qBACE,sSAAC;QACC,OAAM;QACN,SAAQ;QACR,MAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,UAAU;QACvB,GAAG,KAAK;kBAET,cAAA,sSAAC;YAAK,GAAE;;;;;;;;;;;AAGd;MAZS;AAcT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,sSAAC;QACC,OAAM;QACN,SAAQ;QACR,MAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,uBAAuB;QACpC,GAAG,KAAK;kBAET,cAAA,sSAAC;YAAK,GAAE;;;;;;;;;;;AAGd;OAZS;AAcT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,sSAAC;QACC,OAAM;QACN,SAAQ;QACR,MAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,UAAU;QACvB,GAAG,KAAK;kBAET,cAAA,sSAAC;YAAK,GAAE;;;;;;;;;;;AAGd;OAZS;AAcT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,sSAAC;QACC,OAAM;QACN,SAAQ;QACR,MAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,UAAU;QACvB,GAAG,KAAK;kBAET,cAAA,sSAAC;YAAK,GAAE;;;;;;;;;;;AAGd;OAZS;AAcT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,sSAAC;QACC,OAAM;QACN,SAAQ;QACR,MAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,UAAU;QACvB,GAAG,KAAK;kBAET,cAAA,sSAAC;YAAK,GAAE;;;;;;;;;;;AAGd;OAZS;AAcT,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAoC;IACpE,qBACE,sSAAC;QACC,OAAM;QACN,SAAQ;QACR,MAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,UAAU;QACvB,GAAG,KAAK;kBAET,cAAA,sSAAC;YAAK,GAAE;;;;;;;;;;;AAGd;OAZS;AAcT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,sSAAC;QACC,OAAM;QACN,SAAQ;QACR,MAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,UAAU;QACvB,GAAG,KAAK;kBAET,cAAA,sSAAC;YAAK,GAAE;;;;;;;;;;;AAGd;OAZS;AAcT,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAoC;IACpE,qBACE,sSAAC;QACC,OAAM;QACN,SAAQ;QACR,MAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,UAAU;QACvB,GAAG,KAAK;kBAET,cAAA,sSAAC;YAAK,GAAE;;;;;;;;;;;AAGd;OAZS;AAcT,SAAS,QAAQ,EAAE,SAAS,EAAE,GAAG,OAAoC;IACnE,qBACE,sSAAC;QACC,OAAM;QACN,SAAQ;QACR,MAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,UAAU;QACvB,GAAG,KAAK;kBAET,cAAA,sSAAC;YAAK,GAAE;;;;;;;;;;;AAGd;OAZS;AAcT,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAoC;IACpE,qBACE,sSAAC;QACC,OAAM;QACN,SAAQ;QACR,MAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,UAAU;QACvB,GAAG,KAAK;kBAET,cAAA,sSAAC;YAAK,GAAE;;;;;;;;;;;AAGd;OAZS;AAcT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,sSAAC;QACC,OAAM;QACN,SAAQ;QACR,MAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,UAAU;QACvB,GAAG,KAAK;kBAET,cAAA,sSAAC;YAAK,GAAE;;;;;;;;;;;AAGd;OAZS;AAcT,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,sSAAC;QACC,OAAM;QACN,SAAQ;QACR,MAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,UAAU;QACvB,GAAG,KAAK;kBAET,cAAA,sSAAC;YAAK,GAAE;;;;;;;;;;;AAGd;OAZS;AAcT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,sSAAC;QACC,OAAM;QACN,SAAQ;QACR,MAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,UAAU;QACvB,GAAG,KAAK;kBAET,cAAA,sSAAC;YAAK,GAAE;;;;;;;;;;;AAGd;OAZS;AAcT,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAoC;IACpE,qBACE,sSAAC;QACC,OAAM;QACN,MAAK;QACL,SAAQ;QACR,aAAa;QACb,QAAO;QACP,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,UAAU;QACvB,GAAG,KAAK;kBAET,cAAA,sSAAC;YACC,eAAc;YACd,gBAAe;YACf,GAAE;;;;;;;;;;;AAIV;OAlBS;AAoBT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,sSAAC;QACC,OAAM;QACN,MAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,UAAU;QACxB,SAAQ;QACP,GAAG,KAAK;kBAET,cAAA,sSAAC;YAAK,GAAE;;;;;;;;;;;AAGd;OAZS;AAcT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,sSAAC;QACC,OAAM;QACN,MAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,UAAU;QACxB,SAAQ;QACP,GAAG,KAAK;kBAET,cAAA,sSAAC;YAAK,GAAE;;;;;;;;;;;AAGd;OAZS;AAcT,SAAS,iBAAiB,EACxB,SAAS,EACT,GAAG,OACyB;IAC5B,qBACE,sSAAC;QACC,OAAM;QACN,MAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,UAAU;QACxB,SAAQ;QACP,GAAG,KAAK;kBAET,cAAA,sSAAC;YAAK,GAAE;;;;;;;;;;;AAGd;OAfS;AAiBT,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACyB;IAC5B,qBACE,sSAAC;QACC,OAAM;QACN,MAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,UAAU;QACxB,SAAQ;QACP,GAAG,KAAK;kBAET,cAAA,sSAAC;YAAK,GAAE;;;;;;;;;;;AAGd;OAfS;AAiBF,MAAM,WAAW,CAAC,EAAE,OAAO,EAAE,EAAqB;IACvD,qBACE,sSAAC;QACC,QAAQ;QACR,SAAQ;QACR,OAAO;QACP,OAAO;YAAE,OAAO;QAAe;kBAE/B,cAAA,sSAAC;YACC,UAAS;YACT,UAAS;YACT,GAAE;YACF,MAAK;;;;;;;;;;;AAIb;OAhBa;AAkBN,MAAM,cAAc,CAAC,EAAE,OAAO,EAAE,EAAqB,iBAC1D,sSAAC;QACC,QAAQ;QACR,gBAAe;QACf,SAAQ;QACR,OAAO;QACP,OAAO;YAAE,OAAO;QAAe;kBAE/B,cAAA,sSAAC;YACC,UAAS;YACT,UAAS;YACT,GAAE;YACF,MAAK;;;;;;;;;;;OAZE;AAiBN,MAAM,gBAAgB,CAAC,EAAE,OAAO,EAAE,EAAqB,iBAC5D,sSAAC;QACC,QAAQ;QACR,gBAAe;QACf,SAAQ;QACR,OAAO;QACP,OAAO;YAAE,OAAO;QAAe;kBAE/B,cAAA,sSAAC;YACC,UAAS;YACT,UAAS;YACT,GAAE;YACF,MAAK;;;;;;;;;;;OAZE", "debugId": null}}, {"offset": {"line": 1386, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/components/theme-toggle.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport * as React from 'react'\r\nimport { useTheme } from 'next-themes'\r\nimport { cn } from '@/lib/utils'\r\nimport { Button } from '@/components/ui/button'\r\nimport { IconMoon, IconSun } from '@/components/ui/icons'\r\n\r\ninterface ThemeToggleProps {\r\n  className?: string\r\n}\r\n\r\nexport function ThemeToggle({ className }: ThemeToggleProps) {\r\n  const { setTheme, theme } = useTheme()\r\n  const [mounted, setMounted] = React.useState(false)\r\n  const [_, startTransition] = React.useTransition()\r\n\r\n  // hydration 에러 수정. mounted 코드를 제거하려면 컴포넌트 조건부 렌더링 수정필요\r\n  React.useEffect(() => {\r\n    setMounted(true)\r\n  }, [])\r\n\r\n  if (!mounted) {\r\n    return <Button variant=\"ghost\" size=\"icon\" className={cn(className)} />\r\n  }\r\n\r\n  return (\r\n    <Button\r\n      variant=\"ghost\"\r\n      size=\"icon\"\r\n      className={cn(className)}\r\n      onClick={() => {\r\n        startTransition(() => {\r\n          setTheme(theme === 'light' ? 'dark' : 'light')\r\n        })\r\n      }}\r\n    >\r\n      {theme === 'dark' ? (\r\n        <IconMoon className=\"transition-all\" />\r\n      ) : (\r\n        <IconSun className=\"transition-all\" />\r\n      )}\r\n      <span className=\"sr-only\">테마 변경</span>\r\n    </Button>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;;;AANA;;;;;;AAYO,SAAS,YAAY,EAAE,SAAS,EAAoB;;IACzD,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,4PAAA,CAAA,WAAQ,AAAD;IACnC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAc,AAAD,EAAE;IAC7C,MAAM,CAAC,GAAG,gBAAgB,GAAG,CAAA,GAAA,sQAAA,CAAA,gBAAmB,AAAD;IAE/C,uDAAuD;IACvD,CAAA,GAAA,sQAAA,CAAA,YAAe,AAAD;iCAAE;YACd,WAAW;QACb;gCAAG,EAAE;IAEL,IAAI,CAAC,SAAS;QACZ,qBAAO,sSAAC,8HAAA,CAAA,SAAM;YAAC,SAAQ;YAAQ,MAAK;YAAO,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE;;;;;;IAC3D;IAEA,qBACE,sSAAC,8HAAA,CAAA,SAAM;QACL,SAAQ;QACR,MAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE;QACd,SAAS;YACP,gBAAgB;gBACd,SAAS,UAAU,UAAU,SAAS;YACxC;QACF;;YAEC,UAAU,uBACT,sSAAC,6HAAA,CAAA,WAAQ;gBAAC,WAAU;;;;;qCAEpB,sSAAC,6HAAA,CAAA,UAAO;gBAAC,WAAU;;;;;;0BAErB,sSAAC;gBAAK,WAAU;0BAAU;;;;;;;;;;;;AAGhC;GAjCgB;;QACc,4PAAA,CAAA,WAAQ;;;KADtB", "debugId": null}}, {"offset": {"line": 1480, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/components/preview/chat-header.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useRouter } from 'next/navigation';\r\nimport { useWindowSize } from 'usehooks-ts';\r\n\r\nimport { ModelSelector } from '@/components/model-selector';\r\n// import { SidebarToggle } from '@/components/sidebar-toggle';\r\nimport { Button } from '@/components/ui/button';\r\n// import { PlusIcon, VercelIcon } from './icons';\r\n// import { useSidebar } from './ui/sidebar';\r\nimport { memo } from 'react';\r\nimport { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip';\r\nimport { PlusIcon } from 'lucide-react';\r\nimport { ThemeToggle } from '../theme-toggle';\r\n// import { VisibilityType, VisibilitySelector } from './visibility-selector';\r\n\r\nfunction PureChatHeader({\r\n  chatId,\r\n  selectedModelId,\r\n  // selectedVisibilityType,\r\n  isReadonly,\r\n}: {\r\n  chatId: string;\r\n  selectedModelId: string;\r\n  // selectedVisibilityType: VisibilityType;\r\n  isReadonly: boolean;\r\n}) {\r\n  const router = useRouter();\r\n  // const { open } = useSidebar();\r\n\r\n  const { width: windowWidth } = useWindowSize();\r\n\r\n  return (\r\n    <header className=\"flex sticky top-0 bg-background py-1.5 items-center px-2 md:px-2 gap-2\">\r\n      {/* <SidebarToggle /> */}\r\n\r\n      <Tooltip>\r\n        <TooltipTrigger asChild>\r\n          <Button\r\n            variant=\"outline\"\r\n            className=\"order-2 md:order-2 md:px-2 px-2 md:h-fit ml-auto md:ml-0\"\r\n            onClick={() => {\r\n              router.refresh();\r\n            }}\r\n          >\r\n            <PlusIcon />\r\n            <span className=\"md:sr-only\">새 대화</span>\r\n          </Button>\r\n        </TooltipTrigger>\r\n        <TooltipContent>새 대화</TooltipContent>\r\n      </Tooltip>\r\n\r\n      <ModelSelector\r\n        selectedModelId={selectedModelId}\r\n        className=\"order-1 md:order-1\"\r\n      />\r\n\r\n      <ThemeToggle className=\"order-3 ml-auto\" />\r\n\r\n    </header>\r\n  );\r\n}\r\n\r\nexport const ChatHeader = memo(PureChatHeader, (prevProps, nextProps) => {\r\n  return prevProps.selectedModelId === nextProps.selectedModelId;\r\n});\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AACA,+DAA+D;AAC/D;AACA,kDAAkD;AAClD,6CAA6C;AAC7C;AACA;AACA;AACA;;;AAbA;;;;;;;;;AAcA,8EAA8E;AAE9E,SAAS,eAAe,EACtB,MAAM,EACN,eAAe,EACf,0BAA0B;AAC1B,UAAU,EAMX;;IACC,MAAM,SAAS,CAAA,GAAA,8OAAA,CAAA,YAAS,AAAD;IACvB,iCAAiC;IAEjC,MAAM,EAAE,OAAO,WAAW,EAAE,GAAG,CAAA,GAAA,mOAAA,CAAA,gBAAa,AAAD;IAE3C,qBACE,sSAAC;QAAO,WAAU;;0BAGhB,sSAAC,+HAAA,CAAA,UAAO;;kCACN,sSAAC,+HAAA,CAAA,iBAAc;wBAAC,OAAO;kCACrB,cAAA,sSAAC,8HAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,WAAU;4BACV,SAAS;gCACP,OAAO,OAAO;4BAChB;;8CAEA,sSAAC,6RAAA,CAAA,WAAQ;;;;;8CACT,sSAAC;oCAAK,WAAU;8CAAa;;;;;;;;;;;;;;;;;kCAGjC,sSAAC,+HAAA,CAAA,iBAAc;kCAAC;;;;;;;;;;;;0BAGlB,sSAAC,mIAAA,CAAA,gBAAa;gBACZ,iBAAiB;gBACjB,WAAU;;;;;;0BAGZ,sSAAC,iIAAA,CAAA,cAAW;gBAAC,WAAU;;;;;;;;;;;;AAI7B;GA7CS;;QAWQ,8OAAA,CAAA,YAAS;QAGO,mOAAA,CAAA,gBAAa;;;KAdrC;AA+CF,MAAM,2BAAa,CAAA,GAAA,sQAAA,CAAA,OAAI,AAAD,EAAE,gBAAgB,CAAC,WAAW;IACzD,OAAO,UAAU,eAAe,KAAK,UAAU,eAAe;AAChE", "debugId": null}}, {"offset": {"line": 1609, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/components/icons.tsx"], "sourcesContent": ["export const BotIcon = () => {\r\n  return (\r\n    <svg\r\n      height=\"16\"\r\n      strokeLinejoin=\"round\"\r\n      viewBox=\"0 0 16 16\"\r\n      width=\"16\"\r\n      style={{ color: 'currentcolor' }}\r\n    >\r\n      <path\r\n        fillRule=\"evenodd\"\r\n        clipRule=\"evenodd\"\r\n        d=\"M8.75 2.79933C9.19835 2.53997 9.5 2.05521 9.5 1.5C9.5 0.671573 8.82843 0 8 0C7.17157 0 6.5 0.671573 6.5 1.5C6.5 2.05521 6.80165 2.53997 7.25 2.79933V5H7C4.027 5 1.55904 7.16229 1.08296 10H0V13H1V14.5V16H2.5H13.5H15V14.5V13H16V10H14.917C14.441 7.16229 11.973 5 9 5H8.75V2.79933ZM7 6.5C4.51472 6.5 2.5 8.51472 2.5 11V14.5H13.5V11C13.5 8.51472 11.4853 6.5 9 6.5H7ZM7.25 11.25C7.25 12.2165 6.4665 13 5.5 13C4.5335 13 3.75 12.2165 3.75 11.25C3.75 10.2835 4.5335 9.5 5.5 9.5C6.4665 9.5 7.25 10.2835 7.25 11.25ZM10.5 13C11.4665 13 12.25 12.2165 12.25 11.25C12.25 10.2835 11.4665 9.5 10.5 9.5C9.5335 9.5 8.75 10.2835 8.75 11.25C8.75 12.2165 9.5335 13 10.5 13Z\"\r\n        fill=\"currentColor\"\r\n      />\r\n    </svg>\r\n  );\r\n};\r\n\r\nexport const UserIcon = () => {\r\n  return (\r\n    <svg\r\n      data-testid=\"geist-icon\"\r\n      height=\"16\"\r\n      strokeLinejoin=\"round\"\r\n      viewBox=\"0 0 16 16\"\r\n      width=\"16\"\r\n      style={{ color: 'currentcolor' }}\r\n    >\r\n      <path\r\n        fillRule=\"evenodd\"\r\n        clipRule=\"evenodd\"\r\n        d=\"M7.75 0C5.95507 0 4.5 1.45507 4.5 3.25V3.75C4.5 5.54493 5.95507 7 7.75 7H8.25C10.0449 7 11.5 5.54493 11.5 3.75V3.25C11.5 1.45507 10.0449 0 8.25 0H7.75ZM6 3.25C6 2.2835 6.7835 1.5 7.75 1.5H8.25C9.2165 1.5 10 2.2835 10 3.25V3.75C10 4.7165 9.2165 5.5 8.25 5.5H7.75C6.7835 5.5 6 4.7165 6 3.75V3.25ZM2.5 14.5V13.1709C3.31958 11.5377 4.99308 10.5 6.82945 10.5H9.17055C11.0069 10.5 12.6804 11.5377 13.5 13.1709V14.5H2.5ZM6.82945 9C4.35483 9 2.10604 10.4388 1.06903 12.6857L1 12.8353V13V15.25V16H1.75H14.25H15V15.25V13V12.8353L14.931 12.6857C13.894 10.4388 11.6452 9 9.17055 9H6.82945Z\"\r\n        fill=\"currentColor\"\r\n      />\r\n    </svg>\r\n  );\r\n};\r\n\r\nexport const AttachmentIcon = () => {\r\n  return (\r\n    <svg\r\n      height=\"16\"\r\n      strokeLinejoin=\"round\"\r\n      viewBox=\"0 0 16 16\"\r\n      width=\"16\"\r\n      style={{ color: 'currentcolor' }}\r\n    >\r\n      <path\r\n        fillRule=\"evenodd\"\r\n        clipRule=\"evenodd\"\r\n        d=\"M14.5 6.5V13.5C14.5 14.8807 13.3807 16 12 16H4C2.61929 16 1.5 14.8807 1.5 13.5V1.5V0H3H8H9.08579C9.351 0 9.60536 0.105357 9.79289 0.292893L14.2071 4.70711C14.3946 4.89464 14.5 5.149 14.5 5.41421V6.5ZM13 6.5V13.5C13 14.0523 12.5523 14.5 12 14.5H4C3.44772 14.5 3 14.0523 3 13.5V1.5H8V5V6.5H9.5H13ZM9.5 2.12132V5H12.3787L9.5 2.12132Z\"\r\n        fill=\"currentColor\"\r\n      />\r\n    </svg>\r\n  );\r\n};\r\n\r\nexport const VercelIcon = ({ size = 17 }) => {\r\n  return (\r\n    <svg\r\n      height={size}\r\n      strokeLinejoin=\"round\"\r\n      viewBox=\"0 0 16 16\"\r\n      width={size}\r\n      style={{ color: 'currentcolor' }}\r\n    >\r\n      <path\r\n        fillRule=\"evenodd\"\r\n        clipRule=\"evenodd\"\r\n        d=\"M8 1L16 15H0L8 1Z\"\r\n        fill=\"currentColor\"\r\n      />\r\n    </svg>\r\n  );\r\n};\r\n\r\nexport const GitIcon = () => {\r\n  return (\r\n    <svg\r\n      height=\"16\"\r\n      strokeLinejoin=\"round\"\r\n      viewBox=\"0 0 16 16\"\r\n      width=\"16\"\r\n      style={{ color: 'currentcolor' }}\r\n    >\r\n      <g clipPath=\"url(#clip0_872_3147)\">\r\n        <path\r\n          fillRule=\"evenodd\"\r\n          clipRule=\"evenodd\"\r\n          d=\"M8 0C3.58 0 0 3.57879 0 7.99729C0 11.5361 2.29 14.5251 5.47 15.5847C5.87 15.6547 6.02 15.4148 6.02 15.2049C6.02 15.0149 6.01 14.3851 6.01 13.7154C4 14.0852 3.48 13.2255 3.32 12.7757C3.23 12.5458 2.84 11.836 2.5 11.6461C2.22 11.4961 1.82 11.1262 2.49 11.1162C3.12 11.1062 3.57 11.696 3.72 11.936C4.44 13.1455 5.59 12.8057 6.05 12.5957C6.12 12.0759 6.33 11.726 6.56 11.5261C4.78 11.3262 2.92 10.6364 2.92 7.57743C2.92 6.70773 3.23 5.98797 3.74 5.42816C3.66 5.22823 3.38 4.40851 3.82 3.30888C3.82 3.30888 4.49 3.09895 6.02 4.1286C6.66 3.94866 7.34 3.85869 8.02 3.85869C8.7 3.85869 9.38 3.94866 10.02 4.1286C11.55 3.08895 12.22 3.30888 12.22 3.30888C12.66 4.40851 12.38 5.22823 12.3 5.42816C12.81 5.98797 13.12 6.69773 13.12 7.57743C13.12 10.6464 11.25 11.3262 9.47 11.5261C9.76 11.776 10.01 12.2558 10.01 13.0056C10.01 14.0752 10 14.9349 10 15.2049C10 15.4148 10.15 15.6647 10.55 15.5847C12.1381 15.0488 13.5182 14.0284 14.4958 12.6673C15.4735 11.3062 15.9996 9.67293 16 7.99729C16 3.57879 12.42 0 8 0Z\"\r\n          fill=\"currentColor\"\r\n        />\r\n      </g>\r\n      <defs>\r\n        <clipPath id=\"clip0_872_3147\">\r\n          <rect width=\"16\" height=\"16\" fill=\"white\" />\r\n        </clipPath>\r\n      </defs>\r\n    </svg>\r\n  );\r\n};\r\n\r\nexport const BoxIcon = ({ size = 16 }: { size: number }) => {\r\n  return (\r\n    <svg\r\n      height={size}\r\n      strokeLinejoin=\"round\"\r\n      viewBox=\"0 0 16 16\"\r\n      width={size}\r\n      style={{ color: 'currentcolor' }}\r\n    >\r\n      <path\r\n        fillRule=\"evenodd\"\r\n        clipRule=\"evenodd\"\r\n        d=\"M8 0.154663L8.34601 0.334591L14.596 3.58459L15 3.79466V4.25V11.75V12.2053L14.596 12.4154L8.34601 15.6654L8 15.8453L7.65399 15.6654L1.40399 12.4154L1 12.2053V11.75V4.25V3.79466L1.40399 3.58459L7.65399 0.334591L8 0.154663ZM2.5 11.2947V5.44058L7.25 7.81559V13.7647L2.5 11.2947ZM8.75 13.7647L13.5 11.2947V5.44056L8.75 7.81556V13.7647ZM8 1.84534L12.5766 4.22519L7.99998 6.51352L3.42335 4.2252L8 1.84534Z\"\r\n        fill=\"currentColor\"\r\n      />\r\n    </svg>\r\n  );\r\n};\r\n\r\nexport const HomeIcon = ({ size = 16 }: { size: number }) => {\r\n  return (\r\n    <svg\r\n      height={size}\r\n      strokeLinejoin=\"round\"\r\n      viewBox=\"0 0 16 16\"\r\n      width={size}\r\n      style={{ color: 'currentcolor' }}\r\n    >\r\n      <path\r\n        fillRule=\"evenodd\"\r\n        clipRule=\"evenodd\"\r\n        d=\"M12.5 6.56062L8.00001 2.06062L3.50001 6.56062V13.5L6.00001 13.5V11C6.00001 9.89539 6.89544 8.99996 8.00001 8.99996C9.10458 8.99996 10 9.89539 10 11V13.5L12.5 13.5V6.56062ZM13.78 5.71933L8.70711 0.646409C8.31659 0.255886 7.68342 0.255883 7.2929 0.646409L2.21987 5.71944C2.21974 5.71957 2.21961 5.7197 2.21949 5.71982L0.469676 7.46963L-0.0606537 7.99996L1.00001 9.06062L1.53034 8.53029L2.00001 8.06062V14.25V15H2.75001L6.00001 15H7.50001H8.50001H10L13.25 15H14V14.25V8.06062L14.4697 8.53029L15 9.06062L16.0607 7.99996L15.5303 7.46963L13.7806 5.71993C13.7804 5.71973 13.7802 5.71953 13.78 5.71933ZM8.50001 11V13.5H7.50001V11C7.50001 10.7238 7.72386 10.5 8.00001 10.5C8.27615 10.5 8.50001 10.7238 8.50001 11Z\"\r\n        fill=\"currentColor\"\r\n      />\r\n    </svg>\r\n  );\r\n};\r\n\r\nexport const GPSIcon = ({ size = 16 }: { size: number }) => {\r\n  return (\r\n    <svg\r\n      height={size}\r\n      strokeLinejoin=\"round\"\r\n      viewBox=\"0 0 16 16\"\r\n      width={size}\r\n      style={{ color: 'currentcolor' }}\r\n    >\r\n      <path\r\n        d=\"M1 6L15 1L10 15L7.65955 8.91482C7.55797 8.65073 7.34927 8.44203 7.08518 8.34045L1 6Z\"\r\n        stroke=\"currentColor\"\r\n        strokeWidth=\"1.5\"\r\n        strokeLinecap=\"round\"\r\n        strokeLinejoin=\"bevel\"\r\n        fill=\"transparent\"\r\n      />\r\n    </svg>\r\n  );\r\n};\r\n\r\nexport const InvoiceIcon = ({ size = 16 }: { size: number }) => {\r\n  return (\r\n    <svg\r\n      height={size}\r\n      strokeLinejoin=\"round\"\r\n      viewBox=\"0 0 16 16\"\r\n      width={size}\r\n      style={{ color: 'currentcolor' }}\r\n    >\r\n      <path\r\n        fillRule=\"evenodd\"\r\n        clipRule=\"evenodd\"\r\n        d=\"M13 15.1L12 14.5L10.1524 15.8857C10.0621 15.9534 9.93791 15.9534 9.8476 15.8857L8 14.5L6.14377 15.8922C6.05761 15.9568 5.94008 15.9601 5.85047 15.9003L3.75 14.5L3 15L2.83257 15.1116L1.83633 15.7758L1.68656 15.8756C1.60682 15.9288 1.5 15.8716 1.5 15.7758V15.5958V14.3985V14.1972V1.5V0H3H8H9.08579C9.351 0 9.60536 0.105357 9.79289 0.292893L14.2071 4.70711C14.3946 4.89464 14.5 5.149 14.5 5.41421V6.5V14.2507V14.411V15.5881V15.7881C14.5 15.8813 14.3982 15.9389 14.3183 15.891L14.1468 15.7881L13.1375 15.1825L13 15.1ZM12.3787 5L9.5 2.12132V5H12.3787ZM8 1.5V5V6.5H9.5H13V13.3507L12.7717 13.2138L11.9069 12.6948L11.1 13.3L10 14.125L8.9 13.3L8 12.625L7.1 13.3L5.94902 14.1632L4.58205 13.2519L3.75 12.6972L3 13.1972V1.5H8Z\"\r\n        fill=\"currentColor\"\r\n      />\r\n    </svg>\r\n  );\r\n};\r\n\r\nexport const LogoOpenAI = ({ size = 16 }: { size?: number }) => {\r\n  return (\r\n    <svg\r\n      height={size}\r\n      strokeLinejoin=\"round\"\r\n      viewBox=\"0 0 16 16\"\r\n      width={size}\r\n      style={{ color: 'currentcolor' }}\r\n    >\r\n      <path\r\n        d=\"M14.9449 6.54871C15.3128 5.45919 15.1861 4.26567 14.5978 3.27464C13.7131 1.75461 11.9345 0.972595 10.1974 1.3406C9.42464 0.481584 8.3144 -0.00692594 7.15045 7.42132e-05C5.37487 -0.00392587 3.79946 1.1241 3.2532 2.79113C2.11256 3.02164 1.12799 3.72615 0.551837 4.72468C-0.339497 6.24071 -0.1363 8.15175 1.05451 9.45178C0.686626 10.5413 0.813308 11.7348 1.40162 12.7258C2.28637 14.2459 4.06498 15.0279 5.80204 14.6599C6.5743 15.5189 7.68504 16.0074 8.849 15.9999C10.6256 16.0044 12.2015 14.8754 12.7478 13.2069C13.8884 12.9764 14.873 12.2718 15.4491 11.2733C16.3394 9.75728 16.1357 7.84774 14.9454 6.54771L14.9449 6.54871ZM8.85001 14.9544C8.13907 14.9554 7.45043 14.7099 6.90468 14.2604C6.92951 14.2474 6.97259 14.2239 7.00046 14.2069L10.2293 12.3668C10.3945 12.2743 10.4959 12.1008 10.4949 11.9133V7.42173L11.8595 8.19925C11.8742 8.20625 11.8838 8.22025 11.8858 8.23625V11.9558C11.8838 13.6099 10.5263 14.9509 8.85001 14.9544ZM2.32133 12.2028C1.9651 11.5958 1.8369 10.8843 1.95902 10.1938C1.98284 10.2078 2.02489 10.2333 2.05479 10.2503L5.28366 12.0903C5.44733 12.1848 5.65003 12.1848 5.81421 12.0903L9.75604 9.84429V11.3993C9.75705 11.4153 9.74945 11.4308 9.73678 11.4408L6.47295 13.3004C5.01915 14.1264 3.1625 13.6354 2.32184 12.2028H2.32133ZM1.47155 5.24819C1.82626 4.64017 2.38619 4.17516 3.05305 3.93366C3.05305 3.96116 3.05152 4.00966 3.05152 4.04366V7.72424C3.05051 7.91124 3.15186 8.08475 3.31654 8.17725L7.25838 10.4228L5.89376 11.2003C5.88008 11.2093 5.86285 11.2108 5.84765 11.2043L2.58331 9.34327C1.13255 8.51426 0.63494 6.68272 1.47104 5.24869L1.47155 5.24819ZM12.6834 7.82274L8.74157 5.57669L10.1062 4.79968C10.1199 4.79068 10.1371 4.78918 10.1523 4.79568L13.4166 6.65522C14.8699 7.48373 15.3681 9.31827 14.5284 10.7523C14.1732 11.3593 13.6138 11.8243 12.9474 12.0663V8.27575C12.9489 8.08875 12.8481 7.91574 12.6839 7.82274H12.6834ZM14.0414 5.8057C14.0176 5.7912 13.9756 5.7662 13.9457 5.7492L10.7168 3.90916C10.5531 3.81466 10.3504 3.81466 10.1863 3.90916L6.24442 6.15521V4.60017C6.2434 4.58417 6.251 4.56867 6.26367 4.55867L9.52751 2.70063C10.9813 1.87311 12.84 2.36563 13.6781 3.80066C14.0323 4.40667 14.1605 5.11618 14.0404 5.8057H14.0414ZM5.50257 8.57726L4.13744 7.79974C4.12275 7.79274 4.11312 7.77874 4.11109 7.76274V4.04316C4.11211 2.38713 5.47368 1.0451 7.15197 1.0461C7.86189 1.0461 8.54902 1.2921 9.09476 1.74011C9.06993 1.75311 9.02737 1.77661 8.99899 1.79361L5.77012 3.63365C5.60493 3.72615 5.50358 3.89916 5.50459 4.08666L5.50257 8.57626V8.57726ZM6.24391 7.00022L7.99972 5.9997L9.75553 6.99972V9.00027L7.99972 10.0003L6.24391 9.00027V7.00022Z\"\r\n        fill=\"currentColor\"\r\n      />\r\n    </svg>\r\n  );\r\n};\r\n\r\nexport const LogoGoogle = ({ size = 16 }: { size?: number }) => {\r\n  return (\r\n    <svg\r\n      data-testid=\"geist-icon\"\r\n      height={size}\r\n      strokeLinejoin=\"round\"\r\n      viewBox=\"0 0 16 16\"\r\n      width={size}\r\n      style={{ color: 'currentcolor' }}\r\n    >\r\n      <path\r\n        d=\"M8.15991 6.54543V9.64362H12.4654C12.2763 10.64 11.709 11.4837 10.8581 12.0509L13.4544 14.0655C14.9671 12.6692 15.8399 10.6182 15.8399 8.18188C15.8399 7.61461 15.789 7.06911 15.6944 6.54552L8.15991 6.54543Z\"\r\n        fill=\"#4285F4\"\r\n      />\r\n      <path\r\n        d=\"M3.6764 9.52268L3.09083 9.97093L1.01807 11.5855C2.33443 14.1963 5.03241 16 8.15966 16C10.3196 16 12.1305 15.2873 13.4542 14.0655L10.8578 12.0509C10.1451 12.5309 9.23598 12.8219 8.15966 12.8219C6.07967 12.8219 4.31245 11.4182 3.67967 9.5273L3.6764 9.52268Z\"\r\n        fill=\"#34A853\"\r\n      />\r\n      <path\r\n        d=\"M1.01803 4.41455C0.472607 5.49087 0.159912 6.70543 0.159912 7.99995C0.159912 9.29447 0.472607 10.509 1.01803 11.5854C1.01803 11.5926 3.6799 9.51991 3.6799 9.51991C3.5199 9.03991 3.42532 8.53085 3.42532 7.99987C3.42532 7.46889 3.5199 6.95983 3.6799 6.47983L1.01803 4.41455Z\"\r\n        fill=\"#FBBC05\"\r\n      />\r\n      <path\r\n        d=\"M8.15982 3.18545C9.33802 3.18545 10.3853 3.59271 11.2216 4.37818L13.5125 2.0873C12.1234 0.792777 10.3199 0 8.15982 0C5.03257 0 2.33443 1.79636 1.01807 4.41455L3.67985 6.48001C4.31254 4.58908 6.07983 3.18545 8.15982 3.18545Z\"\r\n        fill=\"#EA4335\"\r\n      />\r\n    </svg>\r\n  );\r\n};\r\n\r\nexport const LogoAnthropic = () => {\r\n  return (\r\n    <svg\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n      xmlnsXlink=\"http://www.w3.org/1999/xlink\"\r\n      x=\"0px\"\r\n      y=\"0px\"\r\n      viewBox=\"0 0 92.2 65\"\r\n      style={{ color: 'currentcolor', fill: 'currentcolor' }}\r\n      width=\"18px\"\r\n      height=\"18px\"\r\n    >\r\n      <path\r\n        d=\"M66.5,0H52.4l25.7,65h14.1L66.5,0z M25.7,0L0,65h14.4l5.3-13.6h26.9L51.8,65h14.4L40.5,0C40.5,0,25.7,0,25.7,0z\r\n\t\tM24.3,39.3l8.8-22.8l8.8,22.8H24.3z\"\r\n      />\r\n    </svg>\r\n  );\r\n};\r\n\r\nexport const RouteIcon = ({ size = 16 }: { size?: number }) => {\r\n  return (\r\n    <svg\r\n      height={size}\r\n      strokeLinejoin=\"round\"\r\n      viewBox=\"0 0 16 16\"\r\n      width={size}\r\n      style={{ color: 'currentcolor' }}\r\n    >\r\n      <path\r\n        fillRule=\"evenodd\"\r\n        clipRule=\"evenodd\"\r\n        d=\"M7.53033 0.719661L7 0.189331L5.93934 1.24999L6.46967 1.78032L6.68934 1.99999H3.375C1.51104 1.99999 0 3.51103 0 5.37499C0 7.23895 1.51104 8.74999 3.375 8.74999H12.625C13.6605 8.74999 14.5 9.58946 14.5 10.625C14.5 11.6605 13.6605 12.5 12.625 12.5H4.88555C4.56698 11.4857 3.61941 10.75 2.5 10.75C1.11929 10.75 0 11.8693 0 13.25C0 14.6307 1.11929 15.75 2.5 15.75C3.61941 15.75 4.56698 15.0143 4.88555 14H12.625C14.489 14 16 12.489 16 10.625C16 8.76103 14.489 7.24999 12.625 7.24999H3.375C2.33947 7.24999 1.5 6.41052 1.5 5.37499C1.5 4.33946 2.33947 3.49999 3.375 3.49999H6.68934L6.46967 3.71966L5.93934 4.24999L7 5.31065L7.53033 4.78032L8.85355 3.4571C9.24408 3.06657 9.24408 2.43341 8.85355 2.04288L7.53033 0.719661ZM2.5 14.25C3.05228 14.25 3.5 13.8023 3.5 13.25C3.5 12.6977 3.05228 12.25 2.5 12.25C1.94772 12.25 1.5 12.6977 1.5 13.25C1.5 13.8023 1.94772 14.25 2.5 14.25ZM14.5 2.74999C14.5 3.30228 14.0523 3.74999 13.5 3.74999C12.9477 3.74999 12.5 3.30228 12.5 2.74999C12.5 2.19771 12.9477 1.74999 13.5 1.74999C14.0523 1.74999 14.5 2.19771 14.5 2.74999ZM16 2.74999C16 4.1307 14.8807 5.24999 13.5 5.24999C12.1193 5.24999 11 4.1307 11 2.74999C11 1.36928 12.1193 0.249991 13.5 0.249991C14.8807 0.249991 16 1.36928 16 2.74999Z\"\r\n        fill=\"currentColor\"\r\n      />\r\n    </svg>\r\n  );\r\n};\r\n\r\nexport const FileIcon = ({ size = 16 }: { size?: number }) => {\r\n  return (\r\n    <svg\r\n      height={size}\r\n      strokeLinejoin=\"round\"\r\n      viewBox=\"0 0 16 16\"\r\n      width={size}\r\n      style={{ color: 'currentcolor' }}\r\n    >\r\n      <path\r\n        fillRule=\"evenodd\"\r\n        clipRule=\"evenodd\"\r\n        d=\"M14.5 13.5V6.5V5.41421C14.5 5.149 14.3946 4.89464 14.2071 4.70711L9.79289 0.292893C9.60536 0.105357 9.351 0 9.08579 0H8H3H1.5V1.5V13.5C1.5 14.8807 2.61929 16 4 16H12C13.3807 16 14.5 14.8807 14.5 13.5ZM13 13.5V6.5H9.5H8V5V1.5H3V13.5C3 14.0523 3.44772 14.5 4 14.5H12C12.5523 14.5 13 14.0523 13 13.5ZM9.5 5V2.12132L12.3787 5H9.5ZM5.13 5.00062H4.505V6.25062H5.13H6H6.625V5.00062H6H5.13ZM4.505 8H5.13H11H11.625V9.25H11H5.13H4.505V8ZM5.13 11H4.505V12.25H5.13H11H11.625V11H11H5.13Z\"\r\n        fill=\"currentColor\"\r\n      />\r\n    </svg>\r\n  );\r\n};\r\n\r\nexport const LoaderIcon = ({ size = 16 }: { size?: number }) => {\r\n  return (\r\n    <svg\r\n      height={size}\r\n      strokeLinejoin=\"round\"\r\n      viewBox=\"0 0 16 16\"\r\n      width={size}\r\n      style={{ color: 'currentcolor' }}\r\n    >\r\n      <g clipPath=\"url(#clip0_2393_1490)\">\r\n        <path d=\"M8 0V4\" stroke=\"currentColor\" strokeWidth=\"1.5\" />\r\n        <path\r\n          opacity=\"0.5\"\r\n          d=\"M8 16V12\"\r\n          stroke=\"currentColor\"\r\n          strokeWidth=\"1.5\"\r\n        />\r\n        <path\r\n          opacity=\"0.9\"\r\n          d=\"M3.29773 1.52783L5.64887 4.7639\"\r\n          stroke=\"currentColor\"\r\n          strokeWidth=\"1.5\"\r\n        />\r\n        <path\r\n          opacity=\"0.1\"\r\n          d=\"M12.7023 1.52783L10.3511 4.7639\"\r\n          stroke=\"currentColor\"\r\n          strokeWidth=\"1.5\"\r\n        />\r\n        <path\r\n          opacity=\"0.4\"\r\n          d=\"M12.7023 14.472L10.3511 11.236\"\r\n          stroke=\"currentColor\"\r\n          strokeWidth=\"1.5\"\r\n        />\r\n        <path\r\n          opacity=\"0.6\"\r\n          d=\"M3.29773 14.472L5.64887 11.236\"\r\n          stroke=\"currentColor\"\r\n          strokeWidth=\"1.5\"\r\n        />\r\n        <path\r\n          opacity=\"0.2\"\r\n          d=\"M15.6085 5.52783L11.8043 6.7639\"\r\n          stroke=\"currentColor\"\r\n          strokeWidth=\"1.5\"\r\n        />\r\n        <path\r\n          opacity=\"0.7\"\r\n          d=\"M0.391602 10.472L4.19583 9.23598\"\r\n          stroke=\"currentColor\"\r\n          strokeWidth=\"1.5\"\r\n        />\r\n        <path\r\n          opacity=\"0.3\"\r\n          d=\"M15.6085 10.4722L11.8043 9.2361\"\r\n          stroke=\"currentColor\"\r\n          strokeWidth=\"1.5\"\r\n        />\r\n        <path\r\n          opacity=\"0.8\"\r\n          d=\"M0.391602 5.52783L4.19583 6.7639\"\r\n          stroke=\"currentColor\"\r\n          strokeWidth=\"1.5\"\r\n        />\r\n      </g>\r\n      <defs>\r\n        <clipPath id=\"clip0_2393_1490\">\r\n          <rect width=\"16\" height=\"16\" fill=\"white\" />\r\n        </clipPath>\r\n      </defs>\r\n    </svg>\r\n  );\r\n};\r\n\r\nexport const UploadIcon = ({ size = 16 }: { size?: number }) => {\r\n  return (\r\n    <svg\r\n      data-testid=\"geist-icon\"\r\n      height={size}\r\n      strokeLinejoin=\"round\"\r\n      viewBox=\"0 0 16 16\"\r\n      width={size}\r\n      style={{ color: 'currentcolor' }}\r\n    >\r\n      <path\r\n        fillRule=\"evenodd\"\r\n        clipRule=\"evenodd\"\r\n        d=\"M1.5 4.875C1.5 3.01104 3.01104 1.5 4.875 1.5C6.20018 1.5 7.34838 2.26364 7.901 3.37829C8.1902 3.96162 8.79547 4.5 9.60112 4.5H12.25C13.4926 4.5 14.5 5.50736 14.5 6.75C14.5 7.42688 14.202 8.03329 13.7276 8.44689L13.1622 8.93972L14.1479 10.0704L14.7133 9.57758C15.5006 8.89123 16 7.8785 16 6.75C16 4.67893 14.3211 3 12.25 3H9.60112C9.51183 3 9.35322 2.93049 9.2449 2.71201C8.44888 1.1064 6.79184 0 4.875 0C2.18261 0 0 2.18261 0 4.875V6.40385C0 7.69502 0.598275 8.84699 1.52982 9.59656L2.11415 10.0667L3.0545 8.89808L2.47018 8.42791C1.87727 7.95083 1.5 7.22166 1.5 6.40385V4.875ZM7.29289 7.39645C7.68342 7.00592 8.31658 7.00592 8.70711 7.39645L11.7803 10.4697L12.3107 11L11.25 12.0607L10.7197 11.5303L8.75 9.56066V15.25V16H7.25V15.25V9.56066L5.28033 11.5303L4.75 12.0607L3.68934 11L4.21967 10.4697L7.29289 7.39645Z\"\r\n        fill=\"currentColor\"\r\n      />\r\n    </svg>\r\n  );\r\n};\r\n\r\nexport const MenuIcon = ({ size = 16 }: { size?: number }) => {\r\n  return (\r\n    <svg\r\n      height={size}\r\n      strokeLinejoin=\"round\"\r\n      viewBox=\"0 0 16 16\"\r\n      width={size}\r\n      style={{ color: 'currentcolor' }}\r\n    >\r\n      <path\r\n        fillRule=\"evenodd\"\r\n        clipRule=\"evenodd\"\r\n        d=\"M1 2H1.75H14.25H15V3.5H14.25H1.75H1V2ZM1 12.5H1.75H14.25H15V14H14.25H1.75H1V12.5ZM1.75 7.25H1V8.75H1.75H14.25H15V7.25H14.25H1.75Z\"\r\n        fill=\"currentColor\"\r\n      />\r\n    </svg>\r\n  );\r\n};\r\n\r\nexport const PencilEditIcon = ({ size = 16 }: { size?: number }) => {\r\n  return (\r\n    <svg\r\n      height={size}\r\n      strokeLinejoin=\"round\"\r\n      viewBox=\"0 0 16 16\"\r\n      width={size}\r\n      style={{ color: 'currentcolor' }}\r\n    >\r\n      <path\r\n        fillRule=\"evenodd\"\r\n        clipRule=\"evenodd\"\r\n        d=\"M11.75 0.189331L12.2803 0.719661L15.2803 3.71966L15.8107 4.24999L15.2803 4.78032L5.15901 14.9016C4.45575 15.6049 3.50192 16 2.50736 16H0.75H0V15.25V13.4926C0 12.4981 0.395088 11.5442 1.09835 10.841L11.2197 0.719661L11.75 0.189331ZM11.75 2.31065L9.81066 4.24999L11.75 6.18933L13.6893 4.24999L11.75 2.31065ZM2.15901 11.9016L8.75 5.31065L10.6893 7.24999L4.09835 13.841C3.67639 14.2629 3.1041 14.5 2.50736 14.5H1.5V13.4926C1.5 12.8959 1.73705 12.3236 2.15901 11.9016ZM9 16H16V14.5H9V16Z\"\r\n        fill=\"currentColor\"\r\n      />\r\n    </svg>\r\n  );\r\n};\r\n\r\nexport const CheckedSquare = ({ size = 16 }: { size?: number }) => {\r\n  return (\r\n    <svg\r\n      height={size}\r\n      strokeLinejoin=\"round\"\r\n      viewBox=\"0 0 16 16\"\r\n      width={size}\r\n      style={{ color: 'currentcolor' }}\r\n    >\r\n      <path\r\n        fillRule=\"evenodd\"\r\n        clipRule=\"evenodd\"\r\n        d=\"M15 16H1C0.447715 16 0 15.5523 0 15V1C0 0.447715 0.447716 0 1 0L15 8.17435e-06C15.5523 8.47532e-06 16 0.447724 16 1.00001V15C16 15.5523 15.5523 16 15 16ZM11.7803 6.28033L12.3107 5.75L11.25 4.68934L10.7197 5.21967L6.5 9.43935L5.28033 8.21967L4.75001 7.68934L3.68934 8.74999L4.21967 9.28033L5.96967 11.0303C6.11032 11.171 6.30109 11.25 6.5 11.25C6.69891 11.25 6.88968 11.171 7.03033 11.0303L11.7803 6.28033Z\"\r\n        fill=\"currentColor\"\r\n      />\r\n    </svg>\r\n  );\r\n};\r\n\r\nexport const UncheckedSquare = ({ size = 16 }: { size?: number }) => {\r\n  return (\r\n    <svg\r\n      height={size}\r\n      strokeLinejoin=\"round\"\r\n      viewBox=\"0 0 16 16\"\r\n      width={size}\r\n      style={{ color: 'currentcolor' }}\r\n    >\r\n      <rect\r\n        x=\"1\"\r\n        y=\"1\"\r\n        width=\"14\"\r\n        height=\"14\"\r\n        stroke=\"currentColor\"\r\n        strokeWidth=\"1.5\"\r\n        fill=\"none\"\r\n      />\r\n    </svg>\r\n  );\r\n};\r\n\r\nexport const MoreIcon = ({ size = 16 }: { size?: number }) => {\r\n  return (\r\n    <svg\r\n      height={size}\r\n      strokeLinejoin=\"round\"\r\n      viewBox=\"0 0 16 16\"\r\n      width={size}\r\n      style={{ color: 'currentcolor' }}\r\n    >\r\n      <path\r\n        fillRule=\"evenodd\"\r\n        clipRule=\"evenodd\"\r\n        d=\"M8 4C7.17157 4 6.5 3.32843 6.5 2.5C6.5 1.67157 7.17157 1 8 1C8.82843 1 9.5 1.67157 9.5 2.5C9.5 3.32843 8.82843 4 8 4ZM8 9.5C7.17157 9.5 6.5 8.82843 6.5 8C6.5 7.17157 7.17157 6.5 8 6.5C8.82843 6.5 9.5 7.17157 9.5 8C9.5 8.82843 8.82843 9.5 8 9.5ZM6.5 13.5C6.5 14.3284 7.17157 15 8 15C8.82843 15 9.5 14.3284 9.5 13.5C9.5 12.6716 8.82843 12 8 12C7.17157 12 6.5 12.6716 6.5 13.5Z\"\r\n        fill=\"currentColor\"\r\n      />\r\n    </svg>\r\n  );\r\n};\r\n\r\nexport const TrashIcon = ({ size = 16 }: { size?: number }) => {\r\n  return (\r\n    <svg\r\n      height={size}\r\n      strokeLinejoin=\"round\"\r\n      viewBox=\"0 0 16 16\"\r\n      width={size}\r\n      style={{ color: 'currentcolor' }}\r\n    >\r\n      <path\r\n        fillRule=\"evenodd\"\r\n        clipRule=\"evenodd\"\r\n        d=\"M6.75 2.75C6.75 2.05964 7.30964 1.5 8 1.5C8.69036 1.5 9.25 2.05964 9.25 2.75V3H6.75V2.75ZM5.25 3V2.75C5.25 1.23122 6.48122 0 8 0C9.51878 0 10.75 1.23122 10.75 2.75V3H12.9201H14.25H15V4.5H14.25H13.8846L13.1776 13.6917C13.0774 14.9942 11.9913 16 10.6849 16H5.31508C4.00874 16 2.92263 14.9942 2.82244 13.6917L2.11538 4.5H1.75H1V3H1.75H3.07988H5.25ZM4.31802 13.5767L3.61982 4.5H12.3802L11.682 13.5767C11.6419 14.0977 11.2075 14.5 10.6849 14.5H5.31508C4.79254 14.5 4.3581 14.0977 4.31802 13.5767Z\"\r\n        fill=\"currentColor\"\r\n      />\r\n    </svg>\r\n  );\r\n};\r\n\r\nexport const InfoIcon = ({ size = 16 }: { size?: number }) => {\r\n  return (\r\n    <svg\r\n      height={size}\r\n      strokeLinejoin=\"round\"\r\n      viewBox=\"0 0 16 16\"\r\n      width={size}\r\n      style={{ color: 'currentcolor' }}\r\n    >\r\n      <path\r\n        fillRule=\"evenodd\"\r\n        clipRule=\"evenodd\"\r\n        d=\"M16 8C16 12.4183 12.4183 16 8 16C3.58172 16 0 12.4183 0 8C0 3.58172 3.58172 0 8 0C12.4183 0 16 3.58172 16 8ZM6.25002 7H7.00002H7.75C8.30229 7 8.75 7.44772 8.75 8V11.5V12.25H7.25V11.5V8.5H7.00002H6.25002V7ZM8 6C8.55229 6 9 5.55228 9 5C9 4.44772 8.55229 4 8 4C7.44772 4 7 4.44772 7 5C7 5.55228 7.44772 6 8 6Z\"\r\n        fill=\"currentColor\"\r\n      />\r\n    </svg>\r\n  );\r\n};\r\n\r\nexport const ArrowUpIcon = ({ size = 16 }: { size?: number }) => {\r\n  return (\r\n    <svg\r\n      height={size}\r\n      strokeLinejoin=\"round\"\r\n      viewBox=\"0 0 16 16\"\r\n      width={size}\r\n      style={{ color: 'currentcolor' }}\r\n    >\r\n      <path\r\n        fillRule=\"evenodd\"\r\n        clipRule=\"evenodd\"\r\n        d=\"M8.70711 1.39644C8.31659 1.00592 7.68342 1.00592 7.2929 1.39644L2.21968 6.46966L1.68935 6.99999L2.75001 8.06065L3.28034 7.53032L7.25001 3.56065V14.25V15H8.75001V14.25V3.56065L12.7197 7.53032L13.25 8.06065L14.3107 6.99999L13.7803 6.46966L8.70711 1.39644Z\"\r\n        fill=\"currentColor\"\r\n      />\r\n    </svg>\r\n  );\r\n};\r\n\r\nexport const StopIcon = ({ size = 16 }: { size?: number }) => {\r\n  return (\r\n    <svg\r\n      height={size}\r\n      viewBox=\"0 0 16 16\"\r\n      width={size}\r\n      style={{ color: 'currentcolor' }}\r\n    >\r\n      <path\r\n        fillRule=\"evenodd\"\r\n        clipRule=\"evenodd\"\r\n        d=\"M3 3H13V13H3V3Z\"\r\n        fill=\"currentColor\"\r\n      />\r\n    </svg>\r\n  );\r\n};\r\n\r\nexport const PaperclipIcon = ({ size = 16 }: { size?: number }) => {\r\n  return (\r\n    <svg\r\n      height={size}\r\n      strokeLinejoin=\"round\"\r\n      viewBox=\"0 0 16 16\"\r\n      width={size}\r\n      style={{ color: 'currentcolor' }}\r\n      className=\"-rotate-45\"\r\n    >\r\n      <path\r\n        fillRule=\"evenodd\"\r\n        clipRule=\"evenodd\"\r\n        d=\"M10.8591 1.70735C10.3257 1.70735 9.81417 1.91925 9.437 2.29643L3.19455 8.53886C2.56246 9.17095 2.20735 10.0282 2.20735 10.9222C2.20735 11.8161 2.56246 12.6734 3.19455 13.3055C3.82665 13.9376 4.68395 14.2927 5.57786 14.2927C6.47178 14.2927 7.32908 13.9376 7.96117 13.3055L14.2036 7.06304L14.7038 6.56287L15.7041 7.56321L15.204 8.06337L8.96151 14.3058C8.06411 15.2032 6.84698 15.7074 5.57786 15.7074C4.30875 15.7074 3.09162 15.2032 2.19422 14.3058C1.29682 13.4084 0.792664 12.1913 0.792664 10.9222C0.792664 9.65305 1.29682 8.43592 2.19422 7.53852L8.43666 1.29609C9.07914 0.653606 9.95054 0.292664 10.8591 0.292664C11.7678 0.292664 12.6392 0.653606 13.2816 1.29609C13.9241 1.93857 14.2851 2.80997 14.2851 3.71857C14.2851 4.62718 13.9241 5.49858 13.2816 6.14106L13.2814 6.14133L7.0324 12.3835C7.03231 12.3836 7.03222 12.3837 7.03213 12.3838C6.64459 12.7712 6.11905 12.9888 5.57107 12.9888C5.02297 12.9888 4.49731 12.7711 4.10974 12.3835C3.72217 11.9959 3.50444 11.4703 3.50444 10.9222C3.50444 10.3741 3.72217 9.8484 4.10974 9.46084L4.11004 9.46054L9.877 3.70039L10.3775 3.20051L11.3772 4.20144L10.8767 4.70131L5.11008 10.4612C5.11005 10.4612 5.11003 10.4612 5.11 10.4613C4.98779 10.5835 4.91913 10.7493 4.91913 10.9222C4.91913 11.0951 4.98782 11.2609 5.11008 11.3832C5.23234 11.5054 5.39817 11.5741 5.57107 11.5741C5.74398 11.5741 5.9098 11.5054 6.03206 11.3832L6.03233 11.3829L12.2813 5.14072C12.2814 5.14063 12.2815 5.14054 12.2816 5.14045C12.6586 4.7633 12.8704 4.25185 12.8704 3.71857C12.8704 3.18516 12.6585 2.6736 12.2813 2.29643C11.9041 1.91925 11.3926 1.70735 10.8591 1.70735Z\"\r\n        fill=\"currentColor\"\r\n      />\r\n    </svg>\r\n  );\r\n};\r\n\r\nexport const MoreHorizontalIcon = ({ size = 16 }: { size?: number }) => {\r\n  return (\r\n    <svg\r\n      height={size}\r\n      strokeLinejoin=\"round\"\r\n      viewBox=\"0 0 16 16\"\r\n      width={size}\r\n      style={{ color: 'currentcolor' }}\r\n    >\r\n      <path\r\n        fillRule=\"evenodd\"\r\n        clipRule=\"evenodd\"\r\n        d=\"M4 8C4 8.82843 3.32843 9.5 2.5 9.5C1.67157 9.5 1 8.82843 1 8C1 7.17157 1.67157 6.5 2.5 6.5C3.32843 6.5 4 7.17157 4 8ZM9.5 8C9.5 8.82843 8.82843 9.5 8 9.5C7.17157 9.5 6.5 8.82843 6.5 8C6.5 7.17157 7.17157 6.5 8 6.5C8.82843 6.5 9.5 7.17157 9.5 8ZM13.5 9.5C14.3284 9.5 15 8.82843 15 8C15 7.17157 14.3284 6.5 13.5 6.5C12.6716 6.5 12 7.17157 12 8C12 8.82843 12.6716 9.5 13.5 9.5Z\"\r\n        fill=\"currentColor\"\r\n      />\r\n    </svg>\r\n  );\r\n};\r\n\r\nexport const MessageIcon = ({ size = 16 }: { size?: number }) => {\r\n  return (\r\n    <svg\r\n      height={size}\r\n      strokeLinejoin=\"round\"\r\n      viewBox=\"0 0 16 16\"\r\n      width={size}\r\n      style={{ color: 'currentcolor' }}\r\n    >\r\n      <path\r\n        fillRule=\"evenodd\"\r\n        clipRule=\"evenodd\"\r\n        d=\"M2.8914 10.4028L2.98327 10.6318C3.22909 11.2445 3.5 12.1045 3.5 13C3.5 13.3588 3.4564 13.7131 3.38773 14.0495C3.69637 13.9446 4.01409 13.8159 4.32918 13.6584C4.87888 13.3835 5.33961 13.0611 5.70994 12.7521L6.22471 12.3226L6.88809 12.4196C7.24851 12.4724 7.61994 12.5 8 12.5C11.7843 12.5 14.5 9.85569 14.5 7C14.5 4.14431 11.7843 1.5 8 1.5C4.21574 1.5 1.5 4.14431 1.5 7C1.5 8.18175 1.94229 9.29322 2.73103 10.2153L2.8914 10.4028ZM2.8135 15.7653C1.76096 16 1 16 1 16C1 16 1.43322 15.3097 1.72937 14.4367C1.88317 13.9834 2 13.4808 2 13C2 12.3826 1.80733 11.7292 1.59114 11.1903C0.591845 10.0221 0 8.57152 0 7C0 3.13401 3.58172 0 8 0C12.4183 0 16 3.13401 16 7C16 10.866 12.4183 14 8 14C7.54721 14 7.10321 13.9671 6.67094 13.9038C6.22579 14.2753 5.66881 14.6656 5 15C4.23366 15.3832 3.46733 15.6195 2.8135 15.7653Z\"\r\n        fill=\"currentColor\"\r\n      />\r\n    </svg>\r\n  );\r\n};\r\n\r\nexport const CrossIcon = ({ size = 16 }: { size?: number }) => (\r\n  <svg\r\n    height={size}\r\n    strokeLinejoin=\"round\"\r\n    viewBox=\"0 0 16 16\"\r\n    width={size}\r\n    style={{ color: 'currentcolor' }}\r\n  >\r\n    <path\r\n      fillRule=\"evenodd\"\r\n      clipRule=\"evenodd\"\r\n      d=\"M12.4697 13.5303L13 14.0607L14.0607 13L13.5303 12.4697L9.06065 7.99999L13.5303 3.53032L14.0607 2.99999L13 1.93933L12.4697 2.46966L7.99999 6.93933L3.53032 2.46966L2.99999 1.93933L1.93933 2.99999L2.46966 3.53032L6.93933 7.99999L2.46966 12.4697L1.93933 13L2.99999 14.0607L3.53032 13.5303L7.99999 9.06065L12.4697 13.5303Z\"\r\n      fill=\"currentColor\"\r\n    />\r\n  </svg>\r\n);\r\n\r\nexport const CrossSmallIcon = ({ size = 16 }: { size?: number }) => (\r\n  <svg\r\n    height={size}\r\n    strokeLinejoin=\"round\"\r\n    viewBox=\"0 0 16 16\"\r\n    width={size}\r\n    style={{ color: 'currentcolor' }}\r\n  >\r\n    <path\r\n      fillRule=\"evenodd\"\r\n      clipRule=\"evenodd\"\r\n      d=\"M9.96966 11.0303L10.5 11.5607L11.5607 10.5L11.0303 9.96966L9.06065 7.99999L11.0303 6.03032L11.5607 5.49999L10.5 4.43933L9.96966 4.96966L7.99999 6.93933L6.03032 4.96966L5.49999 4.43933L4.43933 5.49999L4.96966 6.03032L6.93933 7.99999L4.96966 9.96966L4.43933 10.5L5.49999 11.5607L6.03032 11.0303L7.99999 9.06065L9.96966 11.0303Z\"\r\n      fill=\"currentColor\"\r\n    ></path>\r\n  </svg>\r\n);\r\n\r\nexport const UndoIcon = ({ size = 16 }: { size?: number }) => (\r\n  <svg\r\n    height={size}\r\n    strokeLinejoin=\"round\"\r\n    viewBox=\"0 0 16 16\"\r\n    width={size}\r\n    style={{ color: 'currentcolor' }}\r\n  >\r\n    <path\r\n      fillRule=\"evenodd\"\r\n      clipRule=\"evenodd\"\r\n      d=\"M13.5 8C13.5 4.96643 11.0257 2.5 7.96452 2.5C5.42843 2.5 3.29365 4.19393 2.63724 6.5H5.25H6V8H5.25H0.75C0.335787 8 0 7.66421 0 7.25V2.75V2H1.5V2.75V5.23347C2.57851 2.74164 5.06835 1 7.96452 1C11.8461 1 15 4.13001 15 8C15 11.87 11.8461 15 7.96452 15C5.62368 15 3.54872 13.8617 2.27046 12.1122L1.828 11.5066L3.03915 10.6217L3.48161 11.2273C4.48831 12.6051 6.12055 13.5 7.96452 13.5C11.0257 13.5 13.5 11.0336 13.5 8Z\"\r\n      fill=\"currentColor\"\r\n    />\r\n  </svg>\r\n);\r\n\r\nexport const RedoIcon = ({ size = 16 }: { size?: number }) => (\r\n  <svg\r\n    height={size}\r\n    strokeLinejoin=\"round\"\r\n    viewBox=\"0 0 16 16\"\r\n    width={size}\r\n    style={{ color: 'currentcolor' }}\r\n  >\r\n    <path\r\n      fillRule=\"evenodd\"\r\n      clipRule=\"evenodd\"\r\n      d=\"M2.5 8C2.5 4.96643 4.97431 2.5 8.03548 2.5C10.5716 2.5 12.7064 4.19393 13.3628 6.5H10.75H10V8H10.75H15.25C15.6642 8 16 7.66421 16 7.25V2.75V2H14.5V2.75V5.23347C13.4215 2.74164 10.9316 1 8.03548 1C4.1539 1 1 4.13001 1 8C1 11.87 4.1539 15 8.03548 15C10.3763 15 12.4513 13.8617 13.7295 12.1122L14.172 11.5066L12.9609 10.6217L12.5184 11.2273C11.5117 12.6051 9.87945 13.5 8.03548 13.5C4.97431 13.5 2.5 11.0336 2.5 8Z\"\r\n      fill=\"currentColor\"\r\n    />\r\n  </svg>\r\n);\r\n\r\nexport const DeltaIcon = ({ size = 16 }: { size?: number }) => (\r\n  <svg\r\n    height={size}\r\n    strokeLinejoin=\"round\"\r\n    viewBox=\"0 0 16 16\"\r\n    width={size}\r\n    style={{ color: 'currentcolor' }}\r\n  >\r\n    <path\r\n      fillRule=\"evenodd\"\r\n      clipRule=\"evenodd\"\r\n      d=\"M2.67705 15H1L1.75 13.5L6.16147 4.67705L6.15836 4.67082L6.16667 4.66667L7.16147 2.67705L8 1L8.83853 2.67705L14.25 13.5L15 15H13.3229H2.67705ZM7 6.3541L10.5729 13.5H3.42705L7 6.3541Z\"\r\n      fill=\"currentColor\"\r\n    />\r\n  </svg>\r\n);\r\n\r\nexport const PenIcon = ({ size = 16 }: { size?: number }) => (\r\n  <svg\r\n    height={size}\r\n    strokeLinejoin=\"round\"\r\n    viewBox=\"0 0 16 16\"\r\n    width={size}\r\n    style={{ color: 'currentcolor' }}\r\n  >\r\n    <path\r\n      fillRule=\"evenodd\"\r\n      clipRule=\"evenodd\"\r\n      d=\"M8.75 0.189331L9.28033 0.719661L15.2803 6.71966L15.8107 7.24999L15.2803 7.78032L13.7374 9.32322C13.1911 9.8696 12.3733 9.97916 11.718 9.65188L9.54863 13.5568C8.71088 15.0648 7.12143 16 5.39639 16H0.75H0V15.25V10.6036C0 8.87856 0.935237 7.28911 2.4432 6.45136L6.34811 4.28196C6.02084 3.62674 6.13039 2.80894 6.67678 2.26255L8.21967 0.719661L8.75 0.189331ZM7.3697 5.43035L10.5696 8.63029L8.2374 12.8283C7.6642 13.8601 6.57668 14.5 5.39639 14.5H2.56066L5.53033 11.5303L4.46967 10.4697L1.5 13.4393V10.6036C1.5 9.42331 2.1399 8.33579 3.17166 7.76259L7.3697 5.43035ZM12.6768 8.26256C12.5791 8.36019 12.4209 8.36019 12.3232 8.26255L12.0303 7.96966L8.03033 3.96966L7.73744 3.67677C7.63981 3.57914 7.63981 3.42085 7.73744 3.32321L8.75 2.31065L13.6893 7.24999L12.6768 8.26256Z\"\r\n      fill=\"currentColor\"\r\n    />\r\n  </svg>\r\n);\r\n\r\nexport const SummarizeIcon = ({ size = 16 }: { size?: number }) => (\r\n  <svg\r\n    height={size}\r\n    strokeLinejoin=\"round\"\r\n    viewBox=\"0 0 16 16\"\r\n    width={size}\r\n    style={{ color: 'currentcolor' }}\r\n  >\r\n    <path\r\n      fillRule=\"evenodd\"\r\n      clipRule=\"evenodd\"\r\n      d=\"M1.75 12H1V10.5H1.75H5.25H6V12H5.25H1.75ZM1.75 7.75H1V6.25H1.75H4.25H5V7.75H4.25H1.75ZM1.75 3.5H1V2H1.75H7.25H8V3.5H7.25H1.75ZM12.5303 14.7803C12.2374 15.0732 11.7626 15.0732 11.4697 14.7803L9.21967 12.5303L8.68934 12L9.75 10.9393L10.2803 11.4697L11.25 12.4393V2.75V2H12.75V2.75V12.4393L13.7197 11.4697L14.25 10.9393L15.3107 12L14.7803 12.5303L12.5303 14.7803Z\"\r\n      fill=\"currentColor\"\r\n    />\r\n  </svg>\r\n);\r\n\r\nexport const SidebarLeftIcon = ({ size = 16 }: { size?: number }) => (\r\n  <svg\r\n    height={size}\r\n    strokeLinejoin=\"round\"\r\n    viewBox=\"0 0 16 16\"\r\n    width={size}\r\n    style={{ color: 'currentcolor' }}\r\n  >\r\n    <path\r\n      fillRule=\"evenodd\"\r\n      clipRule=\"evenodd\"\r\n      d=\"M6.245 2.5H14.5V12.5C14.5 13.0523 14.0523 13.5 13.5 13.5H6.245V2.5ZM4.995 2.5H1.5V12.5C1.5 13.0523 1.94772 13.5 2.5 13.5H4.995V2.5ZM0 1H1.5H14.5H16V2.5V12.5C16 13.8807 14.8807 15 13.5 15H2.5C1.11929 15 0 13.8807 0 12.5V2.5V1Z\"\r\n      fill=\"currentColor\"\r\n    />\r\n  </svg>\r\n);\r\n\r\nexport const PlusIcon = ({ size = 16 }: { size?: number }) => (\r\n  <svg\r\n    height={size}\r\n    strokeLinejoin=\"round\"\r\n    viewBox=\"0 0 16 16\"\r\n    width={size}\r\n    style={{ color: 'currentcolor' }}\r\n  >\r\n    <path\r\n      fillRule=\"evenodd\"\r\n      clipRule=\"evenodd\"\r\n      d=\"M8.75 1.75V1H7.25V1.75V6.75H2.25H1.5V8.25H2.25H7.25V13.25V14H8.75V13.25V8.25H13.75H14.5V6.75H13.75H8.75V1.75Z\"\r\n      fill=\"currentColor\"\r\n    />\r\n  </svg>\r\n);\r\n\r\nexport const CopyIcon = ({ size = 16 }: { size?: number }) => (\r\n  <svg\r\n    height={size}\r\n    strokeLinejoin=\"round\"\r\n    viewBox=\"0 0 16 16\"\r\n    width={size}\r\n    style={{ color: 'currentcolor' }}\r\n  >\r\n    <path\r\n      fillRule=\"evenodd\"\r\n      clipRule=\"evenodd\"\r\n      d=\"M2.75 0.5C1.7835 0.5 1 1.2835 1 2.25V9.75C1 10.7165 1.7835 11.5 2.75 11.5H3.75H4.5V10H3.75H2.75C2.61193 10 2.5 9.88807 2.5 9.75V2.25C2.5 2.11193 2.61193 2 2.75 2H8.25C8.38807 2 8.5 2.11193 8.5 2.25V3H10V2.25C10 1.2835 9.2165 0.5 8.25 0.5H2.75ZM7.75 4.5C6.7835 4.5 6 5.2835 6 6.25V13.75C6 14.7165 6.7835 15.5 7.75 15.5H13.25C14.2165 15.5 15 14.7165 15 13.75V6.25C15 5.2835 14.2165 4.5 13.25 4.5H7.75ZM7.5 6.25C7.5 6.11193 7.61193 6 7.75 6H13.25C13.3881 6 13.5 6.11193 13.5 6.25V13.75C13.5 13.8881 13.3881 14 13.25 14H7.75C7.61193 14 7.5 13.8881 7.5 13.75V6.25Z\"\r\n      fill=\"currentColor\"\r\n    />\r\n  </svg>\r\n);\r\n\r\nexport const ThumbUpIcon = ({ size = 16 }: { size?: number }) => (\r\n  <svg\r\n    height={size}\r\n    strokeLinejoin=\"round\"\r\n    viewBox=\"0 0 16 16\"\r\n    width={size}\r\n    style={{ color: 'currentcolor' }}\r\n  >\r\n    <path\r\n      fillRule=\"evenodd\"\r\n      clipRule=\"evenodd\"\r\n      d=\"M6.89531 2.23972C6.72984 2.12153 6.5 2.23981 6.5 2.44315V5.25001C6.5 6.21651 5.7165 7.00001 4.75 7.00001H2.5V13.5H12.1884C12.762 13.5 13.262 13.1096 13.4011 12.5532L14.4011 8.55318C14.5984 7.76425 14.0017 7.00001 13.1884 7.00001H9.25H8.5V6.25001V3.51458C8.5 3.43384 8.46101 3.35807 8.39531 3.31114L6.89531 2.23972ZM5 2.44315C5 1.01975 6.6089 0.191779 7.76717 1.01912L9.26717 2.09054C9.72706 2.41904 10 2.94941 10 3.51458V5.50001H13.1884C14.9775 5.50001 16.2903 7.18133 15.8563 8.91698L14.8563 12.917C14.5503 14.1412 13.4503 15 12.1884 15H1.75H1V14.25V6.25001V5.50001H1.75H4.75C4.88807 5.50001 5 5.38808 5 5.25001V2.44315Z\"\r\n      fill=\"currentColor\"\r\n    />\r\n  </svg>\r\n);\r\n\r\nexport const ThumbDownIcon = ({ size = 16 }: { size?: number }) => (\r\n  <svg\r\n    height={size}\r\n    strokeLinejoin=\"round\"\r\n    viewBox=\"0 0 16 16\"\r\n    width={size}\r\n    style={{ color: 'currentcolor' }}\r\n  >\r\n    <path\r\n      fillRule=\"evenodd\"\r\n      clipRule=\"evenodd\"\r\n      d=\"M6.89531 13.7603C6.72984 13.8785 6.5 13.7602 6.5 13.5569V10.75C6.5 9.7835 5.7165 9 4.75 9H2.5V2.5H12.1884C12.762 2.5 13.262 2.89037 13.4011 3.44683L14.4011 7.44683C14.5984 8.23576 14.0017 9 13.1884 9H9.25H8.5V9.75V12.4854C8.5 12.5662 8.46101 12.6419 8.39531 12.6889L6.89531 13.7603ZM5 13.5569C5 14.9803 6.6089 15.8082 7.76717 14.9809L9.26717 13.9095C9.72706 13.581 10 13.0506 10 12.4854V10.5H13.1884C14.9775 10.5 16.2903 8.81868 15.8563 7.08303L14.8563 3.08303C14.5503 1.85882 13.4503 1 12.1884 1H1.75H1V1.75V9.75V10.5H1.75H4.75C4.88807 10.5 5 10.6119 5 10.75V13.5569Z\"\r\n      fill=\"currentColor\"\r\n    />\r\n  </svg>\r\n);\r\n\r\nexport const ChevronDownIcon = ({ size = 16 }: { size?: number }) => (\r\n  <svg\r\n    height={size}\r\n    strokeLinejoin=\"round\"\r\n    viewBox=\"0 0 16 16\"\r\n    width={size}\r\n    style={{ color: 'currentcolor' }}\r\n  >\r\n    <path\r\n      fillRule=\"evenodd\"\r\n      clipRule=\"evenodd\"\r\n      d=\"M12.0607 6.74999L11.5303 7.28032L8.7071 10.1035C8.31657 10.4941 7.68341 10.4941 7.29288 10.1035L4.46966 7.28032L3.93933 6.74999L4.99999 5.68933L5.53032 6.21966L7.99999 8.68933L10.4697 6.21966L11 5.68933L12.0607 6.74999Z\"\r\n      fill=\"currentColor\"\r\n    />\r\n  </svg>\r\n);\r\n\r\nexport const SparklesIcon = ({ size = 16 }: { size?: number }) => (\r\n  <svg\r\n    height={size}\r\n    strokeLinejoin=\"round\"\r\n    viewBox=\"0 0 16 16\"\r\n    width={size}\r\n    style={{ color: 'currentcolor' }}\r\n  >\r\n    <path\r\n      d=\"M2.5 0.5V0H3.5V0.5C3.5 1.60457 4.39543 2.5 5.5 2.5H6V3V3.5H5.5C4.39543 3.5 3.5 4.39543 3.5 5.5V6H3H2.5V5.5C2.5 4.39543 1.60457 3.5 0.5 3.5H0V3V2.5H0.5C1.60457 2.5 2.5 1.60457 2.5 0.5Z\"\r\n      fill=\"currentColor\"\r\n    />\r\n    <path\r\n      d=\"M14.5 4.5V5H13.5V4.5C13.5 3.94772 13.0523 3.5 12.5 3.5H12V3V2.5H12.5C13.0523 2.5 13.5 2.05228 13.5 1.5V1H14H14.5V1.5C14.5 2.05228 14.9477 2.5 15.5 2.5H16V3V3.5H15.5C14.9477 3.5 14.5 3.94772 14.5 4.5Z\"\r\n      fill=\"currentColor\"\r\n    />\r\n    <path\r\n      d=\"M8.40706 4.92939L8.5 4H9.5L9.59294 4.92939C9.82973 7.29734 11.7027 9.17027 14.0706 9.40706L15 9.5V10.5L14.0706 10.5929C11.7027 10.8297 9.82973 12.7027 9.59294 15.0706L9.5 16H8.5L8.40706 15.0706C8.17027 12.7027 6.29734 10.8297 3.92939 10.5929L3 10.5V9.5L3.92939 9.40706C6.29734 9.17027 8.17027 7.29734 8.40706 4.92939Z\"\r\n      fill=\"currentColor\"\r\n    />\r\n  </svg>\r\n);\r\n\r\nexport const CheckCircleFillIcon = ({ size = 16 }: { size?: number }) => {\r\n  return (\r\n    <svg\r\n      height={size}\r\n      strokeLinejoin=\"round\"\r\n      viewBox=\"0 0 16 16\"\r\n      width={size}\r\n      style={{ color: 'currentcolor' }}\r\n    >\r\n      <path\r\n        fillRule=\"evenodd\"\r\n        clipRule=\"evenodd\"\r\n        d=\"M16 8C16 12.4183 12.4183 16 8 16C3.58172 16 0 12.4183 0 8C0 3.58172 3.58172 0 8 0C12.4183 0 16 3.58172 16 8ZM11.5303 6.53033L12.0607 6L11 4.93934L10.4697 5.46967L6.5 9.43934L5.53033 8.46967L5 7.93934L3.93934 9L4.46967 9.53033L5.96967 11.0303C6.26256 11.3232 6.73744 11.3232 7.03033 11.0303L11.5303 6.53033Z\"\r\n        fill=\"currentColor\"\r\n      />\r\n    </svg>\r\n  );\r\n};\r\n\r\nexport const GlobeIcon = ({ size = 16 }: { size?: number }) => {\r\n  return (\r\n    <svg\r\n      height={size}\r\n      strokeLinejoin=\"round\"\r\n      viewBox=\"0 0 16 16\"\r\n      width={size}\r\n      style={{ color: 'currentcolor' }}\r\n    >\r\n      <path\r\n        fillRule=\"evenodd\"\r\n        clipRule=\"evenodd\"\r\n        d=\"M10.268 14.0934C11.9051 13.4838 13.2303 12.2333 13.9384 10.6469C13.1192 10.7941 12.2138 10.9111 11.2469 10.9925C11.0336 12.2005 10.695 13.2621 10.268 14.0934ZM8 16C12.4183 16 16 12.4183 16 8C16 3.58172 12.4183 0 8 0C3.58172 0 0 3.58172 0 8C0 12.4183 3.58172 16 8 16ZM8.48347 14.4823C8.32384 14.494 8.16262 14.5 8 14.5C7.83738 14.5 7.67616 14.494 7.51654 14.4823C7.5132 14.4791 7.50984 14.4759 7.50647 14.4726C7.2415 14.2165 6.94578 13.7854 6.67032 13.1558C6.41594 12.5744 6.19979 11.8714 6.04101 11.0778C6.67605 11.1088 7.33104 11.125 8 11.125C8.66896 11.125 9.32395 11.1088 9.95899 11.0778C9.80021 11.8714 9.58406 12.5744 9.32968 13.1558C9.05422 13.7854 8.7585 14.2165 8.49353 14.4726C8.49016 14.4759 8.4868 14.4791 8.48347 14.4823ZM11.4187 9.72246C12.5137 9.62096 13.5116 9.47245 14.3724 9.28806C14.4561 8.87172 14.5 8.44099 14.5 8C14.5 7.55901 14.4561 7.12828 14.3724 6.71194C13.5116 6.52755 12.5137 6.37904 11.4187 6.27753C11.4719 6.83232 11.5 7.40867 11.5 8C11.5 8.59133 11.4719 9.16768 11.4187 9.72246ZM10.1525 6.18401C10.2157 6.75982 10.25 7.36805 10.25 8C10.25 8.63195 10.2157 9.24018 10.1525 9.81598C9.46123 9.85455 8.7409 9.875 8 9.875C7.25909 9.875 6.53877 9.85455 5.84749 9.81598C5.7843 9.24018 5.75 8.63195 5.75 8C5.75 7.36805 5.7843 6.75982 5.84749 6.18401C6.53877 6.14545 7.25909 6.125 8 6.125C8.74091 6.125 9.46123 6.14545 10.1525 6.18401ZM11.2469 5.00748C12.2138 5.08891 13.1191 5.20593 13.9384 5.35306C13.2303 3.7667 11.9051 2.51622 10.268 1.90662C10.695 2.73788 11.0336 3.79953 11.2469 5.00748ZM8.48347 1.51771C8.4868 1.52089 8.49016 1.52411 8.49353 1.52737C8.7585 1.78353 9.05422 2.21456 9.32968 2.84417C9.58406 3.42562 9.80021 4.12856 9.95899 4.92219C9.32395 4.89118 8.66896 4.875 8 4.875C7.33104 4.875 6.67605 4.89118 6.04101 4.92219C6.19978 4.12856 6.41594 3.42562 6.67032 2.84417C6.94578 2.21456 7.2415 1.78353 7.50647 1.52737C7.50984 1.52411 7.51319 1.52089 7.51653 1.51771C7.67615 1.50597 7.83738 1.5 8 1.5C8.16262 1.5 8.32384 1.50597 8.48347 1.51771ZM5.73202 1.90663C4.0949 2.51622 2.76975 3.7667 2.06159 5.35306C2.88085 5.20593 3.78617 5.08891 4.75309 5.00748C4.96639 3.79953 5.30497 2.73788 5.73202 1.90663ZM4.58133 6.27753C3.48633 6.37904 2.48837 6.52755 1.62761 6.71194C1.54392 7.12828 1.5 7.55901 1.5 8C1.5 8.44099 1.54392 8.87172 1.62761 9.28806C2.48837 9.47245 3.48633 9.62096 4.58133 9.72246C4.52807 9.16768 4.5 8.59133 4.5 8C4.5 7.40867 4.52807 6.83232 4.58133 6.27753ZM4.75309 10.9925C3.78617 10.9111 2.88085 10.7941 2.06159 10.6469C2.76975 12.2333 4.0949 13.4838 5.73202 14.0934C5.30497 13.2621 4.96639 12.2005 4.75309 10.9925Z\"\r\n        fill=\"currentColor\"\r\n      ></path>\r\n    </svg>\r\n  );\r\n};\r\n\r\nexport const LockIcon = ({ size = 16 }: { size?: number }) => {\r\n  return (\r\n    <svg\r\n      height={size}\r\n      strokeLinejoin=\"round\"\r\n      viewBox=\"0 0 16 16\"\r\n      width={size}\r\n      style={{ color: 'currentcolor' }}\r\n    >\r\n      <path\r\n        fillRule=\"evenodd\"\r\n        clipRule=\"evenodd\"\r\n        d=\"M10 4.5V6H6V4.5C6 3.39543 6.89543 2.5 8 2.5C9.10457 2.5 10 3.39543 10 4.5ZM4.5 6V4.5C4.5 2.567 6.067 1 8 1C9.933 1 11.5 2.567 11.5 4.5V6H12.5H14V7.5V12.5C14 13.8807 12.8807 15 11.5 15H4.5C3.11929 15 2 13.8807 2 12.5V7.5V6H3.5H4.5ZM11.5 7.5H10H6H4.5H3.5V12.5C3.5 13.0523 3.94772 13.5 4.5 13.5H11.5C12.0523 13.5 12.5 13.0523 12.5 12.5V7.5H11.5Z\"\r\n        fill=\"currentColor\"\r\n      ></path>\r\n    </svg>\r\n  );\r\n};\r\n\r\nexport const EyeIcon = ({ size = 16 }: { size?: number }) => {\r\n  return (\r\n    <svg\r\n      height={size}\r\n      strokeLinejoin=\"round\"\r\n      viewBox=\"0 0 16 16\"\r\n      width={size}\r\n      style={{ color: 'currentcolor' }}\r\n    >\r\n      <path\r\n        fillRule=\"evenodd\"\r\n        clipRule=\"evenodd\"\r\n        d=\"M4.02168 4.76932C6.11619 2.33698 9.88374 2.33698 11.9783 4.76932L14.7602 7.99999L11.9783 11.2307C9.88374 13.663 6.1162 13.663 4.02168 11.2307L1.23971 7.99999L4.02168 4.76932ZM13.1149 3.79054C10.422 0.663244 5.57797 0.663247 2.88503 3.79054L-0.318359 7.5106V8.48938L2.88503 12.2094C5.57797 15.3367 10.422 15.3367 13.1149 12.2094L16.3183 8.48938V7.5106L13.1149 3.79054ZM6.49997 7.99999C6.49997 7.17157 7.17154 6.49999 7.99997 6.49999C8.82839 6.49999 9.49997 7.17157 9.49997 7.99999C9.49997 8.82842 8.82839 9.49999 7.99997 9.49999C7.17154 9.49999 6.49997 8.82842 6.49997 7.99999ZM7.99997 4.99999C6.34311 4.99999 4.99997 6.34314 4.99997 7.99999C4.99997 9.65685 6.34311 11 7.99997 11C9.65682 11 11 9.65685 11 7.99999C11 6.34314 9.65682 4.99999 7.99997 4.99999Z\"\r\n        fill=\"currentColor\"\r\n      ></path>\r\n    </svg>\r\n  );\r\n};\r\n\r\nexport const ShareIcon = ({ size = 16 }: { size?: number }) => {\r\n  return (\r\n    <svg\r\n      height={size}\r\n      strokeLinejoin=\"round\"\r\n      viewBox=\"0 0 16 16\"\r\n      width={size}\r\n      style={{ color: 'currentcolor' }}\r\n    >\r\n      <path\r\n        fillRule=\"evenodd\"\r\n        clipRule=\"evenodd\"\r\n        d=\"M15 11.25V10.5H13.5V11.25V12.75C13.5 13.1642 13.1642 13.5 12.75 13.5H3.25C2.83579 13.5 2.5 13.1642 2.5 12.75L2.5 3.25C2.5 2.83579 2.83579 2.5 3.25 2.5H5.75H6.5V1H5.75H3.25C2.00736 1 1 2.00736 1 3.25V12.75C1 13.9926 2.00736 15 3.25 15H12.75C13.9926 15 15 13.9926 15 12.75V11.25ZM15 5.5L10.5 1V4C7.46243 4 5 6.46243 5 9.5V10L5.05855 9.91218C6.27146 8.09281 8.31339 7 10.5 7V10L15 5.5Z\"\r\n        fill=\"currentColor\"\r\n      ></path>\r\n    </svg>\r\n  );\r\n};\r\n\r\nexport const CodeIcon = ({ size = 16 }: { size?: number }) => {\r\n  return (\r\n    <svg\r\n      height={size}\r\n      strokeLinejoin=\"round\"\r\n      viewBox=\"0 0 16 16\"\r\n      width={size}\r\n      style={{ color: 'currentcolor' }}\r\n    >\r\n      <path\r\n        fillRule=\"evenodd\"\r\n        clipRule=\"evenodd\"\r\n        d=\"M4.21969 12.5303L4.75002 13.0607L5.81068 12L5.28035 11.4697L1.81068 7.99999L5.28035 4.53032L5.81068 3.99999L4.75002 2.93933L4.21969 3.46966L0.39647 7.29289C0.00594562 7.68341 0.00594562 8.31658 0.39647 8.7071L4.21969 12.5303ZM11.7804 12.5303L11.25 13.0607L10.1894 12L10.7197 11.4697L14.1894 7.99999L10.7197 4.53032L10.1894 3.99999L11.25 2.93933L11.7804 3.46966L15.6036 7.29289C15.9941 7.68341 15.9941 8.31658 15.6036 8.7071L11.7804 12.5303Z\"\r\n        fill=\"currentColor\"\r\n      ></path>\r\n    </svg>\r\n  );\r\n};\r\n\r\nexport const PlayIcon = ({ size = 16 }: { size?: number }) => {\r\n  return (\r\n    <svg\r\n      height={size}\r\n      strokeLinejoin=\"round\"\r\n      viewBox=\"0 0 16 16\"\r\n      width={size}\r\n      style={{ color: 'currentcolor' }}\r\n    >\r\n      <path\r\n        fillRule=\"evenodd\"\r\n        clipRule=\"evenodd\"\r\n        d=\"M13.4549 7.22745L13.3229 7.16146L2.5 1.74999L2.4583 1.72914L1.80902 1.4045L1.3618 1.18089C1.19558 1.09778 1 1.21865 1 1.4045L1 1.9045L1 2.63041L1 2.67704L1 13.3229L1 13.3696L1 14.0955L1 14.5955C1 14.7813 1.19558 14.9022 1.3618 14.8191L1.80902 14.5955L2.4583 14.2708L2.5 14.25L13.3229 8.83852L13.4549 8.77253L14.2546 8.37267L14.5528 8.2236C14.737 8.13147 14.737 7.86851 14.5528 7.77638L14.2546 7.62731L13.4549 7.22745ZM11.6459 7.99999L2.5 3.42704L2.5 12.5729L11.6459 7.99999Z\"\r\n        fill=\"currentColor\"\r\n      ></path>\r\n    </svg>\r\n  );\r\n};\r\n\r\nexport const PythonIcon = ({ size = 16 }: { size?: number }) => {\r\n  return (\r\n    <svg\r\n      height={size}\r\n      strokeLinejoin=\"round\"\r\n      viewBox=\"0 0 16 16\"\r\n      width={size}\r\n      style={{ color: 'currentcolor' }}\r\n    >\r\n      <path\r\n        d=\"M7.90474 0.00013087C7.24499 0.00316291 6.61494 0.0588153 6.06057 0.15584C4.42745 0.441207 4.13094 1.0385 4.13094 2.14002V3.59479H7.9902V4.07971H4.13094H2.68259C1.56099 4.07971 0.578874 4.7465 0.271682 6.01496C-0.0826597 7.4689 -0.0983767 8.37619 0.271682 9.89434C0.546012 11.0244 1.20115 11.8296 2.32276 11.8296H3.64966V10.0856C3.64966 8.82574 4.75179 7.71441 6.06057 7.71441H9.91533C10.9884 7.71441 11.845 6.84056 11.845 5.77472V2.14002C11.845 1.10556 10.9626 0.328487 9.91533 0.15584C9.25237 0.046687 8.56448 -0.00290121 7.90474 0.00013087ZM5.81768 1.17017C6.21631 1.17017 6.54185 1.49742 6.54185 1.89978C6.54185 2.30072 6.21631 2.62494 5.81768 2.62494C5.41761 2.62494 5.09351 2.30072 5.09351 1.89978C5.09351 1.49742 5.41761 1.17017 5.81768 1.17017Z\"\r\n        fill=\"currentColor\"\r\n      ></path>\r\n      <path\r\n        d=\"M12.3262 4.07971V5.77472C12.3262 7.08883 11.1997 8.19488 9.91525 8.19488H6.06049C5.0046 8.19488 4.13086 9.0887 4.13086 10.1346V13.7693C4.13086 14.8037 5.04033 15.4122 6.06049 15.709C7.28211 16.0642 8.45359 16.1285 9.91525 15.709C10.8868 15.4307 11.8449 14.8708 11.8449 13.7693V12.3145H7.99012V11.8296H11.8449H13.7745C14.8961 11.8296 15.3141 11.0558 15.7041 9.89434C16.1071 8.69865 16.0899 7.5488 15.7041 6.01495C15.4269 4.91058 14.8975 4.07971 13.7745 4.07971H12.3262ZM10.1581 13.2843C10.5582 13.2843 10.8823 13.6086 10.8823 14.0095C10.8823 14.4119 10.5582 14.7391 10.1581 14.7391C9.7595 14.7391 9.43397 14.4119 9.43397 14.0095C9.43397 13.6086 9.7595 13.2843 10.1581 13.2843Z\"\r\n        fill=\"currentColor\"\r\n      ></path>\r\n    </svg>\r\n  );\r\n};\r\n\r\nexport const TerminalWindowIcon = ({ size = 16 }: { size?: number }) => {\r\n  return (\r\n    <svg\r\n      height={size}\r\n      strokeLinejoin=\"round\"\r\n      viewBox=\"0 0 16 16\"\r\n      width={size}\r\n      style={{ color: 'currentcolor' }}\r\n    >\r\n      <path\r\n        fillRule=\"evenodd\"\r\n        clipRule=\"evenodd\"\r\n        d=\"M1.5 2.5H14.5V12.5C14.5 13.0523 14.0523 13.5 13.5 13.5H2.5C1.94772 13.5 1.5 13.0523 1.5 12.5V2.5ZM0 1H1.5H14.5H16V2.5V12.5C16 13.8807 14.8807 15 13.5 15H2.5C1.11929 15 0 13.8807 0 12.5V2.5V1ZM4 11.1339L4.44194 10.6919L6.51516 8.61872C6.85687 8.27701 6.85687 7.72299 6.51517 7.38128L4.44194 5.30806L4 4.86612L3.11612 5.75L3.55806 6.19194L5.36612 8L3.55806 9.80806L3.11612 10.25L4 11.1339ZM8 9.75494H8.6225H11.75H12.3725V10.9999H11.75H8.6225H8V9.75494Z\"\r\n        fill=\"currentColor\"\r\n      ></path>\r\n    </svg>\r\n  );\r\n};\r\n\r\nexport const TerminalIcon = ({ size = 16 }: { size?: number }) => {\r\n  return (\r\n    <svg\r\n      height={size}\r\n      strokeLinejoin=\"round\"\r\n      viewBox=\"0 0 16 16\"\r\n      width={size}\r\n      style={{ color: 'currentcolor' }}\r\n    >\r\n      <path\r\n        fillRule=\"evenodd\"\r\n        clipRule=\"evenodd\"\r\n        d=\"M1.53035 12.7804L1.00002 13.3108L-0.0606384 12.2501L0.469692 11.7198L4.18936 8.00011L0.469692 4.28044L-0.0606384 3.75011L1.00002 2.68945L1.53035 3.21978L5.60358 7.29301C5.9941 7.68353 5.9941 8.3167 5.60357 8.70722L1.53035 12.7804ZM8.75002 12.5001H8.00002V14.0001H8.75002H15.25H16V12.5001H15.25H8.75002Z\"\r\n        fill=\"currentColor\"\r\n      ></path>\r\n    </svg>\r\n  );\r\n};\r\n\r\nexport const ClockRewind = ({ size = 16 }: { size?: number }) => {\r\n  return (\r\n    <svg\r\n      height={size}\r\n      strokeLinejoin=\"round\"\r\n      viewBox=\"0 0 16 16\"\r\n      width={size}\r\n      style={{ color: 'currentcolor' }}\r\n    >\r\n      <path\r\n        fillRule=\"evenodd\"\r\n        clipRule=\"evenodd\"\r\n        d=\"M7.96452 2.5C11.0257 2.5 13.5 4.96643 13.5 8C13.5 11.0336 11.0257 13.5 7.96452 13.5C6.12055 13.5 4.48831 12.6051 3.48161 11.2273L3.03915 10.6217L1.828 11.5066L2.27046 12.1122C3.54872 13.8617 5.62368 15 7.96452 15C11.8461 15 15 11.87 15 8C15 4.13001 11.8461 1 7.96452 1C5.06835 1 2.57851 2.74164 1.5 5.23347V3.75V3H0V3.75V7.25C0 7.66421 0.335786 8 0.75 8H3.75H4.5V6.5H3.75H2.63724C3.29365 4.19393 5.42843 2.5 7.96452 2.5ZM8.75 5.25V4.5H7.25V5.25V7.8662C7.25 8.20056 7.4171 8.51279 7.6953 8.69825L9.08397 9.62404L9.70801 10.0401L10.5401 8.79199L9.91603 8.37596L8.75 7.59861V5.25Z\"\r\n        fill=\"currentColor\"\r\n      ></path>\r\n    </svg>\r\n  );\r\n};\r\n\r\nexport const LogsIcon = ({ size = 16 }: { size?: number }) => {\r\n  return (\r\n    <svg\r\n      height={size}\r\n      strokeLinejoin=\"round\"\r\n      viewBox=\"0 0 16 16\"\r\n      width={size}\r\n      style={{ color: 'currentcolor' }}\r\n    >\r\n      <path\r\n        fillRule=\"evenodd\"\r\n        clipRule=\"evenodd\"\r\n        d=\"M9 2H9.75H14.25H15V3.5H14.25H9.75H9V2ZM9 12.5H9.75H14.25H15V14H14.25H9.75H9V12.5ZM9.75 7.25H9V8.75H9.75H14.25H15V7.25H14.25H9.75ZM1 12.5H1.75H2.25H3V14H2.25H1.75H1V12.5ZM1.75 2H1V3.5H1.75H2.25H3V2H2.25H1.75ZM1 7.25H1.75H2.25H3V8.75H2.25H1.75H1V7.25ZM5.75 12.5H5V14H5.75H6.25H7V12.5H6.25H5.75ZM5 2H5.75H6.25H7V3.5H6.25H5.75H5V2ZM5.75 7.25H5V8.75H5.75H6.25H7V7.25H6.25H5.75Z\"\r\n        fill=\"currentColor\"\r\n      ></path>\r\n    </svg>\r\n  );\r\n};\r\n\r\nexport const ImageIcon = ({ size = 16 }: { size?: number }) => {\r\n  return (\r\n    <svg\r\n      height={size}\r\n      strokeLinejoin=\"round\"\r\n      viewBox=\"0 0 16 16\"\r\n      width={size}\r\n      style={{ color: 'currentcolor' }}\r\n    >\r\n      <path\r\n        fillRule=\"evenodd\"\r\n        clipRule=\"evenodd\"\r\n        d=\"M14.5 2.5H1.5V9.18933L2.96966 7.71967L3.18933 7.5H3.49999H6.63001H6.93933L6.96966 7.46967L10.4697 3.96967L11.5303 3.96967L14.5 6.93934V2.5ZM8.00066 8.55999L9.53034 10.0897L10.0607 10.62L9.00001 11.6807L8.46968 11.1503L6.31935 9H3.81065L1.53032 11.2803L1.5 11.3106V12.5C1.5 13.0523 1.94772 13.5 2.5 13.5H13.5C14.0523 13.5 14.5 13.0523 14.5 12.5V9.06066L11 5.56066L8.03032 8.53033L8.00066 8.55999ZM4.05312e-06 10.8107V12.5C4.05312e-06 13.8807 1.11929 15 2.5 15H13.5C14.8807 15 16 13.8807 16 12.5V9.56066L16.5607 9L16.0303 8.46967L16 8.43934V2.5V1H14.5H1.5H4.05312e-06V2.5V10.6893L-0.0606689 10.75L4.05312e-06 10.8107Z\"\r\n        fill=\"currentColor\"\r\n      ></path>\r\n    </svg>\r\n  );\r\n};\r\n\r\nexport const FullscreenIcon = ({ size = 16 }: { size?: number }) => (\r\n  <svg\r\n    height={size}\r\n    strokeLinejoin=\"round\"\r\n    viewBox=\"0 0 16 16\"\r\n    width={size}\r\n    style={{ color: 'currentcolor' }}\r\n  >\r\n    <path\r\n      fillRule=\"evenodd\"\r\n      clipRule=\"evenodd\"\r\n      d=\"M1 5.25V6H2.5V5.25V2.5H5.25H6V1H5.25H2C1.44772 1 1 1.44772 1 2V5.25ZM5.25 14.9994H6V13.4994H5.25H2.5V10.7494V9.99939H1V10.7494V13.9994C1 14.5517 1.44772 14.9994 2 14.9994H5.25ZM15 10V10.75V14C15 14.5523 14.5523 15 14 15H10.75H10V13.5H10.75H13.5V10.75V10H15ZM10.75 1H10V2.5H10.75H13.5V5.25V6H15V5.25V2C15 1.44772 14.5523 1 14 1H10.75Z\"\r\n      fill=\"currentColor\"\r\n    ></path>\r\n  </svg>\r\n);\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAO,MAAM,UAAU;IACrB,qBACE,sSAAC;QACC,QAAO;QACP,gBAAe;QACf,SAAQ;QACR,OAAM;QACN,OAAO;YAAE,OAAO;QAAe;kBAE/B,cAAA,sSAAC;YACC,UAAS;YACT,UAAS;YACT,GAAE;YACF,MAAK;;;;;;;;;;;AAIb;KAjBa;AAmBN,MAAM,WAAW;IACtB,qBACE,sSAAC;QACC,eAAY;QACZ,QAAO;QACP,gBAAe;QACf,SAAQ;QACR,OAAM;QACN,OAAO;YAAE,OAAO;QAAe;kBAE/B,cAAA,sSAAC;YACC,UAAS;YACT,UAAS;YACT,GAAE;YACF,MAAK;;;;;;;;;;;AAIb;MAlBa;AAoBN,MAAM,iBAAiB;IAC5B,qBACE,sSAAC;QACC,QAAO;QACP,gBAAe;QACf,SAAQ;QACR,OAAM;QACN,OAAO;YAAE,OAAO;QAAe;kBAE/B,cAAA,sSAAC;YACC,UAAS;YACT,UAAS;YACT,GAAE;YACF,MAAK;;;;;;;;;;;AAIb;MAjBa;AAmBN,MAAM,aAAa,CAAC,EAAE,OAAO,EAAE,EAAE;IACtC,qBACE,sSAAC;QACC,QAAQ;QACR,gBAAe;QACf,SAAQ;QACR,OAAO;QACP,OAAO;YAAE,OAAO;QAAe;kBAE/B,cAAA,sSAAC;YACC,UAAS;YACT,UAAS;YACT,GAAE;YACF,MAAK;;;;;;;;;;;AAIb;MAjBa;AAmBN,MAAM,UAAU;IACrB,qBACE,sSAAC;QACC,QAAO;QACP,gBAAe;QACf,SAAQ;QACR,OAAM;QACN,OAAO;YAAE,OAAO;QAAe;;0BAE/B,sSAAC;gBAAE,UAAS;0BACV,cAAA,sSAAC;oBACC,UAAS;oBACT,UAAS;oBACT,GAAE;oBACF,MAAK;;;;;;;;;;;0BAGT,sSAAC;0BACC,cAAA,sSAAC;oBAAS,IAAG;8BACX,cAAA,sSAAC;wBAAK,OAAM;wBAAK,QAAO;wBAAK,MAAK;;;;;;;;;;;;;;;;;;;;;;AAK5C;MAxBa;AA0BN,MAAM,UAAU,CAAC,EAAE,OAAO,EAAE,EAAoB;IACrD,qBACE,sSAAC;QACC,QAAQ;QACR,gBAAe;QACf,SAAQ;QACR,OAAO;QACP,OAAO;YAAE,OAAO;QAAe;kBAE/B,cAAA,sSAAC;YACC,UAAS;YACT,UAAS;YACT,GAAE;YACF,MAAK;;;;;;;;;;;AAIb;MAjBa;AAmBN,MAAM,WAAW,CAAC,EAAE,OAAO,EAAE,EAAoB;IACtD,qBACE,sSAAC;QACC,QAAQ;QACR,gBAAe;QACf,SAAQ;QACR,OAAO;QACP,OAAO;YAAE,OAAO;QAAe;kBAE/B,cAAA,sSAAC;YACC,UAAS;YACT,UAAS;YACT,GAAE;YACF,MAAK;;;;;;;;;;;AAIb;MAjBa;AAmBN,MAAM,UAAU,CAAC,EAAE,OAAO,EAAE,EAAoB;IACrD,qBACE,sSAAC;QACC,QAAQ;QACR,gBAAe;QACf,SAAQ;QACR,OAAO;QACP,OAAO;YAAE,OAAO;QAAe;kBAE/B,cAAA,sSAAC;YACC,GAAE;YACF,QAAO;YACP,aAAY;YACZ,eAAc;YACd,gBAAe;YACf,MAAK;;;;;;;;;;;AAIb;MAnBa;AAqBN,MAAM,cAAc,CAAC,EAAE,OAAO,EAAE,EAAoB;IACzD,qBACE,sSAAC;QACC,QAAQ;QACR,gBAAe;QACf,SAAQ;QACR,OAAO;QACP,OAAO;YAAE,OAAO;QAAe;kBAE/B,cAAA,sSAAC;YACC,UAAS;YACT,UAAS;YACT,GAAE;YACF,MAAK;;;;;;;;;;;AAIb;MAjBa;AAmBN,MAAM,aAAa,CAAC,EAAE,OAAO,EAAE,EAAqB;IACzD,qBACE,sSAAC;QACC,QAAQ;QACR,gBAAe;QACf,SAAQ;QACR,OAAO;QACP,OAAO;YAAE,OAAO;QAAe;kBAE/B,cAAA,sSAAC;YACC,GAAE;YACF,MAAK;;;;;;;;;;;AAIb;MAfa;AAiBN,MAAM,aAAa,CAAC,EAAE,OAAO,EAAE,EAAqB;IACzD,qBACE,sSAAC;QACC,eAAY;QACZ,QAAQ;QACR,gBAAe;QACf,SAAQ;QACR,OAAO;QACP,OAAO;YAAE,OAAO;QAAe;;0BAE/B,sSAAC;gBACC,GAAE;gBACF,MAAK;;;;;;0BAEP,sSAAC;gBACC,GAAE;gBACF,MAAK;;;;;;0BAEP,sSAAC;gBACC,GAAE;gBACF,MAAK;;;;;;0BAEP,sSAAC;gBACC,GAAE;gBACF,MAAK;;;;;;;;;;;;AAIb;OA5Ba;AA8BN,MAAM,gBAAgB;IAC3B,qBACE,sSAAC;QACC,OAAM;QACN,YAAW;QACX,GAAE;QACF,GAAE;QACF,SAAQ;QACR,OAAO;YAAE,OAAO;YAAgB,MAAM;QAAe;QACrD,OAAM;QACN,QAAO;kBAEP,cAAA,sSAAC;YACC,GAAE;;;;;;;;;;;AAKV;OAlBa;AAoBN,MAAM,YAAY,CAAC,EAAE,OAAO,EAAE,EAAqB;IACxD,qBACE,sSAAC;QACC,QAAQ;QACR,gBAAe;QACf,SAAQ;QACR,OAAO;QACP,OAAO;YAAE,OAAO;QAAe;kBAE/B,cAAA,sSAAC;YACC,UAAS;YACT,UAAS;YACT,GAAE;YACF,MAAK;;;;;;;;;;;AAIb;OAjBa;AAmBN,MAAM,WAAW,CAAC,EAAE,OAAO,EAAE,EAAqB;IACvD,qBACE,sSAAC;QACC,QAAQ;QACR,gBAAe;QACf,SAAQ;QACR,OAAO;QACP,OAAO;YAAE,OAAO;QAAe;kBAE/B,cAAA,sSAAC;YACC,UAAS;YACT,UAAS;YACT,GAAE;YACF,MAAK;;;;;;;;;;;AAIb;OAjBa;AAmBN,MAAM,aAAa,CAAC,EAAE,OAAO,EAAE,EAAqB;IACzD,qBACE,sSAAC;QACC,QAAQ;QACR,gBAAe;QACf,SAAQ;QACR,OAAO;QACP,OAAO;YAAE,OAAO;QAAe;;0BAE/B,sSAAC;gBAAE,UAAS;;kCACV,sSAAC;wBAAK,GAAE;wBAAS,QAAO;wBAAe,aAAY;;;;;;kCACnD,sSAAC;wBACC,SAAQ;wBACR,GAAE;wBACF,QAAO;wBACP,aAAY;;;;;;kCAEd,sSAAC;wBACC,SAAQ;wBACR,GAAE;wBACF,QAAO;wBACP,aAAY;;;;;;kCAEd,sSAAC;wBACC,SAAQ;wBACR,GAAE;wBACF,QAAO;wBACP,aAAY;;;;;;kCAEd,sSAAC;wBACC,SAAQ;wBACR,GAAE;wBACF,QAAO;wBACP,aAAY;;;;;;kCAEd,sSAAC;wBACC,SAAQ;wBACR,GAAE;wBACF,QAAO;wBACP,aAAY;;;;;;kCAEd,sSAAC;wBACC,SAAQ;wBACR,GAAE;wBACF,QAAO;wBACP,aAAY;;;;;;kCAEd,sSAAC;wBACC,SAAQ;wBACR,GAAE;wBACF,QAAO;wBACP,aAAY;;;;;;kCAEd,sSAAC;wBACC,SAAQ;wBACR,GAAE;wBACF,QAAO;wBACP,aAAY;;;;;;kCAEd,sSAAC;wBACC,SAAQ;wBACR,GAAE;wBACF,QAAO;wBACP,aAAY;;;;;;;;;;;;0BAGhB,sSAAC;0BACC,cAAA,sSAAC;oBAAS,IAAG;8BACX,cAAA,sSAAC;wBAAK,OAAM;wBAAK,QAAO;wBAAK,MAAK;;;;;;;;;;;;;;;;;;;;;;AAK5C;OAzEa;AA2EN,MAAM,aAAa,CAAC,EAAE,OAAO,EAAE,EAAqB;IACzD,qBACE,sSAAC;QACC,eAAY;QACZ,QAAQ;QACR,gBAAe;QACf,SAAQ;QACR,OAAO;QACP,OAAO;YAAE,OAAO;QAAe;kBAE/B,cAAA,sSAAC;YACC,UAAS;YACT,UAAS;YACT,GAAE;YACF,MAAK;;;;;;;;;;;AAIb;OAlBa;AAoBN,MAAM,WAAW,CAAC,EAAE,OAAO,EAAE,EAAqB;IACvD,qBACE,sSAAC;QACC,QAAQ;QACR,gBAAe;QACf,SAAQ;QACR,OAAO;QACP,OAAO;YAAE,OAAO;QAAe;kBAE/B,cAAA,sSAAC;YACC,UAAS;YACT,UAAS;YACT,GAAE;YACF,MAAK;;;;;;;;;;;AAIb;OAjBa;AAmBN,MAAM,iBAAiB,CAAC,EAAE,OAAO,EAAE,EAAqB;IAC7D,qBACE,sSAAC;QACC,QAAQ;QACR,gBAAe;QACf,SAAQ;QACR,OAAO;QACP,OAAO;YAAE,OAAO;QAAe;kBAE/B,cAAA,sSAAC;YACC,UAAS;YACT,UAAS;YACT,GAAE;YACF,MAAK;;;;;;;;;;;AAIb;OAjBa;AAmBN,MAAM,gBAAgB,CAAC,EAAE,OAAO,EAAE,EAAqB;IAC5D,qBACE,sSAAC;QACC,QAAQ;QACR,gBAAe;QACf,SAAQ;QACR,OAAO;QACP,OAAO;YAAE,OAAO;QAAe;kBAE/B,cAAA,sSAAC;YACC,UAAS;YACT,UAAS;YACT,GAAE;YACF,MAAK;;;;;;;;;;;AAIb;OAjBa;AAmBN,MAAM,kBAAkB,CAAC,EAAE,OAAO,EAAE,EAAqB;IAC9D,qBACE,sSAAC;QACC,QAAQ;QACR,gBAAe;QACf,SAAQ;QACR,OAAO;QACP,OAAO;YAAE,OAAO;QAAe;kBAE/B,cAAA,sSAAC;YACC,GAAE;YACF,GAAE;YACF,OAAM;YACN,QAAO;YACP,QAAO;YACP,aAAY;YACZ,MAAK;;;;;;;;;;;AAIb;OApBa;AAsBN,MAAM,WAAW,CAAC,EAAE,OAAO,EAAE,EAAqB;IACvD,qBACE,sSAAC;QACC,QAAQ;QACR,gBAAe;QACf,SAAQ;QACR,OAAO;QACP,OAAO;YAAE,OAAO;QAAe;kBAE/B,cAAA,sSAAC;YACC,UAAS;YACT,UAAS;YACT,GAAE;YACF,MAAK;;;;;;;;;;;AAIb;OAjBa;AAmBN,MAAM,YAAY,CAAC,EAAE,OAAO,EAAE,EAAqB;IACxD,qBACE,sSAAC;QACC,QAAQ;QACR,gBAAe;QACf,SAAQ;QACR,OAAO;QACP,OAAO;YAAE,OAAO;QAAe;kBAE/B,cAAA,sSAAC;YACC,UAAS;YACT,UAAS;YACT,GAAE;YACF,MAAK;;;;;;;;;;;AAIb;OAjBa;AAmBN,MAAM,WAAW,CAAC,EAAE,OAAO,EAAE,EAAqB;IACvD,qBACE,sSAAC;QACC,QAAQ;QACR,gBAAe;QACf,SAAQ;QACR,OAAO;QACP,OAAO;YAAE,OAAO;QAAe;kBAE/B,cAAA,sSAAC;YACC,UAAS;YACT,UAAS;YACT,GAAE;YACF,MAAK;;;;;;;;;;;AAIb;OAjBa;AAmBN,MAAM,cAAc,CAAC,EAAE,OAAO,EAAE,EAAqB;IAC1D,qBACE,sSAAC;QACC,QAAQ;QACR,gBAAe;QACf,SAAQ;QACR,OAAO;QACP,OAAO;YAAE,OAAO;QAAe;kBAE/B,cAAA,sSAAC;YACC,UAAS;YACT,UAAS;YACT,GAAE;YACF,MAAK;;;;;;;;;;;AAIb;OAjBa;AAmBN,MAAM,WAAW,CAAC,EAAE,OAAO,EAAE,EAAqB;IACvD,qBACE,sSAAC;QACC,QAAQ;QACR,SAAQ;QACR,OAAO;QACP,OAAO;YAAE,OAAO;QAAe;kBAE/B,cAAA,sSAAC;YACC,UAAS;YACT,UAAS;YACT,GAAE;YACF,MAAK;;;;;;;;;;;AAIb;OAhBa;AAkBN,MAAM,gBAAgB,CAAC,EAAE,OAAO,EAAE,EAAqB;IAC5D,qBACE,sSAAC;QACC,QAAQ;QACR,gBAAe;QACf,SAAQ;QACR,OAAO;QACP,OAAO;YAAE,OAAO;QAAe;QAC/B,WAAU;kBAEV,cAAA,sSAAC;YACC,UAAS;YACT,UAAS;YACT,GAAE;YACF,MAAK;;;;;;;;;;;AAIb;OAlBa;AAoBN,MAAM,qBAAqB,CAAC,EAAE,OAAO,EAAE,EAAqB;IACjE,qBACE,sSAAC;QACC,QAAQ;QACR,gBAAe;QACf,SAAQ;QACR,OAAO;QACP,OAAO;YAAE,OAAO;QAAe;kBAE/B,cAAA,sSAAC;YACC,UAAS;YACT,UAAS;YACT,GAAE;YACF,MAAK;;;;;;;;;;;AAIb;OAjBa;AAmBN,MAAM,cAAc,CAAC,EAAE,OAAO,EAAE,EAAqB;IAC1D,qBACE,sSAAC;QACC,QAAQ;QACR,gBAAe;QACf,SAAQ;QACR,OAAO;QACP,OAAO;YAAE,OAAO;QAAe;kBAE/B,cAAA,sSAAC;YACC,UAAS;YACT,UAAS;YACT,GAAE;YACF,MAAK;;;;;;;;;;;AAIb;OAjBa;AAmBN,MAAM,YAAY,CAAC,EAAE,OAAO,EAAE,EAAqB,iBACxD,sSAAC;QACC,QAAQ;QACR,gBAAe;QACf,SAAQ;QACR,OAAO;QACP,OAAO;YAAE,OAAO;QAAe;kBAE/B,cAAA,sSAAC;YACC,UAAS;YACT,UAAS;YACT,GAAE;YACF,MAAK;;;;;;;;;;;OAZE;AAiBN,MAAM,iBAAiB,CAAC,EAAE,OAAO,EAAE,EAAqB,iBAC7D,sSAAC;QACC,QAAQ;QACR,gBAAe;QACf,SAAQ;QACR,OAAO;QACP,OAAO;YAAE,OAAO;QAAe;kBAE/B,cAAA,sSAAC;YACC,UAAS;YACT,UAAS;YACT,GAAE;YACF,MAAK;;;;;;;;;;;OAZE;AAiBN,MAAM,WAAW,CAAC,EAAE,OAAO,EAAE,EAAqB,iBACvD,sSAAC;QACC,QAAQ;QACR,gBAAe;QACf,SAAQ;QACR,OAAO;QACP,OAAO;YAAE,OAAO;QAAe;kBAE/B,cAAA,sSAAC;YACC,UAAS;YACT,UAAS;YACT,GAAE;YACF,MAAK;;;;;;;;;;;OAZE;AAiBN,MAAM,WAAW,CAAC,EAAE,OAAO,EAAE,EAAqB,iBACvD,sSAAC;QACC,QAAQ;QACR,gBAAe;QACf,SAAQ;QACR,OAAO;QACP,OAAO;YAAE,OAAO;QAAe;kBAE/B,cAAA,sSAAC;YACC,UAAS;YACT,UAAS;YACT,GAAE;YACF,MAAK;;;;;;;;;;;OAZE;AAiBN,MAAM,YAAY,CAAC,EAAE,OAAO,EAAE,EAAqB,iBACxD,sSAAC;QACC,QAAQ;QACR,gBAAe;QACf,SAAQ;QACR,OAAO;QACP,OAAO;YAAE,OAAO;QAAe;kBAE/B,cAAA,sSAAC;YACC,UAAS;YACT,UAAS;YACT,GAAE;YACF,MAAK;;;;;;;;;;;OAZE;AAiBN,MAAM,UAAU,CAAC,EAAE,OAAO,EAAE,EAAqB,iBACtD,sSAAC;QACC,QAAQ;QACR,gBAAe;QACf,SAAQ;QACR,OAAO;QACP,OAAO;YAAE,OAAO;QAAe;kBAE/B,cAAA,sSAAC;YACC,UAAS;YACT,UAAS;YACT,GAAE;YACF,MAAK;;;;;;;;;;;OAZE;AAiBN,MAAM,gBAAgB,CAAC,EAAE,OAAO,EAAE,EAAqB,iBAC5D,sSAAC;QACC,QAAQ;QACR,gBAAe;QACf,SAAQ;QACR,OAAO;QACP,OAAO;YAAE,OAAO;QAAe;kBAE/B,cAAA,sSAAC;YACC,UAAS;YACT,UAAS;YACT,GAAE;YACF,MAAK;;;;;;;;;;;OAZE;AAiBN,MAAM,kBAAkB,CAAC,EAAE,OAAO,EAAE,EAAqB,iBAC9D,sSAAC;QACC,QAAQ;QACR,gBAAe;QACf,SAAQ;QACR,OAAO;QACP,OAAO;YAAE,OAAO;QAAe;kBAE/B,cAAA,sSAAC;YACC,UAAS;YACT,UAAS;YACT,GAAE;YACF,MAAK;;;;;;;;;;;OAZE;AAiBN,MAAM,WAAW,CAAC,EAAE,OAAO,EAAE,EAAqB,iBACvD,sSAAC;QACC,QAAQ;QACR,gBAAe;QACf,SAAQ;QACR,OAAO;QACP,OAAO;YAAE,OAAO;QAAe;kBAE/B,cAAA,sSAAC;YACC,UAAS;YACT,UAAS;YACT,GAAE;YACF,MAAK;;;;;;;;;;;OAZE;AAiBN,MAAM,WAAW,CAAC,EAAE,OAAO,EAAE,EAAqB,iBACvD,sSAAC;QACC,QAAQ;QACR,gBAAe;QACf,SAAQ;QACR,OAAO;QACP,OAAO;YAAE,OAAO;QAAe;kBAE/B,cAAA,sSAAC;YACC,UAAS;YACT,UAAS;YACT,GAAE;YACF,MAAK;;;;;;;;;;;OAZE;AAiBN,MAAM,cAAc,CAAC,EAAE,OAAO,EAAE,EAAqB,iBAC1D,sSAAC;QACC,QAAQ;QACR,gBAAe;QACf,SAAQ;QACR,OAAO;QACP,OAAO;YAAE,OAAO;QAAe;kBAE/B,cAAA,sSAAC;YACC,UAAS;YACT,UAAS;YACT,GAAE;YACF,MAAK;;;;;;;;;;;OAZE;AAiBN,MAAM,gBAAgB,CAAC,EAAE,OAAO,EAAE,EAAqB,iBAC5D,sSAAC;QACC,QAAQ;QACR,gBAAe;QACf,SAAQ;QACR,OAAO;QACP,OAAO;YAAE,OAAO;QAAe;kBAE/B,cAAA,sSAAC;YACC,UAAS;YACT,UAAS;YACT,GAAE;YACF,MAAK;;;;;;;;;;;OAZE;AAiBN,MAAM,kBAAkB,CAAC,EAAE,OAAO,EAAE,EAAqB,iBAC9D,sSAAC;QACC,QAAQ;QACR,gBAAe;QACf,SAAQ;QACR,OAAO;QACP,OAAO;YAAE,OAAO;QAAe;kBAE/B,cAAA,sSAAC;YACC,UAAS;YACT,UAAS;YACT,GAAE;YACF,MAAK;;;;;;;;;;;OAZE;AAiBN,MAAM,eAAe,CAAC,EAAE,OAAO,EAAE,EAAqB,iBAC3D,sSAAC;QACC,QAAQ;QACR,gBAAe;QACf,SAAQ;QACR,OAAO;QACP,OAAO;YAAE,OAAO;QAAe;;0BAE/B,sSAAC;gBACC,GAAE;gBACF,MAAK;;;;;;0BAEP,sSAAC;gBACC,GAAE;gBACF,MAAK;;;;;;0BAEP,sSAAC;gBACC,GAAE;gBACF,MAAK;;;;;;;;;;;;OAlBE;AAuBN,MAAM,sBAAsB,CAAC,EAAE,OAAO,EAAE,EAAqB;IAClE,qBACE,sSAAC;QACC,QAAQ;QACR,gBAAe;QACf,SAAQ;QACR,OAAO;QACP,OAAO;YAAE,OAAO;QAAe;kBAE/B,cAAA,sSAAC;YACC,UAAS;YACT,UAAS;YACT,GAAE;YACF,MAAK;;;;;;;;;;;AAIb;OAjBa;AAmBN,MAAM,YAAY,CAAC,EAAE,OAAO,EAAE,EAAqB;IACxD,qBACE,sSAAC;QACC,QAAQ;QACR,gBAAe;QACf,SAAQ;QACR,OAAO;QACP,OAAO;YAAE,OAAO;QAAe;kBAE/B,cAAA,sSAAC;YACC,UAAS;YACT,UAAS;YACT,GAAE;YACF,MAAK;;;;;;;;;;;AAIb;OAjBa;AAmBN,MAAM,WAAW,CAAC,EAAE,OAAO,EAAE,EAAqB;IACvD,qBACE,sSAAC;QACC,QAAQ;QACR,gBAAe;QACf,SAAQ;QACR,OAAO;QACP,OAAO;YAAE,OAAO;QAAe;kBAE/B,cAAA,sSAAC;YACC,UAAS;YACT,UAAS;YACT,GAAE;YACF,MAAK;;;;;;;;;;;AAIb;OAjBa;AAmBN,MAAM,UAAU,CAAC,EAAE,OAAO,EAAE,EAAqB;IACtD,qBACE,sSAAC;QACC,QAAQ;QACR,gBAAe;QACf,SAAQ;QACR,OAAO;QACP,OAAO;YAAE,OAAO;QAAe;kBAE/B,cAAA,sSAAC;YACC,UAAS;YACT,UAAS;YACT,GAAE;YACF,MAAK;;;;;;;;;;;AAIb;OAjBa;AAmBN,MAAM,YAAY,CAAC,EAAE,OAAO,EAAE,EAAqB;IACxD,qBACE,sSAAC;QACC,QAAQ;QACR,gBAAe;QACf,SAAQ;QACR,OAAO;QACP,OAAO;YAAE,OAAO;QAAe;kBAE/B,cAAA,sSAAC;YACC,UAAS;YACT,UAAS;YACT,GAAE;YACF,MAAK;;;;;;;;;;;AAIb;OAjBa;AAmBN,MAAM,WAAW,CAAC,EAAE,OAAO,EAAE,EAAqB;IACvD,qBACE,sSAAC;QACC,QAAQ;QACR,gBAAe;QACf,SAAQ;QACR,OAAO;QACP,OAAO;YAAE,OAAO;QAAe;kBAE/B,cAAA,sSAAC;YACC,UAAS;YACT,UAAS;YACT,GAAE;YACF,MAAK;;;;;;;;;;;AAIb;OAjBa;AAmBN,MAAM,WAAW,CAAC,EAAE,OAAO,EAAE,EAAqB;IACvD,qBACE,sSAAC;QACC,QAAQ;QACR,gBAAe;QACf,SAAQ;QACR,OAAO;QACP,OAAO;YAAE,OAAO;QAAe;kBAE/B,cAAA,sSAAC;YACC,UAAS;YACT,UAAS;YACT,GAAE;YACF,MAAK;;;;;;;;;;;AAIb;OAjBa;AAmBN,MAAM,aAAa,CAAC,EAAE,OAAO,EAAE,EAAqB;IACzD,qBACE,sSAAC;QACC,QAAQ;QACR,gBAAe;QACf,SAAQ;QACR,OAAO;QACP,OAAO;YAAE,OAAO;QAAe;;0BAE/B,sSAAC;gBACC,GAAE;gBACF,MAAK;;;;;;0BAEP,sSAAC;gBACC,GAAE;gBACF,MAAK;;;;;;;;;;;;AAIb;OAnBa;AAqBN,MAAM,qBAAqB,CAAC,EAAE,OAAO,EAAE,EAAqB;IACjE,qBACE,sSAAC;QACC,QAAQ;QACR,gBAAe;QACf,SAAQ;QACR,OAAO;QACP,OAAO;YAAE,OAAO;QAAe;kBAE/B,cAAA,sSAAC;YACC,UAAS;YACT,UAAS;YACT,GAAE;YACF,MAAK;;;;;;;;;;;AAIb;OAjBa;AAmBN,MAAM,eAAe,CAAC,EAAE,OAAO,EAAE,EAAqB;IAC3D,qBACE,sSAAC;QACC,QAAQ;QACR,gBAAe;QACf,SAAQ;QACR,OAAO;QACP,OAAO;YAAE,OAAO;QAAe;kBAE/B,cAAA,sSAAC;YACC,UAAS;YACT,UAAS;YACT,GAAE;YACF,MAAK;;;;;;;;;;;AAIb;OAjBa;AAmBN,MAAM,cAAc,CAAC,EAAE,OAAO,EAAE,EAAqB;IAC1D,qBACE,sSAAC;QACC,QAAQ;QACR,gBAAe;QACf,SAAQ;QACR,OAAO;QACP,OAAO;YAAE,OAAO;QAAe;kBAE/B,cAAA,sSAAC;YACC,UAAS;YACT,UAAS;YACT,GAAE;YACF,MAAK;;;;;;;;;;;AAIb;OAjBa;AAmBN,MAAM,WAAW,CAAC,EAAE,OAAO,EAAE,EAAqB;IACvD,qBACE,sSAAC;QACC,QAAQ;QACR,gBAAe;QACf,SAAQ;QACR,OAAO;QACP,OAAO;YAAE,OAAO;QAAe;kBAE/B,cAAA,sSAAC;YACC,UAAS;YACT,UAAS;YACT,GAAE;YACF,MAAK;;;;;;;;;;;AAIb;OAjBa;AAmBN,MAAM,YAAY,CAAC,EAAE,OAAO,EAAE,EAAqB;IACxD,qBACE,sSAAC;QACC,QAAQ;QACR,gBAAe;QACf,SAAQ;QACR,OAAO;QACP,OAAO;YAAE,OAAO;QAAe;kBAE/B,cAAA,sSAAC;YACC,UAAS;YACT,UAAS;YACT,GAAE;YACF,MAAK;;;;;;;;;;;AAIb;OAjBa;AAmBN,MAAM,iBAAiB,CAAC,EAAE,OAAO,EAAE,EAAqB,iBAC7D,sSAAC;QACC,QAAQ;QACR,gBAAe;QACf,SAAQ;QACR,OAAO;QACP,OAAO;YAAE,OAAO;QAAe;kBAE/B,cAAA,sSAAC;YACC,UAAS;YACT,UAAS;YACT,GAAE;YACF,MAAK;;;;;;;;;;;OAZE", "debugId": null}}, {"offset": {"line": 3369, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/components/preview-attachment.tsx"], "sourcesContent": ["import { Attachment } from \"ai\";\r\n\r\nimport { Loader2 } from \"lucide-react\";\r\nimport React from \"react\";\r\n\r\nexport const PreviewAttachment = ({\r\n\tattachment,\r\n\tisUploading = false,\r\n\tremoveFile\r\n}: {\r\n\tattachment: Attachment,\r\n\tisUploading?: boolean,\r\n\tremoveFile?: (url: string) => void\r\n}) => {\r\n\tconst { name, url, contentType } = attachment;\r\n\r\n\treturn (\r\n\t\t(<div className=\"flex flex-col gap-2 max-w-16\">\r\n\t\t\t<div className=\"h-16 w-16 bg-muted rounded-md relative flex flex-col items-center justify-center\">\r\n\t\t\t\t{contentType ? (\r\n\t\t\t\t\tcontentType.startsWith(\"image\") ? (\r\n\t\t\t\t\t\t// NOTE: it is recommended to use next/image for images\r\n\t\t\t\t\t\t// eslint-disable-next-line @next/next/no-img-element\r\n\t\t\t\t\t\t(<img\r\n\t\t\t\t\t\t\tkey={url}\r\n\t\t\t\t\t\t\tsrc={url}\r\n\t\t\t\t\t\t\talt={name ?? \"An image attachment\"}\r\n\t\t\t\t\t\t\tclassName=\"rounded-md size-full object-cover\"\r\n\t\t\t\t\t\t/>)\r\n\t\t\t\t\t) : (\r\n\t\t\t\t\t\t<div className=\"\"></div>\r\n\t\t\t\t\t)\r\n\t\t\t\t) : (\r\n\t\t\t\t\t<div className=\"\"></div>\r\n\t\t\t\t)}\r\n\r\n\t\t\t\t{isUploading && (\r\n\t\t\t\t\t<div className=\"animate-spin absolute text-zinc-500\">\r\n\t\t\t\t\t\t<Loader2 className=\"h-4 w-4 animate-spin text-gray-500\" />\r\n\t\t\t\t\t</div>\r\n\t\t\t\t)}\r\n\t\t\t\t{removeFile &&\r\n\t\t\t\t\t<button\r\n\t\t\t\t\t\ttype=\"button\"\r\n\t\t\t\t\t\tonClick={() => removeFile(url)}\r\n\t\t\t\t\t\tclassName=\"absolute top-1 right-1 bg-red-500 text-white rounded-full w-5 h-5 flex items-center justify-center opacity-0 hover:opacity-100 transition-opacity\"\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t×\r\n\t\t\t\t\t</button>\r\n\t\t\t\t}\r\n\t\t\t</div>\r\n\t\t\t<div className=\"text-xs  max-w-16 truncate\">{name}</div>\r\n\t\t</div>)\r\n\t);\r\n};\r\n"], "names": [], "mappings": ";;;;AAEA;;;AAGO,MAAM,oBAAoB,CAAC,EACjC,UAAU,EACV,cAAc,KAAK,EACnB,UAAU,EAKV;IACA,MAAM,EAAE,IAAI,EAAE,GAAG,EAAE,WAAW,EAAE,GAAG;IAEnC,qBACE,sSAAC;QAAI,WAAU;;0BACf,sSAAC;gBAAI,WAAU;;oBACb,cACA,YAAY,UAAU,CAAC,yBAGrB,sSAAC;wBAED,KAAK;wBACL,KAAK,QAAQ;wBACb,WAAU;uBAHL;;;;6CAMN,sSAAC;wBAAI,WAAU;;;;;6CAGhB,sSAAC;wBAAI,WAAU;;;;;;oBAGf,6BACA,sSAAC;wBAAI,WAAU;kCACd,cAAA,sSAAC,wSAAA,CAAA,UAAO;4BAAC,WAAU;;;;;;;;;;;oBAGpB,4BACA,sSAAC;wBACA,MAAK;wBACL,SAAS,IAAM,WAAW;wBAC1B,WAAU;kCACV;;;;;;;;;;;;0BAKH,sSAAC;gBAAI,WAAU;0BAA8B;;;;;;;;;;;;AAGhD;KAjDa", "debugId": null}}, {"offset": {"line": 3462, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Textarea = React.forwardRef<\r\n  HTMLTextAreaElement,\r\n  React.ComponentProps<\"textarea\">\r\n>(({ className, ...props }, ref) => {\r\n  return (\r\n    <textarea\r\n      className={cn(\r\n        \"flex min-h-[60px] w-full rounded-md border border-input bg-transparent px-3 py-2 text-base shadow-sm placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\r\n        className\r\n      )}\r\n      ref={ref}\r\n      {...props}\r\n    />\r\n  )\r\n})\r\nTextarea.displayName = \"Textarea\"\r\n\r\nexport { Textarea }\r\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAEA,MAAM,yBAAW,CAAA,GAAA,sQAAA,CAAA,aAAgB,AAAD,OAG9B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IAC1B,qBACE,sSAAC;QACC,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,6QACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AACA,SAAS,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 3497, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/components/preview/suggested-actions.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { motion } from 'framer-motion';\r\nimport { Button } from '@/components/ui/button';\r\nimport { ChatRequestOptions, CreateMessage, Message } from 'ai';\r\nimport { memo } from 'react';\r\n\r\ninterface SuggestedActionsProps {\r\n  chatId: string;\r\n  append: (\r\n    message: Message | CreateMessage,\r\n    chatRequestOptions?: ChatRequestOptions,\r\n  ) => Promise<string | null | undefined>;\r\n  selectedModelId?: string;\r\n}\r\n\r\n// 모델별 제안 액션 정의\r\nconst modelSuggestedActions = {\r\n  '지자체 공간정보 플랫폼 챗봇': [\r\n    {\r\n      title: '지원 데이터',\r\n      label: '어떤 데이터를 지원하나요?',\r\n      action: '어떤 데이터를 지원하나요?',\r\n    },\r\n    {\r\n      title: '플랫폼 목적',\r\n      label: '무엇을 위한 플랫폼인가요?',\r\n      action: '무엇을 위한 플랫폼인가요?',\r\n    },\r\n    {\r\n      title: '지도공간 기능',\r\n      label: '지도공간에서는 어떤 기능을 볼 수 있나요?',\r\n      action: '지도공간에서는 어떤 기능을 볼 수 있나요?',\r\n    },\r\n    {\r\n      title: '업무공간 서비스',\r\n      label: '업무공간에는 어떤 서비스가 있나요?',\r\n      action: '업무공간에는 어떤 서비스가 있나요?',\r\n    },\r\n  ],\r\n  '지도개발 어시스턴트': [\r\n    {\r\n      title: '지도 생성하기',\r\n      label: 'ODF를 활용한 지도 만들기',\r\n      action: 'ODF를 사용해서 지도를 생성하는 방법을 알려주세요.',\r\n    },\r\n    {\r\n      title: '배경지도 컨트롤 추가',\r\n      label: '지도에 배경지도 컨트롤을 추가하는 방법',\r\n      action: '배경지도 컨트롤을 추가한 지도를 보여주세요.',\r\n    },\r\n  ],\r\n};\r\n\r\nfunction PureSuggestedActions({ chatId, append, selectedModelId = '지도개발 어시스턴트' }: SuggestedActionsProps) {\r\n  // 선택된 모델에 따라 제안 액션 가져오기\r\n  const suggestedActions = modelSuggestedActions[selectedModelId as keyof typeof modelSuggestedActions] ||\r\n                           modelSuggestedActions['지도개발 어시스턴트'];\r\n\r\n  // 지자체 공간정보 플랫폼 챗봇의 경우 4개 액션을 모두 표시\r\n  const isGovPlatform = selectedModelId === '지자체 공간정보 플랫폼 챗봇';\r\n\r\n  return (\r\n    <div className=\"grid sm:grid-cols-2 gap-2 w-full\">\r\n      {suggestedActions.map((suggestedAction, index) => (\r\n        <motion.div\r\n          initial={{ opacity: 0, y: 20 }}\r\n          animate={{ opacity: 1, y: 0 }}\r\n          exit={{ opacity: 0, y: 20 }}\r\n          transition={{ delay: 0.05 * index }}\r\n          key={`suggested-action-${suggestedAction.title}-${index}`}\r\n          className={!isGovPlatform && index > 1 ? 'hidden sm:block' : 'block'}\r\n        >\r\n          <Button\r\n            variant=\"ghost\"\r\n            onClick={async () => {\r\n              // window.history.replaceState({}, '', `/chat/${chatId}`);\r\n\r\n              append({\r\n                role: 'user',\r\n                content: suggestedAction.action,\r\n              });\r\n            }}\r\n            className=\"text-left border rounded-xl px-4 py-3.5 text-sm flex-1 gap-1 sm:flex-col w-full h-auto justify-start items-start\"\r\n          >\r\n            <span className=\"font-medium\">{suggestedAction.title}</span>\r\n            <span className=\"text-muted-foreground\">\r\n              {suggestedAction.label}\r\n            </span>\r\n          </Button>\r\n        </motion.div>\r\n      ))}\r\n    </div>\r\n  );\r\n}\r\n\r\nexport const SuggestedActions = memo(PureSuggestedActions, () => true);\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AALA;;;;;AAgBA,eAAe;AACf,MAAM,wBAAwB;IAC5B,mBAAmB;QACjB;YACE,OAAO;YACP,OAAO;YACP,QAAQ;QACV;QACA;YACE,OAAO;YACP,OAAO;YACP,QAAQ;QACV;QACA;YACE,OAAO;YACP,OAAO;YACP,QAAQ;QACV;QACA;YACE,OAAO;YACP,OAAO;YACP,QAAQ;QACV;KACD;IACD,cAAc;QACZ;YACE,OAAO;YACP,OAAO;YACP,QAAQ;QACV;QACA;YACE,OAAO;YACP,OAAO;YACP,QAAQ;QACV;KACD;AACH;AAEA,SAAS,qBAAqB,EAAE,MAAM,EAAE,MAAM,EAAE,kBAAkB,YAAY,EAAyB;IACrG,wBAAwB;IACxB,MAAM,mBAAmB,qBAAqB,CAAC,gBAAsD,IAC5E,qBAAqB,CAAC,aAAa;IAE5D,mCAAmC;IACnC,MAAM,gBAAgB,oBAAoB;IAE1C,qBACE,sSAAC;QAAI,WAAU;kBACZ,iBAAiB,GAAG,CAAC,CAAC,iBAAiB,sBACtC,sSAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,MAAM;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC1B,YAAY;oBAAE,OAAO,OAAO;gBAAM;gBAElC,WAAW,CAAC,iBAAiB,QAAQ,IAAI,oBAAoB;0BAE7D,cAAA,sSAAC,8HAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,SAAS;wBACP,0DAA0D;wBAE1D,OAAO;4BACL,MAAM;4BACN,SAAS,gBAAgB,MAAM;wBACjC;oBACF;oBACA,WAAU;;sCAEV,sSAAC;4BAAK,WAAU;sCAAe,gBAAgB,KAAK;;;;;;sCACpD,sSAAC;4BAAK,WAAU;sCACb,gBAAgB,KAAK;;;;;;;;;;;;eAjBrB,CAAC,iBAAiB,EAAE,gBAAgB,KAAK,CAAC,CAAC,EAAE,OAAO;;;;;;;;;;AAwBnE;KAxCS;AA0CF,MAAM,iCAAmB,CAAA,GAAA,sQAAA,CAAA,OAAI,AAAD,EAAE,sBAAsB,IAAM", "debugId": null}}, {"offset": {"line": 3629, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/components/multimodal-input.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport type {\r\n  Attachment,\r\n  ChatRequestOptions,\r\n  CreateMessage,\r\n  Message,\r\n} from 'ai';\r\nimport type React from 'react';\r\nimport {\r\n  useRef,\r\n  useEffect,\r\n  useState,\r\n  useCallback,\r\n  type Dispatch,\r\n  type SetStateAction,\r\n  type ChangeEvent,\r\n  memo,\r\n} from 'react';\r\nimport { toast } from 'sonner';\r\nimport { useLocalStorage, useWindowSize } from 'usehooks-ts';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nimport { ArrowUpIcon, PaperclipIcon, StopIcon } from './icons';\r\nimport { PreviewAttachment } from './preview-attachment';\r\nimport { Button } from './ui/button';\r\nimport { Textarea } from './ui/textarea';\r\nimport { SuggestedActions } from './preview/suggested-actions';\r\nimport equal from 'fast-deep-equal';\r\n\r\nfunction PureMultimodalInput({\r\n  chatId,\r\n  input,\r\n  setInput,\r\n  isLoading,\r\n  stop,\r\n  attachments,\r\n  setAttachments,\r\n  messages,\r\n  setMessages,\r\n  append,\r\n  handleSubmit,\r\n  className,\r\n  selectedModelId,\r\n}: {\r\n  chatId: string;\r\n  input: string;\r\n  setInput: (value: string) => void;\r\n  isLoading: boolean;\r\n  stop: () => void;\r\n  attachments: Array<Attachment>;\r\n  setAttachments: Dispatch<SetStateAction<Array<Attachment>>>;\r\n  messages: Array<Message>;\r\n  setMessages: Dispatch<SetStateAction<Array<Message>>>;\r\n  append: (\r\n    message: Message | CreateMessage,\r\n    chatRequestOptions?: ChatRequestOptions,\r\n  ) => Promise<string | null | undefined>;\r\n  handleSubmit: (\r\n    event?: {\r\n      preventDefault?: () => void;\r\n    },\r\n    chatRequestOptions?: ChatRequestOptions,\r\n  ) => void;\r\n  className?: string;\r\n  selectedModelId?: string;\r\n}) {\r\n  const textareaRef = useRef<HTMLTextAreaElement>(null);\r\n  const { width } = useWindowSize();\r\n\r\n  useEffect(() => {\r\n    if (textareaRef.current) {\r\n      adjustHeight();\r\n    }\r\n  }, []);\r\n\r\n  const adjustHeight = () => {\r\n    if (textareaRef.current) {\r\n      textareaRef.current.style.height = 'auto';\r\n      textareaRef.current.style.height = `${textareaRef.current.scrollHeight + 2}px`;\r\n    }\r\n  };\r\n\r\n  const [localStorageInput, setLocalStorageInput] = useLocalStorage(\r\n    'input',\r\n    '',\r\n  );\r\n\r\n  useEffect(() => {\r\n    if (textareaRef.current) {\r\n      const domValue = textareaRef.current.value;\r\n      // Prefer DOM value over localStorage to handle hydration\r\n      const finalValue = domValue || localStorageInput || '';\r\n      setInput(finalValue);\r\n      adjustHeight();\r\n    }\r\n    // Only run once after hydration\r\n    // eslint-disable-next-line react-hooks/exhaustive-deps\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    setLocalStorageInput(input);\r\n  }, [input, setLocalStorageInput]);\r\n\r\n  const handleInput = (event: React.ChangeEvent<HTMLTextAreaElement>) => {\r\n    setInput(event.target.value);\r\n    adjustHeight();\r\n  };\r\n\r\n  const fileInputRef = useRef<HTMLInputElement>(null);\r\n  const [uploadQueue, setUploadQueue] = useState<Array<string>>([]);\r\n\r\n  const submitForm = useCallback(() => {\r\n    // window.history.replaceState({}, '', `/chat/${chatId}`);\r\n\r\n    handleSubmit(undefined, {\r\n      experimental_attachments: attachments,\r\n    });\r\n\r\n    setAttachments([]);\r\n    setLocalStorageInput('');\r\n\r\n    if (width && width > 768) {\r\n      textareaRef.current?.focus();\r\n    }\r\n  }, [\r\n    attachments,\r\n    handleSubmit,\r\n    setAttachments,\r\n    setLocalStorageInput,\r\n    width,\r\n    chatId,\r\n  ]);\r\n\r\n  const uploadFile = async (file: File) => {\r\n    const formData = new FormData();\r\n    formData.append('file', file);\r\n\r\n    try {\r\n      const response = await fetch('/api/files/upload', {\r\n        method: 'POST',\r\n        body: formData,\r\n      });\r\n\r\n      if (response.ok) {\r\n        const data = await response.json();\r\n        const { url, pathname, contentType } = data;\r\n\r\n        return {\r\n          url,\r\n          name: pathname,\r\n          contentType: contentType,\r\n        };\r\n      }\r\n      const { error } = await response.json();\r\n      toast.error(error);\r\n    } catch (error) {\r\n      toast.error('Failed to upload file, please try again!');\r\n    }\r\n  };\r\n\r\n  const handleFileChange = useCallback(\r\n    async (event: ChangeEvent<HTMLInputElement>) => {\r\n      const files = Array.from(event.target.files || []);\r\n\r\n      setUploadQueue(files.map((file) => file.name));\r\n\r\n      try {\r\n        const uploadPromises = files.map((file) => uploadFile(file));\r\n        const uploadedAttachments = await Promise.all(uploadPromises);\r\n        const successfullyUploadedAttachments = uploadedAttachments.filter(\r\n          (attachment) => attachment !== undefined,\r\n        );\r\n\r\n        setAttachments((currentAttachments) => [\r\n          ...currentAttachments,\r\n          ...successfullyUploadedAttachments,\r\n        ]);\r\n      } catch (error) {\r\n        console.error('Error uploading files!', error);\r\n      } finally {\r\n        setUploadQueue([]);\r\n      }\r\n    },\r\n    [setAttachments],\r\n  );\r\n\r\n  return (\r\n    <div className=\"relative w-full flex flex-col gap-4\">\r\n      {messages.length === 0 &&\r\n        attachments.length === 0 &&\r\n        uploadQueue.length === 0 && (\r\n          <SuggestedActions append={append} chatId={chatId} selectedModelId={selectedModelId} />\r\n        )}\r\n\r\n      <input\r\n        type=\"file\"\r\n        className=\"fixed -top-4 -left-4 size-0.5 opacity-0 pointer-events-none\"\r\n        ref={fileInputRef}\r\n        multiple\r\n        onChange={handleFileChange}\r\n        tabIndex={-1}\r\n      />\r\n\r\n      {(attachments.length > 0 || uploadQueue.length > 0) && (\r\n        <div className=\"flex flex-row gap-2 overflow-x-scroll items-end\">\r\n          {attachments.map((attachment) => (\r\n            <PreviewAttachment key={attachment.url} attachment={attachment} />\r\n          ))}\r\n\r\n          {uploadQueue.map((filename) => (\r\n            <PreviewAttachment\r\n              key={filename}\r\n              attachment={{\r\n                url: '',\r\n                name: filename,\r\n                contentType: '',\r\n              }}\r\n              isUploading={true}\r\n            />\r\n          ))}\r\n        </div>\r\n      )}\r\n      <div className=\"ai-textarea-border w-full rounded-[28px] p-1\">\r\n        <Textarea\r\n          ref={textareaRef}\r\n          placeholder=\"자유롭게 질문해보세요.\"\r\n          value={input}\r\n          onChange={handleInput}\r\n          className={cn(\r\n            'min-h-[24px] max-h-[calc(75dvh)] overflow-hidden resize-none rounded-[28px] p-4 pr-12 !text-base bg-background',\r\n            className,\r\n          )}\r\n          rows={3}\r\n          autoFocus\r\n          onKeyDown={(event) => {\r\n            if (event.key === 'Enter' && !event.shiftKey) {\r\n              event.preventDefault();\r\n\r\n              if (isLoading) {\r\n                toast.error('응답이 완료되기 전까지 잠시만 기다려주세요.');\r\n              } else {\r\n                submitForm();\r\n              }\r\n            }\r\n          }}\r\n        />\r\n        \r\n        {isLoading ? (\r\n          <StopButton stop={stop} setMessages={setMessages} />\r\n        ) : (\r\n          <SendButton\r\n            input={input}\r\n            submitForm={submitForm}\r\n            uploadQueue={uploadQueue}\r\n          />\r\n        )}\r\n      </div>\r\n\r\n\r\n\r\n      {/* <AttachmentsButton fileInputRef={fileInputRef} isLoading={isLoading} /> */}\r\n    </div>\r\n  );\r\n}\r\n\r\nexport const MultimodalInput = memo(\r\n  PureMultimodalInput,\r\n  (prevProps, nextProps) => {\r\n    if (prevProps.input !== nextProps.input) return false;\r\n    if (prevProps.isLoading !== nextProps.isLoading) return false;\r\n    if (!equal(prevProps.attachments, nextProps.attachments)) return false;\r\n\r\n    return true;\r\n  },\r\n);\r\n\r\nfunction PureAttachmentsButton({\r\n  fileInputRef,\r\n  isLoading,\r\n}: {\r\n  fileInputRef: React.MutableRefObject<HTMLInputElement | null>;\r\n  isLoading: boolean;\r\n}) {\r\n  return (\r\n    <Button\r\n      className=\"rounded-full p-1.5 h-fit absolute bottom-2.5 right-11 m-0.5 dark:border-zinc-700\"\r\n      onClick={(event) => {\r\n        event.preventDefault();\r\n        fileInputRef.current?.click();\r\n      }}\r\n      variant=\"outline\"\r\n      disabled={isLoading}\r\n    >\r\n      <PaperclipIcon size={14} />\r\n    </Button>\r\n  );\r\n}\r\n\r\nconst AttachmentsButton = memo(PureAttachmentsButton);\r\n\r\nfunction PureStopButton({\r\n  stop,\r\n  setMessages,\r\n}: {\r\n  stop: () => void;\r\n  setMessages: Dispatch<SetStateAction<Array<Message>>>;\r\n}) {\r\n  return (\r\n    <Button\r\n      className=\"rounded-full p-1.5 h-fit absolute bottom-10 right-4 m-0.5 border dark:border-zinc-600\"\r\n      onClick={(event) => {\r\n        event.preventDefault();\r\n        stop();\r\n        // AI SDK 공식 패턴: appendResponseMessages로 저장된 메시지는 이미 정리됨\r\n        // 별도의 sanitize 불필요\r\n      }}\r\n    >\r\n      <StopIcon size={14} />\r\n    </Button>\r\n  );\r\n}\r\n\r\nconst StopButton = memo(PureStopButton);\r\n\r\nfunction PureSendButton({\r\n  submitForm,\r\n  input,\r\n  uploadQueue,\r\n}: {\r\n  submitForm: () => void;\r\n  input: string;\r\n  uploadQueue: Array<string>;\r\n}) {\r\n  return (\r\n    <Button\r\n      className=\"rounded-full p-1.5 h-fit absolute bottom-10 right-4 m-0.5 border dark:border-zinc-600\"\r\n      onClick={(event) => {\r\n        event.preventDefault();\r\n        submitForm();\r\n      }}\r\n      disabled={input.length === 0 || uploadQueue.length > 0}\r\n    >\r\n      <ArrowUpIcon size={14} />\r\n    </Button>\r\n  );\r\n}\r\n\r\nconst SendButton = memo(PureSendButton, (prevProps, nextProps) => {\r\n  if (prevProps.uploadQueue.length !== nextProps.uploadQueue.length)\r\n    return false;\r\n  if (!prevProps.input !== !nextProps.input) return false;\r\n  return true;\r\n});\r\n"], "names": [], "mappings": ";;;;AASA;AAUA;AACA;AAEA;AAEA;AACA;AACA;AACA;AACA;AACA;;;AA7BA;;;;;;;;;;;AA+BA,SAAS,oBAAoB,EAC3B,MAAM,EACN,KAAK,EACL,QAAQ,EACR,SAAS,EACT,IAAI,EACJ,WAAW,EACX,cAAc,EACd,QAAQ,EACR,WAAW,EACX,MAAM,EACN,YAAY,EACZ,SAAS,EACT,eAAe,EAuBhB;;IACC,MAAM,cAAc,CAAA,GAAA,sQAAA,CAAA,SAAM,AAAD,EAAuB;IAChD,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,mOAAA,CAAA,gBAAa,AAAD;IAE9B,CAAA,GAAA,sQAAA,CAAA,YAAS,AAAD;yCAAE;YACR,IAAI,YAAY,OAAO,EAAE;gBACvB;YACF;QACF;wCAAG,EAAE;IAEL,MAAM,eAAe;QACnB,IAAI,YAAY,OAAO,EAAE;YACvB,YAAY,OAAO,CAAC,KAAK,CAAC,MAAM,GAAG;YACnC,YAAY,OAAO,CAAC,KAAK,CAAC,MAAM,GAAG,GAAG,YAAY,OAAO,CAAC,YAAY,GAAG,EAAE,EAAE,CAAC;QAChF;IACF;IAEA,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,mOAAA,CAAA,kBAAe,AAAD,EAC9D,SACA;IAGF,CAAA,GAAA,sQAAA,CAAA,YAAS,AAAD;yCAAE;YACR,IAAI,YAAY,OAAO,EAAE;gBACvB,MAAM,WAAW,YAAY,OAAO,CAAC,KAAK;gBAC1C,yDAAyD;gBACzD,MAAM,aAAa,YAAY,qBAAqB;gBACpD,SAAS;gBACT;YACF;QACA,gCAAgC;QAChC,uDAAuD;QACzD;wCAAG,EAAE;IAEL,CAAA,GAAA,sQAAA,CAAA,YAAS,AAAD;yCAAE;YACR,qBAAqB;QACvB;wCAAG;QAAC;QAAO;KAAqB;IAEhC,MAAM,cAAc,CAAC;QACnB,SAAS,MAAM,MAAM,CAAC,KAAK;QAC3B;IACF;IAEA,MAAM,eAAe,CAAA,GAAA,sQAAA,CAAA,SAAM,AAAD,EAAoB;IAC9C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAiB,EAAE;IAEhE,MAAM,aAAa,CAAA,GAAA,sQAAA,CAAA,cAAW,AAAD;uDAAE;YAC7B,0DAA0D;YAE1D,aAAa,WAAW;gBACtB,0BAA0B;YAC5B;YAEA,eAAe,EAAE;YACjB,qBAAqB;YAErB,IAAI,SAAS,QAAQ,KAAK;gBACxB,YAAY,OAAO,EAAE;YACvB;QACF;sDAAG;QACD;QACA;QACA;QACA;QACA;QACA;KACD;IAED,MAAM,aAAa,OAAO;QACxB,MAAM,WAAW,IAAI;QACrB,SAAS,MAAM,CAAC,QAAQ;QAExB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,qBAAqB;gBAChD,QAAQ;gBACR,MAAM;YACR;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,WAAW,EAAE,GAAG;gBAEvC,OAAO;oBACL;oBACA,MAAM;oBACN,aAAa;gBACf;YACF;YACA,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI;YACrC,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,EAAE,OAAO,OAAO;YACd,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,MAAM,mBAAmB,CAAA,GAAA,sQAAA,CAAA,cAAW,AAAD;6DACjC,OAAO;YACL,MAAM,QAAQ,MAAM,IAAI,CAAC,MAAM,MAAM,CAAC,KAAK,IAAI,EAAE;YAEjD,eAAe,MAAM,GAAG;qEAAC,CAAC,OAAS,KAAK,IAAI;;YAE5C,IAAI;gBACF,MAAM,iBAAiB,MAAM,GAAG;wFAAC,CAAC,OAAS,WAAW;;gBACtD,MAAM,sBAAsB,MAAM,QAAQ,GAAG,CAAC;gBAC9C,MAAM,kCAAkC,oBAAoB,MAAM;yGAChE,CAAC,aAAe,eAAe;;gBAGjC;yEAAe,CAAC,qBAAuB;+BAClC;+BACA;yBACJ;;YACH,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,0BAA0B;YAC1C,SAAU;gBACR,eAAe,EAAE;YACnB;QACF;4DACA;QAAC;KAAe;IAGlB,qBACE,sSAAC;QAAI,WAAU;;YACZ,SAAS,MAAM,KAAK,KACnB,YAAY,MAAM,KAAK,KACvB,YAAY,MAAM,KAAK,mBACrB,sSAAC,iJAAA,CAAA,mBAAgB;gBAAC,QAAQ;gBAAQ,QAAQ;gBAAQ,iBAAiB;;;;;;0BAGvE,sSAAC;gBACC,MAAK;gBACL,WAAU;gBACV,KAAK;gBACL,QAAQ;gBACR,UAAU;gBACV,UAAU,CAAC;;;;;;YAGZ,CAAC,YAAY,MAAM,GAAG,KAAK,YAAY,MAAM,GAAG,CAAC,mBAChD,sSAAC;gBAAI,WAAU;;oBACZ,YAAY,GAAG,CAAC,CAAC,2BAChB,sSAAC,uIAAA,CAAA,oBAAiB;4BAAsB,YAAY;2BAA5B,WAAW,GAAG;;;;;oBAGvC,YAAY,GAAG,CAAC,CAAC,yBAChB,sSAAC,uIAAA,CAAA,oBAAiB;4BAEhB,YAAY;gCACV,KAAK;gCACL,MAAM;gCACN,aAAa;4BACf;4BACA,aAAa;2BANR;;;;;;;;;;;0BAWb,sSAAC;gBAAI,WAAU;;kCACb,sSAAC,gIAAA,CAAA,WAAQ;wBACP,KAAK;wBACL,aAAY;wBACZ,OAAO;wBACP,UAAU;wBACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,kHACA;wBAEF,MAAM;wBACN,SAAS;wBACT,WAAW,CAAC;4BACV,IAAI,MAAM,GAAG,KAAK,WAAW,CAAC,MAAM,QAAQ,EAAE;gCAC5C,MAAM,cAAc;gCAEpB,IAAI,WAAW;oCACb,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gCACd,OAAO;oCACL;gCACF;4BACF;wBACF;;;;;;oBAGD,0BACC,sSAAC;wBAAW,MAAM;wBAAM,aAAa;;;;;6CAErC,sSAAC;wBACC,OAAO;wBACP,YAAY;wBACZ,aAAa;;;;;;;;;;;;;;;;;;AAUzB;GA1OS;;QAsCW,mOAAA,CAAA,gBAAa;QAemB,mOAAA,CAAA,kBAAe;;;KArD1D;AA4OF,MAAM,gCAAkB,CAAA,GAAA,sQAAA,CAAA,OAAI,AAAD,EAChC,qBACA,CAAC,WAAW;IACV,IAAI,UAAU,KAAK,KAAK,UAAU,KAAK,EAAE,OAAO;IAChD,IAAI,UAAU,SAAS,KAAK,UAAU,SAAS,EAAE,OAAO;IACxD,IAAI,CAAC,CAAA,GAAA,mNAAA,CAAA,UAAK,AAAD,EAAE,UAAU,WAAW,EAAE,UAAU,WAAW,GAAG,OAAO;IAEjE,OAAO;AACT;;AAGF,SAAS,sBAAsB,EAC7B,YAAY,EACZ,SAAS,EAIV;IACC,qBACE,sSAAC,8HAAA,CAAA,SAAM;QACL,WAAU;QACV,SAAS,CAAC;YACR,MAAM,cAAc;YACpB,aAAa,OAAO,EAAE;QACxB;QACA,SAAQ;QACR,UAAU;kBAEV,cAAA,sSAAC,uHAAA,CAAA,gBAAa;YAAC,MAAM;;;;;;;;;;;AAG3B;MApBS;AAsBT,MAAM,kCAAoB,CAAA,GAAA,sQAAA,CAAA,OAAI,AAAD,EAAE;;AAE/B,SAAS,eAAe,EACtB,IAAI,EACJ,WAAW,EAIZ;IACC,qBACE,sSAAC,8HAAA,CAAA,SAAM;QACL,WAAU;QACV,SAAS,CAAC;YACR,MAAM,cAAc;YACpB;QACA,wDAAwD;QACxD,mBAAmB;QACrB;kBAEA,cAAA,sSAAC,uHAAA,CAAA,WAAQ;YAAC,MAAM;;;;;;;;;;;AAGtB;MApBS;AAsBT,MAAM,2BAAa,CAAA,GAAA,sQAAA,CAAA,OAAI,AAAD,EAAE;MAAlB;AAEN,SAAS,eAAe,EACtB,UAAU,EACV,KAAK,EACL,WAAW,EAKZ;IACC,qBACE,sSAAC,8HAAA,CAAA,SAAM;QACL,WAAU;QACV,SAAS,CAAC;YACR,MAAM,cAAc;YACpB;QACF;QACA,UAAU,MAAM,MAAM,KAAK,KAAK,YAAY,MAAM,GAAG;kBAErD,cAAA,sSAAC,uHAAA,CAAA,cAAW;YAAC,MAAM;;;;;;;;;;;AAGzB;MArBS;AAuBT,MAAM,2BAAa,CAAA,GAAA,sQAAA,CAAA,OAAI,AAAD,EAAE,gBAAgB,CAAC,WAAW;IAClD,IAAI,UAAU,WAAW,CAAC,MAAM,KAAK,UAAU,WAAW,CAAC,MAAM,EAC/D,OAAO;IACT,IAAI,CAAC,UAAU,KAAK,KAAK,CAAC,UAAU,KAAK,EAAE,OAAO;IAClD,OAAO;AACT;MALM", "debugId": null}}, {"offset": {"line": 3990, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/lib/hooks/use-preview.ts"], "sourcesContent": ["import { useCallback, useMemo } from 'react';\r\nimport useS<PERSON> from 'swr';\r\n\r\nconst initialPreviewData: PreviewBlock = {\r\n  documentId: 'init',\r\n  content: '',\r\n  kind: 'text',\r\n  title: '',\r\n  status: 'idle',\r\n  isVisible: false,\r\n  boundingBox: {\r\n    top: 0,\r\n    left: 0,\r\n    width: 0,\r\n    height: 0,\r\n  },\r\n};\r\n\r\nexport interface PreviewBlock {\r\n  documentId: string;\r\n  content: string;\r\n  kind: string;\r\n  title: string;\r\n  status: 'idle' | 'streaming';\r\n  isVisible: boolean;\r\n  boundingBox: {\r\n    top: number;\r\n    left: number;\r\n    width: number;\r\n    height: number;\r\n  };\r\n}\r\n\r\n\r\nexport function usePreview() {\r\n  const { data: localPreview, mutate: setLocalPreview } = useSWR<PreviewBlock>(\r\n    'preview',\r\n    null,\r\n    {\r\n      fallbackData: initialPreviewData,\r\n    }\r\n  );\r\n\r\n  const preview = useMemo(() => {\r\n    if (!localPreview) return initialPreviewData;\r\n    return localPreview;\r\n  }, [localPreview]);\r\n\r\n  const setPreview = useCallback(\r\n    (updaterFn: PreviewBlock | ((currentPreview: PreviewBlock) => PreviewBlock)) => {\r\n      setLocalPreview((currentPreview) => {\r\n        const previewToUpdate = currentPreview || initialPreviewData;\r\n\r\n        if (typeof updaterFn === 'function') {\r\n          return updaterFn(previewToUpdate);\r\n        }\r\n\r\n        return updaterFn;\r\n      });\r\n    },\r\n    [setLocalPreview]\r\n  );\r\n\r\n  return useMemo(() => ({ preview, setPreview }), [preview, setPreview]);\r\n}"], "names": [], "mappings": ";;;AAAA;AACA;AAAA;;;;AAEA,MAAM,qBAAmC;IACvC,YAAY;IACZ,SAAS;IACT,MAAM;IACN,OAAO;IACP,QAAQ;IACR,WAAW;IACX,aAAa;QACX,KAAK;QACL,MAAM;QACN,OAAO;QACP,QAAQ;IACV;AACF;AAkBO,SAAS;;IACd,MAAM,EAAE,MAAM,YAAY,EAAE,QAAQ,eAAe,EAAE,GAAG,CAAA,GAAA,sOAAA,CAAA,UAAM,AAAD,EAC3D,WACA,MACA;QACE,cAAc;IAChB;IAGF,MAAM,UAAU,CAAA,GAAA,sQAAA,CAAA,UAAO,AAAD;uCAAE;YACtB,IAAI,CAAC,cAAc,OAAO;YAC1B,OAAO;QACT;sCAAG;QAAC;KAAa;IAEjB,MAAM,aAAa,CAAA,GAAA,sQAAA,CAAA,cAAW,AAAD;8CAC3B,CAAC;YACC;sDAAgB,CAAC;oBACf,MAAM,kBAAkB,kBAAkB;oBAE1C,IAAI,OAAO,cAAc,YAAY;wBACnC,OAAO,UAAU;oBACnB;oBAEA,OAAO;gBACT;;QACF;6CACA;QAAC;KAAgB;IAGnB,OAAO,CAAA,GAAA,sQAAA,CAAA,UAAO,AAAD;8BAAE,IAAM,CAAC;gBAAE;gBAAS;YAAW,CAAC;6BAAG;QAAC;QAAS;KAAW;AACvE;GA9BgB;;QAC0C,sOAAA,CAAA,UAAM", "debugId": null}}, {"offset": {"line": 4065, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/components/code-editor.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { EditorView, lineNumbers as createLineNumbers } from '@codemirror/view';\r\nimport { EditorState, Transaction } from '@codemirror/state';\r\nimport { javascript } from '@codemirror/lang-javascript';\r\nimport { python } from '@codemirror/lang-python';\r\nimport { html } from '@codemirror/lang-html';\r\nimport { oneDark } from '@codemirror/theme-one-dark';\r\nimport { minimalSetup } from 'codemirror';\r\nimport React, { memo, useEffect, useRef } from 'react';\r\nimport { useTheme } from 'next-themes';\r\n\r\n// 에디터 테마 스타일 확장\r\nconst modernThemeLight = EditorView.theme({\r\n  '&': {\r\n    backgroundColor: '#fafafa',\r\n    height: '100%',\r\n    fontSize: '14px',\r\n    borderRadius: '6px',\r\n  },\r\n  '.cm-content': {\r\n    fontFamily: 'ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, monospace',\r\n    padding: '1rem',\r\n  },\r\n  '.cm-line': {\r\n    padding: '0 4px',\r\n    lineHeight: '1.6',\r\n  },\r\n  '.cm-matchingBracket': {\r\n    backgroundColor: '#e2e8f0',\r\n    color: '#1e293b',\r\n  },\r\n  '.cm-activeLine': {\r\n    backgroundColor: '#f8fafc',\r\n  },\r\n});\r\n\r\nconst modernThemeDark = EditorView.theme({\r\n  '&': {\r\n    backgroundColor: '#18181b',\r\n    height: '100%',\r\n    fontSize: '14px',\r\n    borderRadius: '6px',\r\n  },\r\n  '.cm-content': {\r\n    fontFamily: 'ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, monospace',\r\n    padding: '1rem',\r\n  },\r\n  '.cm-line': {\r\n    padding: '0 4px',\r\n    lineHeight: '1.6',\r\n  },\r\n  '.cm-matchingBracket': {\r\n    backgroundColor: '#27272a',\r\n    color: '#e4e4e7',\r\n  },\r\n  '.cm-activeLine': {\r\n    backgroundColor: '#1f1f23',\r\n  },\r\n});\r\n\r\nconst customSetup = [\r\n  minimalSetup,\r\n  EditorView.lineWrapping,\r\n  EditorState.allowMultipleSelections.of(true),\r\n  EditorView.contentAttributes.of({ autocomplete: 'off' }),\r\n];\r\n\r\ntype EditorProps = {\r\n  content: string;\r\n  status: 'streaming' | 'idle';\r\n  language?: string;\r\n  mode?: 'view' | 'edit';\r\n  lineNumbers?: boolean;\r\n  onChange?: (value: string) => void;\r\n};\r\n\r\nfunction PureCodeEditor({ \r\n  content, \r\n  status, \r\n  language = 'javascript',\r\n  mode = 'view',\r\n  lineNumbers = false,\r\n  onChange,\r\n}: EditorProps) {\r\n  const containerRef = useRef<HTMLDivElement>(null);\r\n  const editorRef = useRef<EditorView | null>(null);\r\n  const { resolvedTheme } = useTheme();\r\n\r\n  const getLanguageExtension = (lang: EditorProps['language']) => {\r\n    switch (lang) {\r\n      case 'html':\r\n        return html();\r\n      case 'javascript':\r\n      case 'typescript':\r\n        return javascript();\r\n      case 'python':\r\n        return python();\r\n        \r\n      default:\r\n        return javascript();\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    if (containerRef.current && !editorRef.current) {\r\n      const startState = EditorState.create({\r\n        doc: content,\r\n        extensions: [\r\n          ...customSetup,\r\n          getLanguageExtension(language),\r\n          resolvedTheme === 'dark' ? [oneDark, modernThemeDark] : modernThemeLight,\r\n          mode === 'view' ? EditorView.editable.of(false) : [],\r\n          lineNumbers ? createLineNumbers() : [],\r\n          EditorView.updateListener.of(update => {\r\n            if (update.docChanged && onChange) {\r\n              onChange(update.state.doc.toString());\r\n            }\r\n          }),\r\n        ],\r\n      });\r\n\r\n      editorRef.current = new EditorView({\r\n        state: startState,\r\n        parent: containerRef.current,\r\n      });\r\n    }\r\n\r\n    return () => {\r\n      if (editorRef.current) {\r\n        editorRef.current.destroy();\r\n        editorRef.current = null;\r\n      }\r\n    };\r\n  }, [language, resolvedTheme, mode, lineNumbers]);\r\n\r\n  useEffect(() => {\r\n    if (editorRef.current && content) {\r\n      const currentContent = editorRef.current.state.doc.toString();\r\n\r\n      if (status === 'streaming' || currentContent !== content) {\r\n        const transaction = editorRef.current.state.update({\r\n          changes: {\r\n            from: 0,\r\n            to: currentContent.length,\r\n            insert: content,\r\n          },\r\n          annotations: [Transaction.remote.of(true)],\r\n        });\r\n\r\n        editorRef.current.dispatch(transaction);\r\n      }\r\n    }\r\n  }, [content, status]);\r\n\r\n  return (\r\n    <div\r\n      className=\"relative w-full overflow-hidden rounded-lg border border-zinc-200 dark:border-zinc-800 bg-zinc-50 dark:bg-zinc-900\"\r\n      ref={containerRef}\r\n    />\r\n  );\r\n}\r\n\r\nfunction areEqual(prevProps: EditorProps, nextProps: EditorProps) {\r\n  if (prevProps.status === 'streaming' && nextProps.status === 'streaming')\r\n    return false;\r\n  if (prevProps.content !== nextProps.content) return false;\r\n  if (prevProps.language !== nextProps.language) return false;\r\n  if (prevProps.mode !== nextProps.mode) return false;\r\n  if (prevProps.lineNumbers !== nextProps.lineNumbers) return false;\r\n\r\n  return true;\r\n}\r\n\r\nexport const CodeEditor = memo(PureCodeEditor, areEqual);"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAVA;;;;;;;;;;AAYA,gBAAgB;AAChB,MAAM,mBAAmB,0NAAA,CAAA,aAAU,CAAC,KAAK,CAAC;IACxC,KAAK;QACH,iBAAiB;QACjB,QAAQ;QACR,UAAU;QACV,cAAc;IAChB;IACA,eAAe;QACb,YAAY;QACZ,SAAS;IACX;IACA,YAAY;QACV,SAAS;QACT,YAAY;IACd;IACA,uBAAuB;QACrB,iBAAiB;QACjB,OAAO;IACT;IACA,kBAAkB;QAChB,iBAAiB;IACnB;AACF;AAEA,MAAM,kBAAkB,0NAAA,CAAA,aAAU,CAAC,KAAK,CAAC;IACvC,KAAK;QACH,iBAAiB;QACjB,QAAQ;QACR,UAAU;QACV,cAAc;IAChB;IACA,eAAe;QACb,YAAY;QACZ,SAAS;IACX;IACA,YAAY;QACV,SAAS;QACT,YAAY;IACd;IACA,uBAAuB;QACrB,iBAAiB;QACjB,OAAO;IACT;IACA,kBAAkB;QAChB,iBAAiB;IACnB;AACF;AAEA,MAAM,cAAc;IAClB,qNAAA,CAAA,eAAY;IACZ,0NAAA,CAAA,aAAU,CAAC,YAAY;IACvB,2NAAA,CAAA,cAAW,CAAC,uBAAuB,CAAC,EAAE,CAAC;IACvC,0NAAA,CAAA,aAAU,CAAC,iBAAiB,CAAC,EAAE,CAAC;QAAE,cAAc;IAAM;CACvD;AAWD,SAAS,eAAe,EACtB,OAAO,EACP,MAAM,EACN,WAAW,YAAY,EACvB,OAAO,MAAM,EACb,cAAc,KAAK,EACnB,QAAQ,EACI;;IACZ,MAAM,eAAe,CAAA,GAAA,sQAAA,CAAA,SAAM,AAAD,EAAkB;IAC5C,MAAM,YAAY,CAAA,GAAA,sQAAA,CAAA,SAAM,AAAD,EAAqB;IAC5C,MAAM,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,4PAAA,CAAA,WAAQ,AAAD;IAEjC,MAAM,uBAAuB,CAAC;QAC5B,OAAQ;YACN,KAAK;gBACH,OAAO,CAAA,GAAA,yOAAA,CAAA,OAAI,AAAD;YACZ,KAAK;YACL,KAAK;gBACH,OAAO,CAAA,GAAA,qPAAA,CAAA,aAAU,AAAD;YAClB,KAAK;gBACH,OAAO,CAAA,GAAA,6OAAA,CAAA,SAAM,AAAD;YAEd;gBACE,OAAO,CAAA,GAAA,qPAAA,CAAA,aAAU,AAAD;QACpB;IACF;IAEA,CAAA,GAAA,sQAAA,CAAA,YAAS,AAAD;oCAAE;YACR,IAAI,aAAa,OAAO,IAAI,CAAC,UAAU,OAAO,EAAE;gBAC9C,MAAM,aAAa,2NAAA,CAAA,cAAW,CAAC,MAAM,CAAC;oBACpC,KAAK;oBACL,YAAY;2BACP;wBACH,qBAAqB;wBACrB,kBAAkB,SAAS;4BAAC,yPAAA,CAAA,UAAO;4BAAE;yBAAgB,GAAG;wBACxD,SAAS,SAAS,0NAAA,CAAA,aAAU,CAAC,QAAQ,CAAC,EAAE,CAAC,SAAS,EAAE;wBACpD,cAAc,CAAA,GAAA,0NAAA,CAAA,cAAiB,AAAD,MAAM,EAAE;wBACtC,0NAAA,CAAA,aAAU,CAAC,cAAc,CAAC,EAAE;mEAAC,CAAA;gCAC3B,IAAI,OAAO,UAAU,IAAI,UAAU;oCACjC,SAAS,OAAO,KAAK,CAAC,GAAG,CAAC,QAAQ;gCACpC;4BACF;;qBACD;gBACH;gBAEA,UAAU,OAAO,GAAG,IAAI,0NAAA,CAAA,aAAU,CAAC;oBACjC,OAAO;oBACP,QAAQ,aAAa,OAAO;gBAC9B;YACF;YAEA;4CAAO;oBACL,IAAI,UAAU,OAAO,EAAE;wBACrB,UAAU,OAAO,CAAC,OAAO;wBACzB,UAAU,OAAO,GAAG;oBACtB;gBACF;;QACF;mCAAG;QAAC;QAAU;QAAe;QAAM;KAAY;IAE/C,CAAA,GAAA,sQAAA,CAAA,YAAS,AAAD;oCAAE;YACR,IAAI,UAAU,OAAO,IAAI,SAAS;gBAChC,MAAM,iBAAiB,UAAU,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ;gBAE3D,IAAI,WAAW,eAAe,mBAAmB,SAAS;oBACxD,MAAM,cAAc,UAAU,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC;wBACjD,SAAS;4BACP,MAAM;4BACN,IAAI,eAAe,MAAM;4BACzB,QAAQ;wBACV;wBACA,aAAa;4BAAC,2NAAA,CAAA,cAAW,CAAC,MAAM,CAAC,EAAE,CAAC;yBAAM;oBAC5C;oBAEA,UAAU,OAAO,CAAC,QAAQ,CAAC;gBAC7B;YACF;QACF;mCAAG;QAAC;QAAS;KAAO;IAEpB,qBACE,sSAAC;QACC,WAAU;QACV,KAAK;;;;;;AAGX;GApFS;;QAUmB,4PAAA,CAAA,WAAQ;;;KAV3B;AAsFT,SAAS,SAAS,SAAsB,EAAE,SAAsB;IAC9D,IAAI,UAAU,MAAM,KAAK,eAAe,UAAU,MAAM,KAAK,aAC3D,OAAO;IACT,IAAI,UAAU,OAAO,KAAK,UAAU,OAAO,EAAE,OAAO;IACpD,IAAI,UAAU,QAAQ,KAAK,UAAU,QAAQ,EAAE,OAAO;IACtD,IAAI,UAAU,IAAI,KAAK,UAAU,IAAI,EAAE,OAAO;IAC9C,IAAI,UAAU,WAAW,KAAK,UAAU,WAAW,EAAE,OAAO;IAE5D,OAAO;AACT;AAEO,MAAM,2BAAa,CAAA,GAAA,sQAAA,CAAA,OAAI,AAAD,EAAE,gBAAgB", "debugId": null}}, {"offset": {"line": 4266, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/components/code-block.tsx"], "sourcesContent": ["'use client';\r\n\r\n// components/code-block.tsx\r\nimport { usePreview } from '@/lib/hooks/use-preview';\r\nimport { Button } from './ui/button';\r\nimport { CopyIcon, FullscreenIcon } from 'lucide-react';\r\nimport { toast } from 'sonner';\r\nimport { CodeEditor } from './code-editor';\r\nimport { useCopyToClipboard } from 'usehooks-ts';\r\nimport { cn } from '@/lib/utils';\r\nimport { BetterTooltip } from './ui/tooltip';\r\n\r\ninterface CodeBlockProps {\r\n  node: any;\r\n  inline: boolean;\r\n  className: string;\r\n  children: any;\r\n}\r\n\r\nexport function CodeBlock({\r\n  node,\r\n  inline,\r\n  className,\r\n  children,\r\n  ...props\r\n}: CodeBlockProps) {\r\n  const match = /language-(\\w+)/.exec(className || '');\r\n  const [_, copyToClipboard] = useCopyToClipboard();\r\n  const { setPreview } = usePreview();\r\n\r\n  if (inline || !match) {\r\n    return (\r\n      <code className={cn(\"font-mono\", \"rounded-md\", {\r\n        \"bg-zinc-100 dark:bg-zinc-800 py-0.5 px-1.5 text-sm\": inline,\r\n        \"bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-950/30 dark:to-indigo-950/30 text-sm py-1 px-2.5 mx-1\": !match\r\n      })} {...props}>\r\n        {children}\r\n      </code>\r\n    );\r\n  }\r\n\r\n  const language = match[1].toLowerCase();\r\n  const content = String(children);\r\n\r\n  const handlePreviewClick = () => {\r\n    setPreview(prev => ({\r\n      ...prev,\r\n      isVisible: true,\r\n      content,\r\n      kind: language,\r\n      title: 'HTML Preview',\r\n      status: 'idle'\r\n    }));\r\n  };\r\n\r\n  return (\r\n    <div className=\"overflow-hidden rounded-xl border border-zinc-200 dark:border-zinc-700\">\r\n      <div className=\"flex items-center justify-between border-b border-zinc-200 dark:border-zinc-700 bg-zinc-50 dark:bg-zinc-900 px-4 py-2\">\r\n        <span className=\"text-xs font-medium text-zinc-500\">\r\n          {language.toUpperCase()}\r\n        </span>\r\n        <div className=\"flex items-center space-x-2\">\r\n          {language === 'html' && (\r\n            <BetterTooltip content=\"미리보기 (완성 후 클릭하세요)\">\r\n              <Button\r\n                variant=\"ghost\"\r\n                size=\"icon\"\r\n                className=\"h-6 w-6\"\r\n                onClick={handlePreviewClick}\r\n              >\r\n                <FullscreenIcon className=\"h-4 w-4\" />\r\n                <span className=\"sr-only\">미리보기</span>\r\n              </Button>\r\n            </BetterTooltip>\r\n\r\n          )}\r\n          <BetterTooltip content=\"복사하기\">\r\n            <Button\r\n              variant=\"ghost\"\r\n              size=\"icon\"\r\n              className=\"h-6 w-6\"\r\n              onClick={() => {\r\n                copyToClipboard(content);\r\n                toast.success('클립보드에 복사되었습니다.');\r\n              }}\r\n            >\r\n              <CopyIcon className=\"h-4 w-4\" />\r\n              <span className=\"sr-only\">복사하기</span>\r\n            </Button>\r\n          </BetterTooltip>\r\n        </div>\r\n      </div>\r\n\r\n      <div className=\"p-3 bg-background max-h-[350px] overflow-auto styled-scrollbar\">\r\n        <CodeEditor\r\n          content={content}\r\n          language={language}\r\n          mode=\"view\"\r\n          status=\"streaming\"\r\n        />\r\n      </div>\r\n    </div>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AAEA,4BAA4B;AAC5B;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;;;AAVA;;;;;;;;;AAmBO,SAAS,UAAU,EACxB,IAAI,EACJ,MAAM,EACN,SAAS,EACT,QAAQ,EACR,GAAG,OACY;;IACf,MAAM,QAAQ,iBAAiB,IAAI,CAAC,aAAa;IACjD,MAAM,CAAC,GAAG,gBAAgB,GAAG,CAAA,GAAA,mOAAA,CAAA,qBAAkB,AAAD;IAC9C,MAAM,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,aAAU,AAAD;IAEhC,IAAI,UAAU,CAAC,OAAO;QACpB,qBACE,sSAAC;YAAK,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,aAAa,cAAc;gBAC7C,sDAAsD;gBACtD,mHAAmH,CAAC;YACtH;YAAK,GAAG,KAAK;sBACV;;;;;;IAGP;IAEA,MAAM,WAAW,KAAK,CAAC,EAAE,CAAC,WAAW;IACrC,MAAM,UAAU,OAAO;IAEvB,MAAM,qBAAqB;QACzB,WAAW,CAAA,OAAQ,CAAC;gBAClB,GAAG,IAAI;gBACP,WAAW;gBACX;gBACA,MAAM;gBACN,OAAO;gBACP,QAAQ;YACV,CAAC;IACH;IAEA,qBACE,sSAAC;QAAI,WAAU;;0BACb,sSAAC;gBAAI,WAAU;;kCACb,sSAAC;wBAAK,WAAU;kCACb,SAAS,WAAW;;;;;;kCAEvB,sSAAC;wBAAI,WAAU;;4BACZ,aAAa,wBACZ,sSAAC,+HAAA,CAAA,gBAAa;gCAAC,SAAQ;0CACrB,cAAA,sSAAC,8HAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,WAAU;oCACV,SAAS;;sDAET,sSAAC,ySAAA,CAAA,iBAAc;4CAAC,WAAU;;;;;;sDAC1B,sSAAC;4CAAK,WAAU;sDAAU;;;;;;;;;;;;;;;;;0CAKhC,sSAAC,+HAAA,CAAA,gBAAa;gCAAC,SAAQ;0CACrB,cAAA,sSAAC,8HAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,WAAU;oCACV,SAAS;wCACP,gBAAgB;wCAChB,2QAAA,CAAA,QAAK,CAAC,OAAO,CAAC;oCAChB;;sDAEA,sSAAC,6RAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;sDACpB,sSAAC;4CAAK,WAAU;sDAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAMlC,sSAAC;gBAAI,WAAU;0BACb,cAAA,sSAAC,gIAAA,CAAA,aAAU;oBACT,SAAS;oBACT,UAAU;oBACV,MAAK;oBACL,QAAO;;;;;;;;;;;;;;;;;AAKjB;GApFgB;;QAQe,mOAAA,CAAA,qBAAkB;QACxB,iIAAA,CAAA,aAAU;;;KATnB", "debugId": null}}, {"offset": {"line": 4464, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/components/markdown.tsx"], "sourcesContent": ["import Link from 'next/link';\r\nimport React, { memo } from 'react';\r\nimport ReactMarkdown, { type Components } from 'react-markdown';\r\nimport remarkGfm from 'remark-gfm';\r\nimport { CodeBlock } from './code-block';\r\n\r\nconst components: Partial<Components> = {\r\n  code: CodeBlock,\r\n  p: ({ node, children, ...props }) => {\r\n    return (\r\n      <p className=\"px-2\" {...props}>\r\n        {children}\r\n      </p>\r\n    );\r\n  },\r\n  ol: ({ node, children, ...props }) => {\r\n  return (\r\n    <ol\r\n      className=\"space-y-1.5 pl-0 counter-reset-[custom-counter] text-sm leading-relaxed\"\r\n      style={{ counterReset: 'custom-counter' }}\r\n      {...props}\r\n    >\r\n      {children}\r\n    </ol>\r\n  );\r\n},\r\nli: ({ node, children, ...props }) => {\r\n  return (\r\n    <li className=\"relative pl-4  text-gray-800 before:content-['•'] before:absolute before:left-0 before:text-gray-400\" {...props}>\r\n      {children}\r\n    </li>\r\n  );\r\n},\r\n  ul: ({ node, children, ...props }) => {\r\n    return (\r\n      <ul className=\" list-outside ml-4\" {...props}>\r\n        {children}\r\n      </ul>\r\n    );\r\n  },\r\n  strong: ({ node, children, ...props }) => {\r\n    return (\r\n      <span className=\"font-semibold\" {...props}>\r\n        {children}\r\n      </span>\r\n    );\r\n  },\r\n  a: ({ node, children, ...props }) => {\r\n    return (\r\n      <Link\r\n        className=\"text-blue-500 hover:underline\"\r\n        target=\"_blank\"\r\n        rel=\"noreferrer\"\r\n        {...props}\r\n      >\r\n        {children}\r\n      </Link>\r\n    );\r\n  },\r\n  h1: ({ node, children, ...props }) => {\r\n    return (\r\n      <h1 className=\"text-3xl font-semibold mt-6 mb-2\" {...props}>\r\n        {children}\r\n      </h1>\r\n    );\r\n  },\r\n  h2: ({ node, children, ...props }) => {\r\n    return (\r\n      <h2 className=\"text-2xl font-semibold mt-6 mb-2\" {...props}>\r\n        {children}\r\n      </h2>\r\n    );\r\n  },\r\n  h3: ({ node, children, ...props }) => {\r\n    return (\r\n      <h3 className=\"text-xl font-semibold mt-6 mb-2\" {...props}>\r\n        {children}\r\n      </h3>\r\n    );\r\n  },\r\n  h4: ({ node, children, ...props }) => {\r\n    return (\r\n      <h4 className=\"text-lg font-semibold mt-6 mb-2\" {...props}>\r\n        {children}\r\n      </h4>\r\n    );\r\n  },\r\n  h5: ({ node, children, ...props }) => {\r\n    return (\r\n      <h5 className=\"text-base font-semibold mt-6 mb-2\" {...props}>\r\n        {children}\r\n      </h5>\r\n    );\r\n  },\r\n  h6: ({ node, children, ...props }) => {\r\n    return (\r\n      <h6 className=\"text-sm font-semibold mt-6 mb-2\" {...props}>\r\n        {children}\r\n      </h6>\r\n    );\r\n  },\r\n  table: ({ node, children, ...props }) => {\r\n    return (\r\n      <table className=\"min-w-full border-collapse border my-4\" {...props}>\r\n        {children}\r\n      </table>\r\n    );\r\n  },\r\n  thead: ({ node, children, ...props }) => {\r\n    return (\r\n      <thead {...props}>\r\n        {children}\r\n      </thead>\r\n    );\r\n  },\r\n  tr: ({ node, children, ...props }) => {\r\n    return (\r\n      <tr className=\"border-b \" {...props}>\r\n        {children}\r\n      </tr>\r\n    );\r\n  },\r\n  th: ({ node, children, ...props }) => {\r\n    return (\r\n      <th className=\"px-6 py-3 text-left text-sm font-semibold  border-r  last:border-r-0\" {...props}>\r\n        {children}\r\n      </th>\r\n    );\r\n  },\r\n  td: ({ node, children, ...props }) => {\r\n    return (\r\n      <td className=\"px-6 py-4 text-sm border-r last:border-r-0\" {...props}>\r\n        {children}\r\n      </td>\r\n    );\r\n  },\r\n};\r\n\r\nconst remarkPlugins = [remarkGfm];\r\n\r\nconst NonMemoizedMarkdown = ({ children }: { children: string }) => {\r\n  return (\r\n    <ReactMarkdown remarkPlugins={remarkPlugins} components={components}>\r\n      {children}\r\n    </ReactMarkdown>\r\n  );\r\n};\r\n\r\nexport const Markdown = memo(\r\n  NonMemoizedMarkdown,\r\n  (prevProps, nextProps) => prevProps.children === nextProps.children,\r\n);\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;;;;;;;AAEA,MAAM,aAAkC;IACtC,MAAM,+HAAA,CAAA,YAAS;IACf,GAAG,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAG,OAAO;QAC9B,qBACE,sSAAC;YAAE,WAAU;YAAQ,GAAG,KAAK;sBAC1B;;;;;;IAGP;IACA,IAAI,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAG,OAAO;QACjC,qBACE,sSAAC;YACC,WAAU;YACV,OAAO;gBAAE,cAAc;YAAiB;YACvC,GAAG,KAAK;sBAER;;;;;;IAGP;IACA,IAAI,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAG,OAAO;QAC/B,qBACE,sSAAC;YAAG,WAAU;YAAwG,GAAG,KAAK;sBAC3H;;;;;;IAGP;IACE,IAAI,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAG,OAAO;QAC/B,qBACE,sSAAC;YAAG,WAAU;YAAsB,GAAG,KAAK;sBACzC;;;;;;IAGP;IACA,QAAQ,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAG,OAAO;QACnC,qBACE,sSAAC;YAAK,WAAU;YAAiB,GAAG,KAAK;sBACtC;;;;;;IAGP;IACA,GAAG,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAG,OAAO;QAC9B,qBACE,sSAAC,wQAAA,CAAA,UAAI;YACH,WAAU;YACV,QAAO;YACP,KAAI;YACH,GAAG,KAAK;sBAER;;;;;;IAGP;IACA,IAAI,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAG,OAAO;QAC/B,qBACE,sSAAC;YAAG,WAAU;YAAoC,GAAG,KAAK;sBACvD;;;;;;IAGP;IACA,IAAI,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAG,OAAO;QAC/B,qBACE,sSAAC;YAAG,WAAU;YAAoC,GAAG,KAAK;sBACvD;;;;;;IAGP;IACA,IAAI,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAG,OAAO;QAC/B,qBACE,sSAAC;YAAG,WAAU;YAAmC,GAAG,KAAK;sBACtD;;;;;;IAGP;IACA,IAAI,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAG,OAAO;QAC/B,qBACE,sSAAC;YAAG,WAAU;YAAmC,GAAG,KAAK;sBACtD;;;;;;IAGP;IACA,IAAI,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAG,OAAO;QAC/B,qBACE,sSAAC;YAAG,WAAU;YAAqC,GAAG,KAAK;sBACxD;;;;;;IAGP;IACA,IAAI,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAG,OAAO;QAC/B,qBACE,sSAAC;YAAG,WAAU;YAAmC,GAAG,KAAK;sBACtD;;;;;;IAGP;IACA,OAAO,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAG,OAAO;QAClC,qBACE,sSAAC;YAAM,WAAU;YAA0C,GAAG,KAAK;sBAChE;;;;;;IAGP;IACA,OAAO,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAG,OAAO;QAClC,qBACE,sSAAC;YAAO,GAAG,KAAK;sBACb;;;;;;IAGP;IACA,IAAI,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAG,OAAO;QAC/B,qBACE,sSAAC;YAAG,WAAU;YAAa,GAAG,KAAK;sBAChC;;;;;;IAGP;IACA,IAAI,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAG,OAAO;QAC/B,qBACE,sSAAC;YAAG,WAAU;YAAwE,GAAG,KAAK;sBAC3F;;;;;;IAGP;IACA,IAAI,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAG,OAAO;QAC/B,qBACE,sSAAC;YAAG,WAAU;YAA8C,GAAG,KAAK;sBACjE;;;;;;IAGP;AACF;AAEA,MAAM,gBAAgB;IAAC,0MAAA,CAAA,UAAS;CAAC;AAEjC,MAAM,sBAAsB,CAAC,EAAE,QAAQ,EAAwB;IAC7D,qBACE,sSAAC,kTAAA,CAAA,UAAa;QAAC,eAAe;QAAe,YAAY;kBACtD;;;;;;AAGP;KANM;AAQC,MAAM,yBAAW,CAAA,GAAA,sQAAA,CAAA,OAAI,AAAD,EACzB,qBACA,CAAC,WAAW,YAAc,UAAU,QAAQ,KAAK,UAAU,QAAQ", "debugId": null}}, {"offset": {"line": 4702, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/components/message-actions.tsx"], "sourcesContent": ["import type { Message } from 'ai';\r\nimport { toast } from 'sonner';\r\nimport { useSWRConfig } from 'swr';\r\nimport { useCopyToClipboard } from 'usehooks-ts';\r\n\r\n// import type { Vote } from '@/lib/db/schema';\r\nimport { getMessageIdFromAnnotations } from '@/lib/utils';\r\n\r\nimport { memo } from 'react';\r\nimport equal from 'fast-deep-equal';\r\nimport { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';\r\nimport { Button } from '@/components/ui/button';\r\nimport { CopyIcon } from 'lucide-react';\r\nimport { ThumbDownIcon, ThumbUpIcon } from '@/components/ui/icons';\r\nimport { Vote } from '@/lib/db/schema';\r\n\r\nexport function PureMessageActions({\r\n  chatId,\r\n  message,\r\n  vote,\r\n  isLoading,\r\n}: {\r\n  chatId: string;\r\n  message: Message;\r\n  vote: Vote | undefined;\r\n  isLoading: boolean;\r\n}) {\r\n  const { mutate } = useSWRConfig();\r\n  const [_, copyToClipboard] = useCopyToClipboard();\r\n\r\n  if (isLoading) return null;\r\n  if (message.role === 'user') return null;\r\n  // if (message.toolInvocations && message.toolInvocations.length > 0)\r\n    // return null;\r\n\r\n  return (\r\n    <TooltipProvider delayDuration={0}>\r\n      <div className=\"flex ml-2 flex-row gap-2\">\r\n        <Tooltip>\r\n          <TooltipTrigger asChild>\r\n            <Button\r\n              className=\"py-1 px-2 h-fit text-muted-foreground\"\r\n              variant=\"outline\"\r\n              onClick={async () => {\r\n                await copyToClipboard(message.content as string);\r\n                toast.success('클립보드에 복사되었습니다!');\r\n              }}\r\n            >\r\n              <CopyIcon />\r\n            </Button>\r\n          </TooltipTrigger>\r\n          <TooltipContent>복사하기</TooltipContent>\r\n        </Tooltip>\r\n\r\n        {/* <Tooltip>\r\n          <TooltipTrigger asChild>\r\n            <Button\r\n              className=\"py-1 px-2 h-fit text-muted-foreground !pointer-events-auto\"\r\n              // disabled={vote?.isUpvoted}\r\n              variant=\"outline\"\r\n              onClick={async () => {\r\n                const messageId = getMessageIdFromAnnotations(message);\r\n\r\n                const upvote = fetch('/api/vote', {\r\n                  method: 'POST',\r\n                  body: JSON.stringify({\r\n                    chatId,\r\n                    messageId,\r\n                    type: 'up',\r\n                  }),\r\n                });\r\n\r\n                toast.promise(upvote, {\r\n                  loading: '응답을 추천하는 중...',\r\n                  success: () => {\r\n                    mutate<Array<Vote>>(\r\n                      `/api/vote?chatId=${chatId}`,\r\n                      (currentVotes) => {\r\n                        if (!currentVotes) return [];\r\n\r\n                        const votesWithoutCurrent = currentVotes.filter(\r\n                          (vote) => vote.messageId !== message.id,\r\n                        );\r\n\r\n                        return [\r\n                          ...votesWithoutCurrent,\r\n                          {\r\n                            chatId,\r\n                            messageId: message.id,\r\n                            isUpvoted: true,\r\n                          },\r\n                        ];\r\n                      },\r\n                      { revalidate: false },\r\n                    );\r\n\r\n                    return '응답을 추천했습니다!';\r\n                  },\r\n                  error: '응답 추천에 실패했습니다.',\r\n                });\r\n              }}\r\n            >\r\n              <ThumbUpIcon />\r\n            </Button>\r\n          </TooltipTrigger>\r\n          <TooltipContent>좋아요</TooltipContent>\r\n        </Tooltip>\r\n\r\n        <Tooltip>\r\n          <TooltipTrigger asChild>\r\n            <Button\r\n              className=\"py-1 px-2 h-fit text-muted-foreground !pointer-events-auto\"\r\n              variant=\"outline\"\r\n              // disabled={vote && !vote.isUpvoted}\r\n              onClick={async () => {\r\n                const messageId = getMessageIdFromAnnotations(message);\r\n\r\n                const downvote = fetch('/api/vote', {\r\n                  method: 'PATCH',\r\n                  body: JSON.stringify({\r\n                    chatId,\r\n                    messageId,\r\n                    type: 'down',\r\n                  }),\r\n                });\r\n\r\n                toast.promise(downvote, {\r\n                  loading: '응답을 비추천하는 중...',\r\n                  success: () => {\r\n                    mutate<Array<Vote>>(\r\n                      `/api/vote?chatId=${chatId}`,\r\n                      (currentVotes) => {\r\n                        if (!currentVotes) return [];\r\n\r\n                        const votesWithoutCurrent = currentVotes.filter(\r\n                          (vote) => vote.messageId !== message.id,\r\n                        );\r\n\r\n                        return [\r\n                          ...votesWithoutCurrent,\r\n                          {\r\n                            chatId,\r\n                            messageId: message.id,\r\n                            isUpvoted: false,\r\n                          },\r\n                        ];\r\n                      },\r\n                      { revalidate: false },\r\n                    );\r\n\r\n                    return '응답을 비추천했습니다!';\r\n                  },\r\n                  error: '응답 비추천에 실패했습니다.',\r\n                });\r\n              }}\r\n            >\r\n              <ThumbDownIcon />\r\n            </Button>\r\n          </TooltipTrigger>\r\n          <TooltipContent>별로에요</TooltipContent>\r\n        </Tooltip> */}\r\n      </div>\r\n    </TooltipProvider>\r\n  );\r\n}\r\n\r\nexport const MessageActions = memo(\r\n  PureMessageActions,\r\n  (prevProps, nextProps) => {\r\n    // if (!equal(prevProps.vote, nextProps.vote)) return false;\r\n    if (prevProps.isLoading !== nextProps.isLoading) return false;\r\n\r\n    return true;\r\n  },\r\n);\r\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAAA;AACA;AAKA;AAEA;AACA;AACA;;;;;;;;;;AAIO,SAAS,mBAAmB,EACjC,MAAM,EACN,OAAO,EACP,IAAI,EACJ,SAAS,EAMV;;IACC,MAAM,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,2NAAA,CAAA,eAAY,AAAD;IAC9B,MAAM,CAAC,GAAG,gBAAgB,GAAG,CAAA,GAAA,mOAAA,CAAA,qBAAkB,AAAD;IAE9C,IAAI,WAAW,OAAO;IACtB,IAAI,QAAQ,IAAI,KAAK,QAAQ,OAAO;IACpC,qEAAqE;IACnE,eAAe;IAEjB,qBACE,sSAAC,+HAAA,CAAA,kBAAe;QAAC,eAAe;kBAC9B,cAAA,sSAAC;YAAI,WAAU;sBACb,cAAA,sSAAC,+HAAA,CAAA,UAAO;;kCACN,sSAAC,+HAAA,CAAA,iBAAc;wBAAC,OAAO;kCACrB,cAAA,sSAAC,8HAAA,CAAA,SAAM;4BACL,WAAU;4BACV,SAAQ;4BACR,SAAS;gCACP,MAAM,gBAAgB,QAAQ,OAAO;gCACrC,2QAAA,CAAA,QAAK,CAAC,OAAO,CAAC;4BAChB;sCAEA,cAAA,sSAAC,6RAAA,CAAA,WAAQ;;;;;;;;;;;;;;;kCAGb,sSAAC,+HAAA,CAAA,iBAAc;kCAAC;;;;;;;;;;;;;;;;;;;;;;AAiH1B;GApJgB;;QAWK,2NAAA,CAAA,eAAY;QACF,mOAAA,CAAA,qBAAkB;;;KAZjC;AAsJT,MAAM,+BAAiB,CAAA,GAAA,sQAAA,CAAA,OAAI,AAAD,EAC/B,oBACA,CAAC,WAAW;IACV,4DAA4D;IAC5D,IAAI,UAAU,SAAS,KAAK,UAAU,SAAS,EAAE,OAAO;IAExD,OAAO;AACT", "debugId": null}}, {"offset": {"line": 4811, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/lib/hooks/use-user-message-id.ts"], "sourcesContent": ["'use client';\r\n\r\nimport useSWR from 'swr';\r\n\r\nexport function useUserMessageId() {\r\n  const { data: userMessageIdFromServer, mutate: setUserMessageIdFromServer } =\r\n    useSWR('userMessageIdFromServer', null);\r\n\r\n  return { userMessageIdFromServer, setUserMessageIdFromServer };\r\n}\r\n"], "names": [], "mappings": ";;;AAEA;AAAA;;AAFA;;AAIO,SAAS;;IACd,MAAM,EAAE,MAAM,uBAAuB,EAAE,QAAQ,0BAA0B,EAAE,GACzE,CAAA,GAAA,sOAAA,CAAA,UAAM,AAAD,EAAE,2BAA2B;IAEpC,OAAO;QAAE;QAAyB;IAA2B;AAC/D;GALgB;;QAEZ,sOAAA,CAAA,UAAM", "debugId": null}}, {"offset": {"line": 4841, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/app/%28map%29/actions.ts"], "sourcesContent": ["'use server';\r\n\r\nimport { geon } from '@/lib/ai';\r\nimport { openai } from '@ai-sdk/openai';\r\nimport { VisibilityType } from '@/components/visibility-selector';\r\nimport { deleteMessagesByChatIdAfterTimestamp, getMessageById, updateChatVisiblityById } from '@/lib/db/queries';\r\nimport { CoreUserMessage, generateText } from 'ai';\r\nimport { cookies } from 'next/headers';\r\n\r\nexport async function saveModelId(model: string) {\r\n  const cookieStore = await cookies();\r\n  cookieStore.set('model-id', model);\r\n}\r\nexport async function saveDevModelId(model: string) {\r\n  const cookieStore = await cookies();\r\n  cookieStore.set('dev-model-id', model);\r\n}\r\n\r\nexport async function generateTitleFromUserMessage({\r\n  message,\r\n}: {\r\n  message: CoreUserMessage;\r\n}) {\r\n  const { text: title } = await generateText({\r\n    model: openai('gpt-4o-mini'),\r\n    system: `\\n\r\n    - you will generate a short title based on the first message a user begins a conversation with\r\n    - ensure it is not more than 80 characters long\r\n    - the title should be a summary of the user's message\r\n    - do not use quotes or colons`,\r\n    prompt: JSON.stringify(message),\r\n  });\r\n\r\n  return title;\r\n}\r\n\r\nexport async function deleteTrailingMessages({ id }: { id: string }) {\r\n  const [message] = await getMessageById({ id });\r\n\r\n  await deleteMessagesByChatIdAfterTimestamp({\r\n    chatId: message.chatId,\r\n    timestamp: message.createdAt,\r\n  });\r\n}\r\n\r\nexport async function updateChatVisibility({\r\n  chatId,\r\n  visibility,\r\n}: {\r\n  chatId: string;\r\n  visibility: VisibilityType;\r\n}) {\r\n  await updateChatVisiblityById({ chatId, visibility });\r\n}\r\n"], "names": [], "mappings": ";;;;;;IAoCsB,yBAAA,WAAA,GAAA,CAAA,GAAA,kUAAA,CAAA,wBAAA,EAAA,8CAAA,kUAAA,CAAA,aAAA,EAAA,KAAA,GAAA,kUAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 4857, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/components/message-editor.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { ChatRequestOptions, Message } from 'ai';\r\nimport { Dispatch, SetStateAction, useEffect, useRef, useState } from 'react';\r\nimport { toast } from 'sonner';\r\nimport { Button } from '@/components/ui/button';\r\nimport { Textarea } from '@/components/ui/textarea';\r\nimport { useUserMessageId } from '@/lib/hooks/use-user-message-id';\r\nimport { deleteTrailingMessages } from '@/app/(map)/actions';\r\n\r\nexport type MessageEditorProps = {\r\n  message: Message;\r\n  setMode: Dispatch<SetStateAction<'view' | 'edit'>>;\r\n  setMessages: (\r\n    messages: Message[] | ((messages: Message[]) => Message[]),\r\n  ) => void;\r\n  reload: (\r\n    chatRequestOptions?: ChatRequestOptions,\r\n  ) => Promise<string | null | undefined>;\r\n};\r\n\r\nexport function MessageEditor({\r\n  message,\r\n  setMode,\r\n  setMessages,\r\n  reload,\r\n}: MessageEditorProps) {\r\n  const { userMessageIdFromServer } = useUserMessageId();\r\n  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);\r\n\r\n  const [draftContent, setDraftContent] = useState<string>(message.content);\r\n  const textareaRef = useRef<HTMLTextAreaElement>(null);\r\n\r\n  useEffect(() => {\r\n    if (textareaRef.current) {\r\n      adjustHeight();\r\n    }\r\n  }, []);\r\n\r\n  const adjustHeight = () => {\r\n    if (textareaRef.current) {\r\n      textareaRef.current.style.height = 'auto';\r\n      textareaRef.current.style.height = `${textareaRef.current.scrollHeight + 2}px`;\r\n    }\r\n  };\r\n\r\n  const handleInput = (event: React.ChangeEvent<HTMLTextAreaElement>) => {\r\n    setDraftContent(event.target.value);\r\n    adjustHeight();\r\n  };\r\n\r\n  return (\r\n    <div className=\"flex flex-col gap-2 w-full\">\r\n      <Textarea\r\n        ref={textareaRef}\r\n        className=\"bg-transparent outline-none overflow-hidden resize-none !text-base rounded-xl w-full\"\r\n        value={draftContent}\r\n        onChange={handleInput}\r\n      />\r\n\r\n      <div className=\"flex flex-row gap-2 justify-end\">\r\n        <Button\r\n          variant=\"outline\"\r\n          className=\"h-fit py-2 px-3\"\r\n          onClick={() => {\r\n            setMode('view');\r\n          }}\r\n        >\r\n          취소\r\n        </Button>\r\n        <Button\r\n          variant=\"default\"\r\n          className=\"h-fit py-2 px-3\"\r\n          disabled={isSubmitting}\r\n          onClick={async () => {\r\n            setIsSubmitting(true);\r\n            const messageId = userMessageIdFromServer ?? message.id;\r\n\r\n            if (!messageId) {\r\n              toast.error('Something went wrong, please try again!');\r\n              setIsSubmitting(false);\r\n              return;\r\n            }\r\n\r\n            await deleteTrailingMessages({\r\n              id: messageId,\r\n            });\r\n\r\n            setMessages((messages) => {\r\n              const index = messages.findIndex((m) => m.id === message.id);\r\n\r\n              if (index !== -1) {\r\n                const updatedMessage = {\r\n                  ...message,\r\n                  content: draftContent,\r\n                };\r\n\r\n                return [...messages.slice(0, index), updatedMessage];\r\n              }\r\n\r\n              return messages;\r\n            });\r\n\r\n            setMode('view');\r\n            reload();\r\n          }}\r\n        >\r\n          {isSubmitting ? '전송중...' : '전송'}\r\n        </Button>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AACA;AACA;;;AARA;;;;;;;AAqBO,SAAS,cAAc,EAC5B,OAAO,EACP,OAAO,EACP,WAAW,EACX,MAAM,EACa;;IACnB,MAAM,EAAE,uBAAuB,EAAE,GAAG,CAAA,GAAA,+IAAA,CAAA,mBAAgB,AAAD;IACnD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAW;IAE1D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAU,QAAQ,OAAO;IACxE,MAAM,cAAc,CAAA,GAAA,sQAAA,CAAA,SAAM,AAAD,EAAuB;IAEhD,CAAA,GAAA,sQAAA,CAAA,YAAS,AAAD;mCAAE;YACR,IAAI,YAAY,OAAO,EAAE;gBACvB;YACF;QACF;kCAAG,EAAE;IAEL,MAAM,eAAe;QACnB,IAAI,YAAY,OAAO,EAAE;YACvB,YAAY,OAAO,CAAC,KAAK,CAAC,MAAM,GAAG;YACnC,YAAY,OAAO,CAAC,KAAK,CAAC,MAAM,GAAG,GAAG,YAAY,OAAO,CAAC,YAAY,GAAG,EAAE,EAAE,CAAC;QAChF;IACF;IAEA,MAAM,cAAc,CAAC;QACnB,gBAAgB,MAAM,MAAM,CAAC,KAAK;QAClC;IACF;IAEA,qBACE,sSAAC;QAAI,WAAU;;0BACb,sSAAC,gIAAA,CAAA,WAAQ;gBACP,KAAK;gBACL,WAAU;gBACV,OAAO;gBACP,UAAU;;;;;;0BAGZ,sSAAC;gBAAI,WAAU;;kCACb,sSAAC,8HAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,WAAU;wBACV,SAAS;4BACP,QAAQ;wBACV;kCACD;;;;;;kCAGD,sSAAC,8HAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,WAAU;wBACV,UAAU;wBACV,SAAS;4BACP,gBAAgB;4BAChB,MAAM,YAAY,2BAA2B,QAAQ,EAAE;4BAEvD,IAAI,CAAC,WAAW;gCACd,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gCACZ,gBAAgB;gCAChB;4BACF;4BAEA,MAAM,CAAA,GAAA,yJAAA,CAAA,yBAAsB,AAAD,EAAE;gCAC3B,IAAI;4BACN;4BAEA,YAAY,CAAC;gCACX,MAAM,QAAQ,SAAS,SAAS,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK,QAAQ,EAAE;gCAE3D,IAAI,UAAU,CAAC,GAAG;oCAChB,MAAM,iBAAiB;wCACrB,GAAG,OAAO;wCACV,SAAS;oCACX;oCAEA,OAAO;2CAAI,SAAS,KAAK,CAAC,GAAG;wCAAQ;qCAAe;gCACtD;gCAEA,OAAO;4BACT;4BAEA,QAAQ;4BACR;wBACF;kCAEC,eAAe,WAAW;;;;;;;;;;;;;;;;;;AAKrC;GA3FgB;;QAMsB,+IAAA,CAAA,mBAAgB;;;KANtC", "debugId": null}}, {"offset": {"line": 4995, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/components/ui/alert-dialog.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as AlertDialogPrimitive from \"@radix-ui/react-alert-dialog\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\nimport { buttonVariants } from \"@/components/ui/button\"\r\n\r\nconst AlertDialog = AlertDialogPrimitive.Root\r\n\r\nconst AlertDialogTrigger = AlertDialogPrimitive.Trigger\r\n\r\nconst AlertDialogPortal = AlertDialogPrimitive.Portal\r\n\r\nconst AlertDialogOverlay = React.forwardRef<\r\n  React.ElementRef<typeof AlertDialogPrimitive.Overlay>,\r\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Overlay>\r\n>(({ className, ...props }, ref) => (\r\n  <AlertDialogPrimitive.Overlay\r\n    className={cn(\r\n      \"fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0\",\r\n      className\r\n    )}\r\n    {...props}\r\n    ref={ref}\r\n  />\r\n))\r\nAlertDialogOverlay.displayName = AlertDialogPrimitive.Overlay.displayName\r\n\r\nconst AlertDialogContent = React.forwardRef<\r\n  React.ElementRef<typeof AlertDialogPrimitive.Content>,\r\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Content>\r\n>(({ className, ...props }, ref) => (\r\n  <AlertDialogPortal>\r\n    <AlertDialogOverlay />\r\n    <AlertDialogPrimitive.Content\r\n      ref={ref}\r\n      className={cn(\r\n        \"fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  </AlertDialogPortal>\r\n))\r\nAlertDialogContent.displayName = AlertDialogPrimitive.Content.displayName\r\n\r\nconst AlertDialogHeader = ({\r\n  className,\r\n  ...props\r\n}: React.HTMLAttributes<HTMLDivElement>) => (\r\n  <div\r\n    className={cn(\r\n      \"flex flex-col space-y-2 text-center sm:text-left\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n)\r\nAlertDialogHeader.displayName = \"AlertDialogHeader\"\r\n\r\nconst AlertDialogFooter = ({\r\n  className,\r\n  ...props\r\n}: React.HTMLAttributes<HTMLDivElement>) => (\r\n  <div\r\n    className={cn(\r\n      \"flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n)\r\nAlertDialogFooter.displayName = \"AlertDialogFooter\"\r\n\r\nconst AlertDialogTitle = React.forwardRef<\r\n  React.ElementRef<typeof AlertDialogPrimitive.Title>,\r\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Title>\r\n>(({ className, ...props }, ref) => (\r\n  <AlertDialogPrimitive.Title\r\n    ref={ref}\r\n    className={cn(\"text-lg font-semibold\", className)}\r\n    {...props}\r\n  />\r\n))\r\nAlertDialogTitle.displayName = AlertDialogPrimitive.Title.displayName\r\n\r\nconst AlertDialogDescription = React.forwardRef<\r\n  React.ElementRef<typeof AlertDialogPrimitive.Description>,\r\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Description>\r\n>(({ className, ...props }, ref) => (\r\n  <AlertDialogPrimitive.Description\r\n    ref={ref}\r\n    className={cn(\"text-sm text-muted-foreground\", className)}\r\n    {...props}\r\n  />\r\n))\r\nAlertDialogDescription.displayName =\r\n  AlertDialogPrimitive.Description.displayName\r\n\r\nconst AlertDialogAction = React.forwardRef<\r\n  React.ElementRef<typeof AlertDialogPrimitive.Action>,\r\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Action>\r\n>(({ className, ...props }, ref) => (\r\n  <AlertDialogPrimitive.Action\r\n    ref={ref}\r\n    className={cn(buttonVariants(), className)}\r\n    {...props}\r\n  />\r\n))\r\nAlertDialogAction.displayName = AlertDialogPrimitive.Action.displayName\r\n\r\nconst AlertDialogCancel = React.forwardRef<\r\n  React.ElementRef<typeof AlertDialogPrimitive.Cancel>,\r\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Cancel>\r\n>(({ className, ...props }, ref) => (\r\n  <AlertDialogPrimitive.Cancel\r\n    ref={ref}\r\n    className={cn(\r\n      buttonVariants({ variant: \"outline\" }),\r\n      \"mt-2 sm:mt-0\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nAlertDialogCancel.displayName = AlertDialogPrimitive.Cancel.displayName\r\n\r\nexport {\r\n  AlertDialog,\r\n  AlertDialogPortal,\r\n  AlertDialogOverlay,\r\n  AlertDialogTrigger,\r\n  AlertDialogContent,\r\n  AlertDialogHeader,\r\n  AlertDialogFooter,\r\n  AlertDialogTitle,\r\n  AlertDialogDescription,\r\n  AlertDialogAction,\r\n  AlertDialogCancel,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAEA;AACA;AAEA;AACA;AANA;;;;;;AAQA,MAAM,cAAc,qRAAA,CAAA,OAAyB;AAE7C,MAAM,qBAAqB,qRAAA,CAAA,UAA4B;AAEvD,MAAM,oBAAoB,qRAAA,CAAA,SAA2B;AAErD,MAAM,mCAAqB,CAAA,GAAA,sQAAA,CAAA,aAAgB,AAAD,EAGxC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,sSAAC,qRAAA,CAAA,UAA4B;QAC3B,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,2JACA;QAED,GAAG,KAAK;QACT,KAAK;;;;;;KAVH;AAaN,mBAAmB,WAAW,GAAG,qRAAA,CAAA,UAA4B,CAAC,WAAW;AAEzE,MAAM,mCAAqB,CAAA,GAAA,sQAAA,CAAA,aAAgB,AAAD,QAGxC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,sSAAC;;0BACC,sSAAC;;;;;0BACD,sSAAC,qRAAA,CAAA,UAA4B;gBAC3B,KAAK;gBACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,+fACA;gBAED,GAAG,KAAK;;;;;;;;;;;;;AAIf,mBAAmB,WAAW,GAAG,qRAAA,CAAA,UAA4B,CAAC,WAAW;AAEzE,MAAM,oBAAoB,CAAC,EACzB,SAAS,EACT,GAAG,OACkC,iBACrC,sSAAC;QACC,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,oDACA;QAED,GAAG,KAAK;;;;;;MATP;AAYN,kBAAkB,WAAW,GAAG;AAEhC,MAAM,oBAAoB,CAAC,EACzB,SAAS,EACT,GAAG,OACkC,iBACrC,sSAAC;QACC,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;;;;;;MATP;AAYN,kBAAkB,WAAW,GAAG;AAEhC,MAAM,iCAAmB,CAAA,GAAA,sQAAA,CAAA,aAAgB,AAAD,QAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,sSAAC,qRAAA,CAAA,QAA0B;QACzB,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,yBAAyB;QACtC,GAAG,KAAK;;;;;;;AAGb,iBAAiB,WAAW,GAAG,qRAAA,CAAA,QAA0B,CAAC,WAAW;AAErE,MAAM,uCAAyB,CAAA,GAAA,sQAAA,CAAA,aAAgB,AAAD,QAG5C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,sSAAC,qRAAA,CAAA,cAAgC;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,uBAAuB,WAAW,GAChC,qRAAA,CAAA,cAAgC,CAAC,WAAW;AAE9C,MAAM,kCAAoB,CAAA,GAAA,sQAAA,CAAA,aAAgB,AAAD,QAGvC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,sSAAC,qRAAA,CAAA,SAA2B;QAC1B,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,CAAA,GAAA,8HAAA,CAAA,iBAAc,AAAD,KAAK;QAC/B,GAAG,KAAK;;;;;;;AAGb,kBAAkB,WAAW,GAAG,qRAAA,CAAA,SAA2B,CAAC,WAAW;AAEvE,MAAM,kCAAoB,CAAA,GAAA,sQAAA,CAAA,aAAgB,AAAD,SAGvC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,sSAAC,qRAAA,CAAA,SAA2B;QAC1B,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,CAAA,GAAA,8HAAA,CAAA,iBAAc,AAAD,EAAE;YAAE,SAAS;QAAU,IACpC,gBACA;QAED,GAAG,KAAK;;;;;;;AAGb,kBAAkB,WAAW,GAAG,qRAAA,CAAA,SAA2B,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 5147, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/components/preview/source-citation.tsx"], "sourcesContent": ["import React, { useState } from 'react';\r\nimport { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>og<PERSON><PERSON><PERSON>, AlertDialogTitle, AlertDialogAction } from '@/components/ui/alert-dialog';\r\nimport { Info, X } from 'lucide-react';\r\nimport { Markdown } from '../markdown';\r\n\r\ntype FileMeta = {\r\n  name: string;\r\n  content_type: string;\r\n  size: number;\r\n  collection_name: string;\r\n}\r\n\r\ntype File = {\r\n  id: string;\r\n  meta: FileMeta;\r\n  created_at: number;\r\n  updated_at: number;\r\n}\r\n\r\ntype User = {\r\n  id: string;\r\n  name: string;\r\n  email: string;\r\n  role: string;\r\n  profile_image_url: string;\r\n}\r\n\r\ntype Source = {\r\n  id: string;\r\n  user_id: string;\r\n  name: string;\r\n  description: string;\r\n  data?: any;\r\n  meta: any | null;\r\n  access_control: any | null;\r\n  created_at: number;\r\n  updated_at: number;\r\n  user: User;\r\n  files: File[];\r\n  type: string;\r\n}\r\n\r\ntype Metadata = {\r\n  created_by: string;\r\n  embedding_config: string;\r\n  file_id: string;\r\n  hash: string;\r\n  name: string;\r\n  source: string;\r\n  start_index: number;\r\n  score: number;\r\n}\r\n\r\ntype SourceItem = {\r\n  source: Source;\r\n  document: string[];\r\n  metadata: <PERSON><PERSON><PERSON>[];\r\n  distances: number[];\r\n}\r\n\r\ninterface SourceCitationProps {\r\n  sources: SourceItem[];\r\n}\r\n\r\nconst SourceCitation: React.FC<SourceCitationProps> = ({ sources }) => {\r\n  const [isDialogOpen, setIsDialogOpen] = useState(false);\r\n  const [selectedSource, setSelectedSource] = useState<{name: string, contents: {content: string, relevance: number}[]} | null>(null);\r\n\r\n  const handleSourceClick = (fileName: string) => {\r\n    const contents = sources.flatMap((sourceItem) => {\r\n      return sourceItem.metadata\r\n        .map((meta, index) => ({\r\n          content: sourceItem.document[index],\r\n          relevance: sourceItem.distances[index] * 100,\r\n          fileName: meta.name\r\n        }))\r\n        .filter(item => item.fileName === fileName);\r\n    });\r\n\r\n    setSelectedSource({\r\n      name: fileName,\r\n      contents: contents\r\n    });\r\n    setIsDialogOpen(true);\r\n  };\r\n\r\n  const getUniqueFiles = () => {\r\n    const filesMap = new Map<string, { name: string, relevance: number, count: number }>();\r\n    \r\n    sources.forEach(({ metadata, distances }) => {\r\n      metadata.forEach((meta, index) => {\r\n        const fileName = meta.name;\r\n        const currentRelevance = distances[index] * 100;\r\n        \r\n        if (!filesMap.has(fileName)) {\r\n          filesMap.set(fileName, {\r\n            name: fileName,\r\n            relevance: currentRelevance,\r\n            count: 1\r\n          });\r\n        } else {\r\n          const current = filesMap.get(fileName)!;\r\n          filesMap.set(fileName, {\r\n            name: fileName,\r\n            relevance: current.relevance + currentRelevance,\r\n            count: current.count + 1\r\n          });\r\n        }\r\n      });\r\n    });\r\n    \r\n    return Array.from(filesMap.entries()).map(([_, value]) => ({\r\n      name: value.name,\r\n      avgRelevance: value.relevance / value.count\r\n    }));\r\n  };\r\n\r\n  const parseContent = (content: string) => {\r\n    try {\r\n      // metadata 부분 제거\r\n      const [mainContent] = content.split(\" metadata=\");\r\n      // page_content= 와 따옴표 제거\r\n      return mainContent\r\n        .replace(/^page_content=/, '')\r\n        .replace(/^'|'$/g, '');\r\n    } catch (e) {\r\n      return content;\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"w-full max-w-2xl\">\r\n      <div className=\"flex flex-col gap-2\">\r\n        <div className=\"text-sm flex items-center gap-1\">\r\n          <Info size={16} />\r\n          <span>참고 문서</span>\r\n        </div>\r\n        <div className=\"flex flex-wrap gap-2\">\r\n          {getUniqueFiles().map((file) => (\r\n            <button\r\n              key={file.name}\r\n              onClick={() => handleSourceClick(file.name)}\r\n              className=\"inline-flex items-center gap-2 px-3 py-1 text-sm rounded-full border hover:opacity-80 transition-opacity\"\r\n            >\r\n              <span>{file.name}</span>\r\n              <span className=\"text-xs opacity-70\">\r\n                {file.avgRelevance.toFixed(1)}%\r\n              </span>\r\n            </button>\r\n          ))}\r\n        </div>\r\n      </div>\r\n\r\n      <AlertDialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>\r\n        <AlertDialogContent>\r\n          <AlertDialogHeader className=\"relative\">\r\n            <AlertDialogTitle className=\"pr-8\">{selectedSource?.name}</AlertDialogTitle>\r\n            <button\r\n              onClick={() => setIsDialogOpen(false)}\r\n              className=\"absolute right-0 top-0 p-1 rounded-full hover:opacity-70 transition-opacity\"\r\n            >\r\n              <X size={18} />\r\n            </button>\r\n          </AlertDialogHeader>\r\n          <div className=\"max-h-96 overflow-y-auto\">\r\n            {selectedSource?.contents.map((item, index) => (\r\n              <div key={index} className=\"mt-4\">\r\n                <div className=\"flex items-center gap-2 mb-1\">\r\n                  <div className=\"h-1 flex-grow rounded-full overflow-hidden border\">\r\n                    <div \r\n                      className=\"h-full transition-all\"\r\n                      style={{ \r\n                        width: `${item.relevance}%`,\r\n                        backgroundColor: `hsl(${item.relevance * 1.2}, 70%, 50%)`\r\n                      }} \r\n                    />\r\n                  </div>\r\n                  <span className=\"text-xs opacity-70\">{item.relevance.toFixed(1)}%</span>\r\n                </div>\r\n                <div className=\"border-l-2 pl-4 py-2\">\r\n                  <Markdown>{item.content as string}</Markdown>\r\n                </div>\r\n              </div>\r\n            ))}\r\n          </div>\r\n          <AlertDialogAction onClick={() => setIsDialogOpen(false)} className=\"mt-4\">\r\n            닫기\r\n          </AlertDialogAction>\r\n        </AlertDialogContent>\r\n      </AlertDialog>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default SourceCitation;"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AAAA;AACA;;;;;;;AA6DA,MAAM,iBAAgD,CAAC,EAAE,OAAO,EAAE;;IAChE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAA2E;IAE9H,MAAM,oBAAoB,CAAC;QACzB,MAAM,WAAW,QAAQ,OAAO,CAAC,CAAC;YAChC,OAAO,WAAW,QAAQ,CACvB,GAAG,CAAC,CAAC,MAAM,QAAU,CAAC;oBACrB,SAAS,WAAW,QAAQ,CAAC,MAAM;oBACnC,WAAW,WAAW,SAAS,CAAC,MAAM,GAAG;oBACzC,UAAU,KAAK,IAAI;gBACrB,CAAC,GACA,MAAM,CAAC,CAAA,OAAQ,KAAK,QAAQ,KAAK;QACtC;QAEA,kBAAkB;YAChB,MAAM;YACN,UAAU;QACZ;QACA,gBAAgB;IAClB;IAEA,MAAM,iBAAiB;QACrB,MAAM,WAAW,IAAI;QAErB,QAAQ,OAAO,CAAC,CAAC,EAAE,QAAQ,EAAE,SAAS,EAAE;YACtC,SAAS,OAAO,CAAC,CAAC,MAAM;gBACtB,MAAM,WAAW,KAAK,IAAI;gBAC1B,MAAM,mBAAmB,SAAS,CAAC,MAAM,GAAG;gBAE5C,IAAI,CAAC,SAAS,GAAG,CAAC,WAAW;oBAC3B,SAAS,GAAG,CAAC,UAAU;wBACrB,MAAM;wBACN,WAAW;wBACX,OAAO;oBACT;gBACF,OAAO;oBACL,MAAM,UAAU,SAAS,GAAG,CAAC;oBAC7B,SAAS,GAAG,CAAC,UAAU;wBACrB,MAAM;wBACN,WAAW,QAAQ,SAAS,GAAG;wBAC/B,OAAO,QAAQ,KAAK,GAAG;oBACzB;gBACF;YACF;QACF;QAEA,OAAO,MAAM,IAAI,CAAC,SAAS,OAAO,IAAI,GAAG,CAAC,CAAC,CAAC,GAAG,MAAM,GAAK,CAAC;gBACzD,MAAM,MAAM,IAAI;gBAChB,cAAc,MAAM,SAAS,GAAG,MAAM,KAAK;YAC7C,CAAC;IACH;IAEA,MAAM,eAAe,CAAC;QACpB,IAAI;YACF,iBAAiB;YACjB,MAAM,CAAC,YAAY,GAAG,QAAQ,KAAK,CAAC;YACpC,yBAAyB;YACzB,OAAO,YACJ,OAAO,CAAC,kBAAkB,IAC1B,OAAO,CAAC,UAAU;QACvB,EAAE,OAAO,GAAG;YACV,OAAO;QACT;IACF;IAEA,qBACE,sSAAC;QAAI,WAAU;;0BACb,sSAAC;gBAAI,WAAU;;kCACb,sSAAC;wBAAI,WAAU;;0CACb,sSAAC,yRAAA,CAAA,OAAI;gCAAC,MAAM;;;;;;0CACZ,sSAAC;0CAAK;;;;;;;;;;;;kCAER,sSAAC;wBAAI,WAAU;kCACZ,iBAAiB,GAAG,CAAC,CAAC,qBACrB,sSAAC;gCAEC,SAAS,IAAM,kBAAkB,KAAK,IAAI;gCAC1C,WAAU;;kDAEV,sSAAC;kDAAM,KAAK,IAAI;;;;;;kDAChB,sSAAC;wCAAK,WAAU;;4CACb,KAAK,YAAY,CAAC,OAAO,CAAC;4CAAG;;;;;;;;+BAN3B,KAAK,IAAI;;;;;;;;;;;;;;;;0BAatB,sSAAC,uIAAA,CAAA,cAAW;gBAAC,MAAM;gBAAc,cAAc;0BAC7C,cAAA,sSAAC,uIAAA,CAAA,qBAAkB;;sCACjB,sSAAC,uIAAA,CAAA,oBAAiB;4BAAC,WAAU;;8CAC3B,sSAAC,uIAAA,CAAA,mBAAgB;oCAAC,WAAU;8CAAQ,gBAAgB;;;;;;8CACpD,sSAAC;oCACC,SAAS,IAAM,gBAAgB;oCAC/B,WAAU;8CAEV,cAAA,sSAAC,mRAAA,CAAA,IAAC;wCAAC,MAAM;;;;;;;;;;;;;;;;;sCAGb,sSAAC;4BAAI,WAAU;sCACZ,gBAAgB,SAAS,IAAI,CAAC,MAAM,sBACnC,sSAAC;oCAAgB,WAAU;;sDACzB,sSAAC;4CAAI,WAAU;;8DACb,sSAAC;oDAAI,WAAU;8DACb,cAAA,sSAAC;wDACC,WAAU;wDACV,OAAO;4DACL,OAAO,GAAG,KAAK,SAAS,CAAC,CAAC,CAAC;4DAC3B,iBAAiB,CAAC,IAAI,EAAE,KAAK,SAAS,GAAG,IAAI,WAAW,CAAC;wDAC3D;;;;;;;;;;;8DAGJ,sSAAC;oDAAK,WAAU;;wDAAsB,KAAK,SAAS,CAAC,OAAO,CAAC;wDAAG;;;;;;;;;;;;;sDAElE,sSAAC;4CAAI,WAAU;sDACb,cAAA,sSAAC,0HAAA,CAAA,WAAQ;0DAAE,KAAK,OAAO;;;;;;;;;;;;mCAdjB;;;;;;;;;;sCAmBd,sSAAC,uIAAA,CAAA,oBAAiB;4BAAC,SAAS,IAAM,gBAAgB;4BAAQ,WAAU;sCAAO;;;;;;;;;;;;;;;;;;;;;;;AAOrF;GAhIM;KAAA;uCAkIS", "debugId": null}}, {"offset": {"line": 5438, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/public/images/main-logo.png.mjs%20%28structured%20image%20object%29"], "sourcesContent": ["import src from \"IMAGE\";\nexport default { src, width: 56, height: 31, blurDataURL: \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAECAYAAACzzX7wAAAAj0lEQVR42gGEAHv/AAIGDgsVOnZyLF+pplON4tlmo/z5ZI3HvRQdKSMAAAAAABAvYlsrceDsQ4v2+mSi/v1sp/7+jLn9/GaFrqUpM0A0AC915uY6hPj+SZD7/GCf/v5qpv39m8P9/bXT/v2fut7OACtnxsE4gfTySI/58G+o/fOEtf3zqsz987HQ+/GDnL2pIbRNFU5moJYAAAAASUVORK5CYII=\", blurWidth: 8, blurHeight: 4 }\n"], "names": [], "mappings": ";;;AAAA;;uCACe;IAAE,KAAA,yHAAA,CAAA,UAAG;IAAE,OAAO;IAAI,QAAQ;IAAI,aAAa;IAAsS,WAAW;IAAG,YAAY;AAAE", "debugId": null}}, {"offset": {"line": 5460, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/components/preview/message.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport type { ChatRequestOptions, Message } from 'ai';\r\nimport { motion } from 'framer-motion';\r\nimport { memo, useState, type Dispatch, type SetStateAction } from 'react';\r\n\r\nimport type { Vote } from '@/lib/db/schema';\r\n\r\nimport type { UIBlock } from '@/components/block';\r\nimport { PencilEditIcon, SparklesIcon } from '@/components/icons';\r\nimport { Markdown } from '@/components/markdown';\r\nimport { MessageActions } from '@/components/message-actions';\r\nimport { PreviewAttachment } from '@/components/preview-attachment';\r\nimport equal from 'fast-deep-equal';\r\nimport { cn } from '@/lib/utils';\r\nimport { Button } from '@/components/ui/button';\r\nimport { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip';\r\nimport { MessageEditor } from '@/components/message-editor';\r\nimport SourceDisplay from './source-citation';\r\nimport SourceCitation from './source-citation';\r\nimport BotImage from \"@/public/images/main-logo.png\";\r\n\r\nconst PurePreviewMessage = ({\r\n  chatId,\r\n  message,\r\n  vote,\r\n  isLoading,\r\n  setMessages,\r\n  reload,\r\n  isReadonly,\r\n}: {\r\n  chatId: string;\r\n  message: Message;\r\n  vote: Vote | undefined;\r\n  isLoading: boolean;\r\n  setMessages: (\r\n    messages: Message[] | ((messages: Message[]) => Message[]),\r\n  ) => void;\r\n  reload: (\r\n    chatRequestOptions?: ChatRequestOptions,\r\n  ) => Promise<string | null | undefined>;\r\n  isReadonly: boolean;\r\n}) => {\r\n  const [mode, setMode] = useState<'view' | 'edit'>('view');\r\n\r\n  return (\r\n    <motion.div\r\n      className=\"w-full mx-auto max-w-3xl px-4 group/message\"\r\n      initial={{ y: 5, opacity: 0 }}\r\n      animate={{ y: 0, opacity: 1 }}\r\n      data-role={message.role}\r\n    >\r\n      <div\r\n        className={cn(\r\n          'flex gap-4 w-full group-data-[role=user]/message:ml-auto group-data-[role=user]/message:max-w-2xl',\r\n          {\r\n            'w-full': mode === 'edit',\r\n            'group-data-[role=user]/message:w-fit': mode !== 'edit',\r\n          },\r\n        )}\r\n      >\r\n        {message.role === 'assistant' && (\r\n          <div className=\"size-8 flex items-center rounded-full justify-center shrink-0 bg-background\">\r\n            <div className=\"rounded-full\">\r\n              <img src={BotImage.src} alt=\"Bot\" className=\"w-full h-full object-cover\" />\r\n            </div>\r\n          </div>\r\n        )}\r\n\r\n        <div className=\"flex flex-col gap-2 w-full\">\r\n          {message.experimental_attachments && (\r\n            <div className=\"flex flex-row justify-end gap-2\">\r\n              {message.experimental_attachments.map((attachment) => (\r\n                <PreviewAttachment\r\n                  key={attachment.url}\r\n                  attachment={attachment}\r\n                />\r\n              ))}\r\n            </div>\r\n          )}\r\n\r\n          {message.content && mode === 'view' && (\r\n            <div className=\"flex flex-row gap-2 items-start\">\r\n              {message.role === 'user' && !isReadonly && (\r\n                <Tooltip>\r\n                  <TooltipTrigger asChild>\r\n                    <Button\r\n                      variant=\"ghost\"\r\n                      className=\"px-2 h-fit rounded-full text-muted-foreground opacity-0 group-hover/message:opacity-100\"\r\n                      onClick={() => {\r\n                        setMode('edit');\r\n                      }}\r\n                    >\r\n                      <PencilEditIcon />\r\n                    </Button>\r\n                  </TooltipTrigger>\r\n                  <TooltipContent>Edit message</TooltipContent>\r\n                </Tooltip>\r\n              )}\r\n\r\n              <div\r\n                className={cn('flex flex-col gap-4', {\r\n                  'bg-primary text-primary-foreground px-3 py-2 rounded-xl':\r\n                    message.role === 'user',\r\n                })}\r\n              >\r\n                <Markdown>{message.content as string}</Markdown>\r\n              </div>\r\n            </div>\r\n          )}\r\n\r\n          {message.content && mode === 'edit' && (\r\n            <div className=\"flex flex-row gap-2 items-start\">\r\n              <div className=\"size-8\" />\r\n\r\n              <MessageEditor\r\n                key={message.id}\r\n                message={message}\r\n                setMode={setMode}\r\n                setMessages={setMessages}\r\n                reload={reload}\r\n              />\r\n            </div>\r\n          )}\r\n\r\n          {message.toolInvocations && message.toolInvocations.length > 0 && (\r\n            <div className=\"flex flex-col gap-4\">\r\n              {message.toolInvocations.map((toolInvocation) => {\r\n                const { toolName, toolCallId, state, args } = toolInvocation;\r\n                return (\r\n                  <div key={toolCallId}>  {/* key prop 추가 */}\r\n                    {toolName === 'knowledgeBase' && (\r\n                      <SourceCitation sources={args} />\r\n                    )}\r\n                  </div>\r\n                );\r\n              })}\r\n            </div>\r\n          )}\r\n          {!isReadonly && (\r\n            <MessageActions\r\n              key={`action-${message.id}`}\r\n              chatId={chatId}\r\n              message={message}\r\n              vote={vote}\r\n              isLoading={isLoading}\r\n            />\r\n          )}\r\n        </div>\r\n      </div>\r\n    </motion.div>\r\n  );\r\n};\r\n\r\nexport const PreviewMessage = memo(\r\n  PurePreviewMessage,\r\n  (prevProps, nextProps) => {\r\n    if (prevProps.isLoading !== nextProps.isLoading) return false;\r\n    if (prevProps.message.content !== nextProps.message.content) return false;\r\n    if (\r\n      !equal(\r\n        prevProps.message.toolInvocations,\r\n        nextProps.message.toolInvocations,\r\n      )\r\n    )\r\n      return false;\r\n    if (!equal(prevProps.vote, nextProps.vote)) return false;\r\n\r\n    return true;\r\n  },\r\n);\r\n\r\nexport const ThinkingMessage = () => {\r\n  const role = 'assistant';\r\n\r\n  return (\r\n    <motion.div\r\n      className=\"w-full mx-auto max-w-3xl px-4 group/message\"\r\n      initial={{ y: 5, opacity: 0 }}\r\n      animate={{ y: 0, opacity: 1, transition: { delay: 1 } }}\r\n      data-role={role}\r\n    >\r\n      <div\r\n        className={cn(\r\n          'flex gap-4 group-data-[role=user]/message:px-3 w-full group-data-[role=user]/message:w-fit group-data-[role=user]/message:ml-auto group-data-[role=user]/message:max-w-2xl group-data-[role=user]/message:py-2 rounded-xl',\r\n          {\r\n            'group-data-[role=user]/message:bg-muted': true,\r\n          },\r\n        )}\r\n      >\r\n        <div className=\"size-8 flex items-center rounded-full justify-center shrink-0 bg-background\">\r\n          <div className=\"rounded-full\">\r\n            <img src={BotImage.src} alt=\"Bot\" className=\"w-full h-full object-cover\" />\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"flex flex-col gap-2 w-full\">\r\n          <div className=\"flex flex-col gap-4 mt-1 text-muted-foreground animate-pulse\">\r\n            지식 기반 검색 중입니다...\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </motion.div>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;;AAGA;AACA;AAKA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;;;AApBA;;;;;;;;;;;;;;AAsBA,MAAM,qBAAqB,CAAC,EAC1B,MAAM,EACN,OAAO,EACP,IAAI,EACJ,SAAS,EACT,WAAW,EACX,MAAM,EACN,UAAU,EAaX;;IACC,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAmB;IAElD,qBACE,sSAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAU;QACV,SAAS;YAAE,GAAG;YAAG,SAAS;QAAE;QAC5B,SAAS;YAAE,GAAG;YAAG,SAAS;QAAE;QAC5B,aAAW,QAAQ,IAAI;kBAEvB,cAAA,sSAAC;YACC,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,qGACA;gBACE,UAAU,SAAS;gBACnB,wCAAwC,SAAS;YACnD;;gBAGD,QAAQ,IAAI,KAAK,6BAChB,sSAAC;oBAAI,WAAU;8BACb,cAAA,sSAAC;wBAAI,WAAU;kCACb,cAAA,sSAAC;4BAAI,KAAK,mSAAA,CAAA,UAAQ,CAAC,GAAG;4BAAE,KAAI;4BAAM,WAAU;;;;;;;;;;;;;;;;8BAKlD,sSAAC;oBAAI,WAAU;;wBACZ,QAAQ,wBAAwB,kBAC/B,sSAAC;4BAAI,WAAU;sCACZ,QAAQ,wBAAwB,CAAC,GAAG,CAAC,CAAC,2BACrC,sSAAC,uIAAA,CAAA,oBAAiB;oCAEhB,YAAY;mCADP,WAAW,GAAG;;;;;;;;;;wBAO1B,QAAQ,OAAO,IAAI,SAAS,wBAC3B,sSAAC;4BAAI,WAAU;;gCACZ,QAAQ,IAAI,KAAK,UAAU,CAAC,4BAC3B,sSAAC,+HAAA,CAAA,UAAO;;sDACN,sSAAC,+HAAA,CAAA,iBAAc;4CAAC,OAAO;sDACrB,cAAA,sSAAC,8HAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,WAAU;gDACV,SAAS;oDACP,QAAQ;gDACV;0DAEA,cAAA,sSAAC,uHAAA,CAAA,iBAAc;;;;;;;;;;;;;;;sDAGnB,sSAAC,+HAAA,CAAA,iBAAc;sDAAC;;;;;;;;;;;;8CAIpB,sSAAC;oCACC,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,uBAAuB;wCACnC,2DACE,QAAQ,IAAI,KAAK;oCACrB;8CAEA,cAAA,sSAAC,0HAAA,CAAA,WAAQ;kDAAE,QAAQ,OAAO;;;;;;;;;;;;;;;;;wBAK/B,QAAQ,OAAO,IAAI,SAAS,wBAC3B,sSAAC;4BAAI,WAAU;;8CACb,sSAAC;oCAAI,WAAU;;;;;;8CAEf,sSAAC,mIAAA,CAAA,gBAAa;oCAEZ,SAAS;oCACT,SAAS;oCACT,aAAa;oCACb,QAAQ;mCAJH,QAAQ,EAAE;;;;;;;;;;;wBASpB,QAAQ,eAAe,IAAI,QAAQ,eAAe,CAAC,MAAM,GAAG,mBAC3D,sSAAC;4BAAI,WAAU;sCACZ,QAAQ,eAAe,CAAC,GAAG,CAAC,CAAC;gCAC5B,MAAM,EAAE,QAAQ,EAAE,UAAU,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG;gCAC9C,qBACE,sSAAC;;wCAAqB;wCACnB,aAAa,iCACZ,sSAAC,+IAAA,CAAA,UAAc;4CAAC,SAAS;;;;;;;mCAFnB;;;;;4BAMd;;;;;;wBAGH,CAAC,4BACA,sSAAC,oIAAA,CAAA,iBAAc;4BAEb,QAAQ;4BACR,SAAS;4BACT,MAAM;4BACN,WAAW;2BAJN,CAAC,OAAO,EAAE,QAAQ,EAAE,EAAE;;;;;;;;;;;;;;;;;;;;;;AAWzC;GAlIM;KAAA;AAoIC,MAAM,+BAAiB,CAAA,GAAA,sQAAA,CAAA,OAAI,AAAD,EAC/B,oBACA,CAAC,WAAW;IACV,IAAI,UAAU,SAAS,KAAK,UAAU,SAAS,EAAE,OAAO;IACxD,IAAI,UAAU,OAAO,CAAC,OAAO,KAAK,UAAU,OAAO,CAAC,OAAO,EAAE,OAAO;IACpE,IACE,CAAC,CAAA,GAAA,mNAAA,CAAA,UAAK,AAAD,EACH,UAAU,OAAO,CAAC,eAAe,EACjC,UAAU,OAAO,CAAC,eAAe,GAGnC,OAAO;IACT,IAAI,CAAC,CAAA,GAAA,mNAAA,CAAA,UAAK,AAAD,EAAE,UAAU,IAAI,EAAE,UAAU,IAAI,GAAG,OAAO;IAEnD,OAAO;AACT;;AAGK,MAAM,kBAAkB;IAC7B,MAAM,OAAO;IAEb,qBACE,sSAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAU;QACV,SAAS;YAAE,GAAG;YAAG,SAAS;QAAE;QAC5B,SAAS;YAAE,GAAG;YAAG,SAAS;YAAG,YAAY;gBAAE,OAAO;YAAE;QAAE;QACtD,aAAW;kBAEX,cAAA,sSAAC;YACC,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,6NACA;gBACE,2CAA2C;YAC7C;;8BAGF,sSAAC;oBAAI,WAAU;8BACb,cAAA,sSAAC;wBAAI,WAAU;kCACb,cAAA,sSAAC;4BAAI,KAAK,mSAAA,CAAA,UAAQ,CAAC,GAAG;4BAAE,KAAI;4BAAM,WAAU;;;;;;;;;;;;;;;;8BAIhD,sSAAC;oBAAI,WAAU;8BACb,cAAA,sSAAC;wBAAI,WAAU;kCAA+D;;;;;;;;;;;;;;;;;;;;;;AAOxF;MAhCa", "debugId": null}}, {"offset": {"line": 5792, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/lib/hooks/use-scroll-to-bottom.ts"], "sourcesContent": ["import { useEffect, useRef, type RefObject } from \"react\";\r\n\r\nexport function useScrollToBottom<T extends HTMLElement>(): [\r\n  RefObject<T | null>,\r\n  RefObject<T | null>,\r\n] {\r\n  const containerRef = useRef<T | null>(null);\r\n  const endRef = useRef<T | null>(null);\r\n  const shouldScrollRef = useRef(true);\r\n\r\n  useEffect(() => {\r\n    const container = containerRef.current;\r\n    const end = endRef.current;\r\n\r\n    if (container && end) {\r\n      // Initial scroll\r\n      end.scrollIntoView({ behavior: \"instant\", block: \"end\" });\r\n\r\n      // Check if user has scrolled up\r\n      const handleScroll = () => {\r\n        if (!container) return;\r\n        \r\n        const isAtBottom = Math.abs(\r\n          (container.scrollHeight - container.scrollTop) - container.clientHeight\r\n        ) < 10;\r\n        \r\n        shouldScrollRef.current = isAtBottom;\r\n      };\r\n\r\n      const observer = new MutationObserver(() => {\r\n        // Only scroll if we're at the bottom or it's a new message\r\n        if (shouldScrollRef.current) {\r\n          end.scrollIntoView({ behavior: \"instant\", block: \"end\" });\r\n        }\r\n      });\r\n\r\n      observer.observe(container, {\r\n        childList: true,\r\n        subtree: true, // Watch nested changes\r\n        characterData: true, // Watch text changes\r\n      });\r\n\r\n      // Add scroll listener\r\n      container.addEventListener('scroll', handleScroll);\r\n\r\n      return () => {\r\n        observer.disconnect();\r\n        container.removeEventListener('scroll', handleScroll);\r\n      };\r\n    }\r\n  }, []);\r\n\r\n  return [containerRef, endRef];\r\n}"], "names": [], "mappings": ";;;AAAA;;;AAEO,SAAS;;IAId,MAAM,eAAe,CAAA,GAAA,sQAAA,CAAA,SAAM,AAAD,EAAY;IACtC,MAAM,SAAS,CAAA,GAAA,sQAAA,CAAA,SAAM,AAAD,EAAY;IAChC,MAAM,kBAAkB,CAAA,GAAA,sQAAA,CAAA,SAAM,AAAD,EAAE;IAE/B,CAAA,GAAA,sQAAA,CAAA,YAAS,AAAD;uCAAE;YACR,MAAM,YAAY,aAAa,OAAO;YACtC,MAAM,MAAM,OAAO,OAAO;YAE1B,IAAI,aAAa,KAAK;gBACpB,iBAAiB;gBACjB,IAAI,cAAc,CAAC;oBAAE,UAAU;oBAAW,OAAO;gBAAM;gBAEvD,gCAAgC;gBAChC,MAAM;gEAAe;wBACnB,IAAI,CAAC,WAAW;wBAEhB,MAAM,aAAa,KAAK,GAAG,CACzB,AAAC,UAAU,YAAY,GAAG,UAAU,SAAS,GAAI,UAAU,YAAY,IACrE;wBAEJ,gBAAgB,OAAO,GAAG;oBAC5B;;gBAEA,MAAM,WAAW,IAAI;mDAAiB;wBACpC,2DAA2D;wBAC3D,IAAI,gBAAgB,OAAO,EAAE;4BAC3B,IAAI,cAAc,CAAC;gCAAE,UAAU;gCAAW,OAAO;4BAAM;wBACzD;oBACF;;gBAEA,SAAS,OAAO,CAAC,WAAW;oBAC1B,WAAW;oBACX,SAAS;oBACT,eAAe;gBACjB;gBAEA,sBAAsB;gBACtB,UAAU,gBAAgB,CAAC,UAAU;gBAErC;mDAAO;wBACL,SAAS,UAAU;wBACnB,UAAU,mBAAmB,CAAC,UAAU;oBAC1C;;YACF;QACF;sCAAG,EAAE;IAEL,OAAO;QAAC;QAAc;KAAO;AAC/B;GAnDgB", "debugId": null}}, {"offset": {"line": 5863, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Card = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\r\n      \"rounded-lg border bg-card text-card-foreground shadow-sm\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nCard.displayName = \"Card\"\r\n\r\nconst CardHeader = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\r\n    {...props}\r\n  />\r\n))\r\nCardHeader.displayName = \"CardHeader\"\r\n\r\nconst CardTitle = React.forwardRef<\r\n  HTMLParagraphElement,\r\n  React.HTMLAttributes<HTMLHeadingElement>\r\n>(({ className, ...props }, ref) => (\r\n  <h3\r\n    ref={ref}\r\n    className={cn(\r\n      \"text-2xl font-semibold leading-none tracking-tight\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nCardTitle.displayName = \"CardTitle\"\r\n\r\nconst CardDescription = React.forwardRef<\r\n  HTMLParagraphElement,\r\n  React.HTMLAttributes<HTMLParagraphElement>\r\n>(({ className, ...props }, ref) => (\r\n  <p\r\n    ref={ref}\r\n    className={cn(\"text-sm text-muted-foreground\", className)}\r\n    {...props}\r\n  />\r\n))\r\nCardDescription.displayName = \"CardDescription\"\r\n\r\nconst CardContent = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\r\n))\r\nCardContent.displayName = \"CardContent\"\r\n\r\nconst CardFooter = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"flex items-center p-6 pt-0\", className)}\r\n    {...props}\r\n  />\r\n))\r\nCardFooter.displayName = \"CardFooter\"\r\n\r\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\r\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AAEA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,sQAAA,CAAA,aAAgB,AAAD,OAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,sSAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,4DACA;QAED,GAAG,KAAK;;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,sQAAA,CAAA,aAAgB,AAAD,QAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,sSAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,sQAAA,CAAA,aAAgB,AAAD,QAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,sSAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,sQAAA,CAAA,aAAgB,AAAD,QAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,sSAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,sQAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,sSAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,sQAAA,CAAA,aAAgB,AAAD,SAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,sSAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 5966, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/components/ui/accordion.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as AccordionPrimitive from \"@radix-ui/react-accordion\"\r\nimport { ChevronDown } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Accordion = AccordionPrimitive.Root\r\n\r\nconst AccordionItem = React.forwardRef<\r\n  React.ElementRef<typeof AccordionPrimitive.Item>,\r\n  React.ComponentPropsWithoutRef<typeof AccordionPrimitive.Item>\r\n>(({ className, ...props }, ref) => (\r\n  <AccordionPrimitive.Item\r\n    ref={ref}\r\n    className={cn(\"border-b\", className)}\r\n    {...props}\r\n  />\r\n))\r\nAccordionItem.displayName = \"AccordionItem\"\r\n\r\nconst AccordionTrigger = React.forwardRef<\r\n  React.ElementRef<typeof AccordionPrimitive.Trigger>,\r\n  React.ComponentPropsWithoutRef<typeof AccordionPrimitive.Trigger>\r\n>(({ className, children, ...props }, ref) => (\r\n  <AccordionPrimitive.Header className=\"flex\">\r\n    <AccordionPrimitive.Trigger\r\n      ref={ref}\r\n      className={cn(\r\n        \"flex flex-1 items-center justify-between py-4 font-medium transition-all hover:underline [&[data-state=open]>svg]:rotate-180\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      {children}\r\n      <ChevronDown className=\"h-4 w-4 shrink-0 transition-transform duration-200\" />\r\n    </AccordionPrimitive.Trigger>\r\n  </AccordionPrimitive.Header>\r\n))\r\nAccordionTrigger.displayName = AccordionPrimitive.Trigger.displayName\r\n\r\nconst AccordionContent = React.forwardRef<\r\n  React.ElementRef<typeof AccordionPrimitive.Content>,\r\n  React.ComponentPropsWithoutRef<typeof AccordionPrimitive.Content>\r\n>(({ className, children, ...props }, ref) => (\r\n  <AccordionPrimitive.Content\r\n    ref={ref}\r\n    className=\"overflow-hidden text-sm transition-all data-[state=closed]:animate-accordion-up data-[state=open]:animate-accordion-down\"\r\n    {...props}\r\n  >\r\n    <div className={cn(\"pb-4 pt-0\", className)}>{children}</div>\r\n  </AccordionPrimitive.Content>\r\n))\r\n\r\nAccordionContent.displayName = AccordionPrimitive.Content.displayName\r\n\r\nexport { Accordion, AccordionItem, AccordionTrigger, AccordionContent }\r\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;AACA;AAEA;AANA;;;;;;AAQA,MAAM,YAAY,+QAAA,CAAA,OAAuB;AAEzC,MAAM,8BAAgB,CAAA,GAAA,sQAAA,CAAA,aAAgB,AAAD,OAGnC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,sSAAC,+QAAA,CAAA,OAAuB;QACtB,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QACzB,GAAG,KAAK;;;;;;;AAGb,cAAc,WAAW,GAAG;AAE5B,MAAM,iCAAmB,CAAA,GAAA,sQAAA,CAAA,aAAgB,AAAD,QAGtC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,sSAAC,+QAAA,CAAA,SAAyB;QAAC,WAAU;kBACnC,cAAA,sSAAC,+QAAA,CAAA,UAA0B;YACzB,KAAK;YACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,gIACA;YAED,GAAG,KAAK;;gBAER;8BACD,sSAAC,2SAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;;;;;;;;;;;;;AAI7B,iBAAiB,WAAW,GAAG,+QAAA,CAAA,UAA0B,CAAC,WAAW;AAErE,MAAM,iCAAmB,CAAA,GAAA,sQAAA,CAAA,aAAgB,AAAD,QAGtC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,sSAAC,+QAAA,CAAA,UAA0B;QACzB,KAAK;QACL,WAAU;QACT,GAAG,KAAK;kBAET,cAAA,sSAAC;YAAI,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,aAAa;sBAAa;;;;;;;;;;;;AAIjD,iBAAiB,WAAW,GAAG,+QAAA,CAAA,UAA0B,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 6059, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/components/preview/overview.tsx"], "sourcesContent": ["import { motion } from 'framer-motion';\nimport { BotIcon, CodeIcon, BuildingIcon } from 'lucide-react';\nimport { Card, CardContent } from \"@/components/ui/card\";\nimport {\n  Accordion,\n  AccordionContent,\n  AccordionItem,\n  AccordionTrigger,\n} from \"@/components/ui/accordion\";\n\ninterface OverviewProps {\n  selectedModelId?: string;\n}\n\n// 모델별 설정 정의\nconst modelConfigs = {\n  '지자체 공간정보 플랫폼 챗봇': {\n    icon: BuildingIcon,\n    title: '지자체 공간정보 플랫폼 챗봇',\n    description: '플랫폼에 대해 궁금한 점을 여쭤보세요!',\n    accordionTitle: '플랫폼 지원 범위',\n    sections: [\n      { title: \"지원 데이터 종류\" },\n      { title: \"플랫폼 목적 및 기능\" },\n      { title: \"지도공간 서비스\" },\n      { title: \"업무공간 서비스\" }\n    ],\n    footerText: '지자체 공간정보 플랫폼에 대한 자세한 정보를 제공해드립니다.'\n  },\n  '지도개발 어시스턴트': {\n    icon: CodeIcon,\n    title: '지도개발 어시스턴트',\n    description: '지도 개발과 관련된 질문을 해보세요',\n    accordionTitle: '개발 지원 범위',\n    sections: [\n      { title: \"지도 생성 방법\" },\n      { title: \"레이어 생성\" },\n      { title: \"이벤트 처리\" },\n      { title: \"컨트롤/UI\" }\n    ],\n    footerText: '코드 예제와 함께 자세한 설명을 제공해드립니다.'\n  }\n};\n\nexport const Overview = ({ selectedModelId = '지도개발 어시스턴트' }: OverviewProps) => {\n  // 선택된 모델의 설정 가져오기\n  const config = modelConfigs[selectedModelId as keyof typeof modelConfigs] || modelConfigs['지도개발 어시스턴트'];\n  const IconComponent = config.icon;\n\n  return (\n    <motion.div\n      key={`overview-${selectedModelId}`}\n      initial={{ opacity: 0, y: 10 }}\n      animate={{ opacity: 1, y: 0 }}\n      exit={{ opacity: 0, y: -10 }}\n      transition={{ duration: 0.3 }}\n      className=\"w-full max-w-sm backdrop-blur-sm mx-auto bg-gradient-to-b from-background/10 to-background/80 rounded-xl\"\n    >\n      <Card className=\"border-none shadow-none bg-transparent\">\n        <CardContent className=\"p-6 space-y-6\">\n          <div className=\"relative\">\n            <motion.div\n              className=\"flex items-center justify-center gap-4\"\n              initial={{ scale: 0.9 }}\n              animate={{ scale: 1 }}\n              transition={{ delay: 0.2, type: \"spring\", stiffness: 150 }}\n            >\n              <IconComponent size={28} className=\"text-primary\" />\n              <span className=\"font-bold text-2xl\">+</span>\n              <BotIcon size={28} className=\"text-primary\" />\n            </motion.div>\n            <motion.div\n              className=\"text-center mt-4\"\n              initial={{ opacity: 0 }}\n              animate={{ opacity: 1 }}\n              transition={{ delay: 0.3 }}\n            >\n              <h2 className=\"text-lg font-semibold bg-clip-text bg-gradient-to-r from-primary to-primary/80\">\n                {config.title}\n              </h2>\n            </motion.div>\n          </div>\n\n          <motion.div\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            transition={{ delay: 0.4 }}\n            className=\"space-y-4\"\n          >\n            <p className=\"text-sm text-center\">\n              {config.description}\n            </p>\n\n            <Accordion type=\"single\" collapsible defaultValue=\"examples\" className=\"flex justify-center bg-background/40 rounded-lg\">\n              <AccordionItem value=\"examples\" className=\"border-none\">\n                <AccordionTrigger className=\"justify-center gap-2 py-3 hover:no-underline\">\n                  <span className=\"text-sm font-medium\">{config.accordionTitle}</span>\n                </AccordionTrigger>\n                <AccordionContent className=\"py-4\">\n                  <motion.div\n                    className=\"space-y-6\"\n                    variants={{\n                      hidden: { opacity: 0 },\n                      show: {\n                        opacity: 1,\n                        transition: { staggerChildren: 0.1 }\n                      }\n                    }}\n                    initial=\"hidden\"\n                    animate=\"show\"\n                  >\n                    {config.sections.map((section, idx) => (\n                      <motion.div\n                        key={idx}\n                        variants={{\n                          hidden: { opacity: 0, y: 10 },\n                          show: { opacity: 1, y: 0 }\n                        }}\n                        className=\"space-y-2\"\n                      >\n                        <h3 className=\"text-sm font-medium text-primary\">{section.title}</h3>\n                      </motion.div>\n                    ))}\n                  </motion.div>\n                </AccordionContent>\n              </AccordionItem>\n            </Accordion>\n          </motion.div>\n\n          <motion.p\n            className=\"text-xs text-center text-muted-foreground\"\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            transition={{ delay: 0.5 }}\n          >\n            {config.footerText}\n          </motion.p>\n        </CardContent>\n      </Card>\n    </motion.div>\n  );\n};\n\nexport default Overview;"], "names": [], "mappings": ";;;;;AAAA;AACA;AAAA;AAAA;AACA;AACA;;;;;;AAWA,YAAY;AACZ,MAAM,eAAe;IACnB,mBAAmB;QACjB,MAAM,qSAAA,CAAA,eAAY;QAClB,OAAO;QACP,aAAa;QACb,gBAAgB;QAChB,UAAU;YACR;gBAAE,OAAO;YAAY;YACrB;gBAAE,OAAO;YAAc;YACvB;gBAAE,OAAO;YAAW;YACpB;gBAAE,OAAO;YAAW;SACrB;QACD,YAAY;IACd;IACA,cAAc;QACZ,MAAM,6RAAA,CAAA,WAAQ;QACd,OAAO;QACP,aAAa;QACb,gBAAgB;QAChB,UAAU;YACR;gBAAE,OAAO;YAAW;YACpB;gBAAE,OAAO;YAAS;YAClB;gBAAE,OAAO;YAAS;YAClB;gBAAE,OAAO;YAAS;SACnB;QACD,YAAY;IACd;AACF;AAEO,MAAM,WAAW,CAAC,EAAE,kBAAkB,YAAY,EAAiB;IACxE,kBAAkB;IAClB,MAAM,SAAS,YAAY,CAAC,gBAA6C,IAAI,YAAY,CAAC,aAAa;IACvG,MAAM,gBAAgB,OAAO,IAAI;IAEjC,qBACE,sSAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;QAET,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,MAAM;YAAE,SAAS;YAAG,GAAG,CAAC;QAAG;QAC3B,YAAY;YAAE,UAAU;QAAI;QAC5B,WAAU;kBAEV,cAAA,sSAAC,4HAAA,CAAA,OAAI;YAAC,WAAU;sBACd,cAAA,sSAAC,4HAAA,CAAA,cAAW;gBAAC,WAAU;;kCACrB,sSAAC;wBAAI,WAAU;;0CACb,sSAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;gCACT,WAAU;gCACV,SAAS;oCAAE,OAAO;gCAAI;gCACtB,SAAS;oCAAE,OAAO;gCAAE;gCACpB,YAAY;oCAAE,OAAO;oCAAK,MAAM;oCAAU,WAAW;gCAAI;;kDAEzD,sSAAC;wCAAc,MAAM;wCAAI,WAAU;;;;;;kDACnC,sSAAC;wCAAK,WAAU;kDAAqB;;;;;;kDACrC,sSAAC,2RAAA,CAAA,UAAO;wCAAC,MAAM;wCAAI,WAAU;;;;;;;;;;;;0CAE/B,sSAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;gCACT,WAAU;gCACV,SAAS;oCAAE,SAAS;gCAAE;gCACtB,SAAS;oCAAE,SAAS;gCAAE;gCACtB,YAAY;oCAAE,OAAO;gCAAI;0CAEzB,cAAA,sSAAC;oCAAG,WAAU;8CACX,OAAO,KAAK;;;;;;;;;;;;;;;;;kCAKnB,sSAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;wBAAE;wBACtB,SAAS;4BAAE,SAAS;wBAAE;wBACtB,YAAY;4BAAE,OAAO;wBAAI;wBACzB,WAAU;;0CAEV,sSAAC;gCAAE,WAAU;0CACV,OAAO,WAAW;;;;;;0CAGrB,sSAAC,iIAAA,CAAA,YAAS;gCAAC,MAAK;gCAAS,WAAW;gCAAC,cAAa;gCAAW,WAAU;0CACrE,cAAA,sSAAC,iIAAA,CAAA,gBAAa;oCAAC,OAAM;oCAAW,WAAU;;sDACxC,sSAAC,iIAAA,CAAA,mBAAgB;4CAAC,WAAU;sDAC1B,cAAA,sSAAC;gDAAK,WAAU;0DAAuB,OAAO,cAAc;;;;;;;;;;;sDAE9D,sSAAC,iIAAA,CAAA,mBAAgB;4CAAC,WAAU;sDAC1B,cAAA,sSAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;gDACT,WAAU;gDACV,UAAU;oDACR,QAAQ;wDAAE,SAAS;oDAAE;oDACrB,MAAM;wDACJ,SAAS;wDACT,YAAY;4DAAE,iBAAiB;wDAAI;oDACrC;gDACF;gDACA,SAAQ;gDACR,SAAQ;0DAEP,OAAO,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,oBAC7B,sSAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;wDAET,UAAU;4DACR,QAAQ;gEAAE,SAAS;gEAAG,GAAG;4DAAG;4DAC5B,MAAM;gEAAE,SAAS;gEAAG,GAAG;4DAAE;wDAC3B;wDACA,WAAU;kEAEV,cAAA,sSAAC;4DAAG,WAAU;sEAAoC,QAAQ,KAAK;;;;;;uDAP1D;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAgBnB,sSAAC,mSAAA,CAAA,SAAM,CAAC,CAAC;wBACP,WAAU;wBACV,SAAS;4BAAE,SAAS;wBAAE;wBACtB,SAAS;4BAAE,SAAS;wBAAE;wBACtB,YAAY;4BAAE,OAAO;wBAAI;kCAExB,OAAO,UAAU;;;;;;;;;;;;;;;;;OApFnB,CAAC,SAAS,EAAE,iBAAiB;;;;;AA0FxC;KAjGa;uCAmGE", "debugId": null}}, {"offset": {"line": 6383, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/components/preview/messages.tsx"], "sourcesContent": ["import { ChatRequestOptions, Message } from 'ai';\r\nimport { PreviewMessage, ThinkingMessage } from './message';\r\nimport { useScrollToBottom } from '@/lib/hooks/use-scroll-to-bottom';\r\nimport { Overview } from '@/components/preview/overview';\r\nimport { UIBlock } from '@/components/block';\r\nimport { Dispatch, memo, SetStateAction } from 'react';\r\nimport { Vote } from '@/lib/db/schema';\r\nimport equal from 'fast-deep-equal';\r\n\r\ninterface MessagesProps {\r\n  chatId: string;\r\n  isLoading: boolean;\r\n  votes: Array<Vote> | undefined;\r\n  messages: Array<Message>;\r\n  setMessages: (\r\n    messages: Message[] | ((messages: Message[]) => Message[]),\r\n  ) => void;\r\n  reload: (\r\n    chatRequestOptions?: ChatRequestOptions,\r\n  ) => Promise<string | null | undefined>;\r\n  isReadonly: boolean;\r\n  selectedModelId?: string;\r\n}\r\n\r\nfunction PureMessages({\r\n  chatId,\r\n  isLoading,\r\n  votes,\r\n  messages,\r\n  setMessages,\r\n  reload,\r\n  isReadonly,\r\n  selectedModelId,\r\n}: MessagesProps) {\r\n  const [messagesContainerRef, messagesEndRef] =\r\n    useScrollToBottom<HTMLDivElement>();\r\n\r\n  return (\r\n    <div\r\n      ref={messagesContainerRef}\r\n      className=\"flex flex-col min-w-0 gap-6 flex-1 overflow-y-auto px-4 pt-4 styled-scrollbar\"\r\n    >\r\n      {messages.length === 0 && <Overview selectedModelId={selectedModelId} />}\r\n\r\n      {messages.map((message, index) => (\r\n        <PreviewMessage\r\n          key={message.id}\r\n          chatId={chatId}\r\n          message={message}\r\n          isLoading={isLoading && messages.length - 1 === index}\r\n          vote={\r\n            votes\r\n              ? votes.find((vote) => vote.messageId === message.id)\r\n              : undefined\r\n          }\r\n          setMessages={setMessages}\r\n          reload={reload}\r\n          isReadonly={isReadonly}\r\n        />\r\n      ))}\r\n\r\n      {isLoading &&\r\n        messages.length > 0 &&\r\n        messages[messages.length - 1].role === 'user' && <ThinkingMessage />}\r\n\r\n      <div\r\n        ref={messagesEndRef}\r\n        className=\"shrink-0 min-w-[24px] min-h-[24px]\"\r\n      />\r\n    </div>\r\n  );\r\n}\r\n\r\nexport const Messages = memo(PureMessages, (prevProps, nextProps) => {\r\n  if (prevProps.isLoading !== nextProps.isLoading) return false;\r\n  if (prevProps.isLoading && nextProps.isLoading) return false;\r\n  if (prevProps.messages.length !== nextProps.messages.length) return false;\r\n  if (!equal(prevProps.votes, nextProps.votes)) return false;\r\n  if (prevProps.selectedModelId !== nextProps.selectedModelId) return false;\r\n\r\n  return true;\r\n});\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AAEA;AAEA;;;;;;;;AAiBA,SAAS,aAAa,EACpB,MAAM,EACN,SAAS,EACT,KAAK,EACL,QAAQ,EACR,WAAW,EACX,MAAM,EACN,UAAU,EACV,eAAe,EACD;;IACd,MAAM,CAAC,sBAAsB,eAAe,GAC1C,CAAA,GAAA,gJAAA,CAAA,oBAAiB,AAAD;IAElB,qBACE,sSAAC;QACC,KAAK;QACL,WAAU;;YAET,SAAS,MAAM,KAAK,mBAAK,sSAAC,qIAAA,CAAA,WAAQ;gBAAC,iBAAiB;;;;;;YAEpD,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,sSAAC,oIAAA,CAAA,iBAAc;oBAEb,QAAQ;oBACR,SAAS;oBACT,WAAW,aAAa,SAAS,MAAM,GAAG,MAAM;oBAChD,MACE,QACI,MAAM,IAAI,CAAC,CAAC,OAAS,KAAK,SAAS,KAAK,QAAQ,EAAE,IAClD;oBAEN,aAAa;oBACb,QAAQ;oBACR,YAAY;mBAXP,QAAQ,EAAE;;;;;YAelB,aACC,SAAS,MAAM,GAAG,KAClB,QAAQ,CAAC,SAAS,MAAM,GAAG,EAAE,CAAC,IAAI,KAAK,wBAAU,sSAAC,oIAAA,CAAA,kBAAe;;;;;0BAEnE,sSAAC;gBACC,KAAK;gBACL,WAAU;;;;;;;;;;;;AAIlB;GA/CS;;QAWL,gJAAA,CAAA,oBAAiB;;;KAXZ;AAiDF,MAAM,yBAAW,CAAA,GAAA,sQAAA,CAAA,OAAI,AAAD,EAAE,cAAc,CAAC,WAAW;IACrD,IAAI,UAAU,SAAS,KAAK,UAAU,SAAS,EAAE,OAAO;IACxD,IAAI,UAAU,SAAS,IAAI,UAAU,SAAS,EAAE,OAAO;IACvD,IAAI,UAAU,QAAQ,CAAC,MAAM,KAAK,UAAU,QAAQ,CAAC,MAAM,EAAE,OAAO;IACpE,IAAI,CAAC,CAAA,GAAA,mNAAA,CAAA,UAAK,AAAD,EAAE,UAAU,KAAK,EAAE,UAAU,KAAK,GAAG,OAAO;IACrD,IAAI,UAAU,eAAe,KAAK,UAAU,eAAe,EAAE,OAAO;IAEpE,OAAO;AACT", "debugId": null}}, {"offset": {"line": 6473, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/components/footer.tsx"], "sourcesContent": ["import React from 'react'\r\n\r\nimport { cn } from '@/lib/utils'\r\n\r\nexport function FooterText({ className, ...props }: React.ComponentProps<'p'>) {\r\n\treturn (\r\n\t\t<p\r\n\t\t\tclassName={cn(\r\n\t\t\t\t'px-2 text-center text-xs leading-normal text-muted-foreground',\r\n\t\t\t\tclassName\r\n\t\t\t)}\r\n\t\t\t{...props}\r\n\t\t>\r\n\t\t\t작업을 도와드리기 위해 최선을 다하지만 실패할 수 있습니다.\r\n\t\t\t{/* 문서와 관련된 자세한 내용은 {' '} */}\r\n\t\t\t{/* <ExternalLink href=\"https://developer.geon.kr\">개발자 지원센터</ExternalLink> 를 확인해주세요 */}\r\n\t\t\t\r\n\t\t</p>\r\n\t)\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;;;AAEO,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAkC;IAC5E,qBACC,sSAAC;QACA,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACX,iEACA;QAEA,GAAG,KAAK;kBACT;;;;;;AAOH;KAfgB", "debugId": null}}, {"offset": {"line": 6503, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/components/preview-panel.tsx"], "sourcesContent": ["import { AnimatePresence, motion } from 'framer-motion';\r\nimport React, { memo, useState, useEffect, useRef } from 'react';\r\nimport { usePreview } from '@/lib/hooks/use-preview';\r\nimport { CodeEditor } from './code-editor';\r\nimport { cn } from '@/lib/utils';\r\nimport {\r\n  ChevronLeft,\r\n  ChevronRight,\r\n  Maximize2,\r\n  MinusSquare,\r\n  X,\r\n  Minimize2\r\n} from 'lucide-react';\r\n\r\ninterface PreviewPanelProps {\r\n  isReadonly?: boolean;\r\n  isVisible?: boolean;\r\n}\r\n\r\ninterface NavigationState {\r\n  history: string[];\r\n  currentIndex: number;\r\n}\r\n\r\ntype Tab = 'preview' | 'code' | 'console';\r\n\r\ninterface ConsoleLog {\r\n  type: 'log' | 'error' | 'warn';\r\n  timestamp: number;\r\n  content: string;\r\n}\r\n\r\nconst TabButton = memo(({ selected, label, onClick }: { selected: boolean; label: string; onClick: () => void }) => (\r\n  <button\r\n    onClick={onClick}\r\n    className={cn(\r\n      \"px-4 py-2 text-sm font-medium transition-colors\",\r\n      selected ? \"text-foreground border-b-2 border-primary\" : \"text-muted-foreground hover:text-foreground\"\r\n    )}\r\n  >\r\n    {label}\r\n  </button>\r\n));\r\n\r\nTabButton.displayName = 'TabButton';\r\n\r\nconst ConsoleOutput = memo(({ logs }: { logs: ConsoleLog[] }) => (\r\n  <div className=\"font-mono text-sm p-4 space-y-2 h-full overflow-auto styled-scrollbar\">\r\n    {logs.map((log, i) => (\r\n      <div key={i} className={cn(\"flex items-start gap-2\", {\r\n        \"text-red-500\": log.type === 'error',\r\n        \"text-yellow-500\": log.type === 'warn'\r\n      })}>\r\n        <span className=\"text-muted-foreground text-xs\">\r\n          {new Date(log.timestamp).toLocaleTimeString()}\r\n        </span>\r\n        <span>{log.content}</span>\r\n      </div>\r\n    ))}\r\n  </div>\r\n));\r\n\r\nConsoleOutput.displayName = 'ConsoleOutput';\r\n\r\n// HTML 프리뷰에 console 캡처 코드를 주입하는 함수\r\nconst injectConsoleCapture = (html: string) => `\r\n  <!DOCTYPE html>\r\n  <html>\r\n    <head>\r\n      <script>\r\n        const originalConsole = {\r\n          log: console.log,\r\n          error: console.error,\r\n          warn: console.warn\r\n        };\r\n        \r\n        function captureConsole(type, args) {\r\n          window.parent.postMessage({\r\n            type: 'console',\r\n            logType: type,\r\n            content: Array.from(args).map(arg => \r\n              typeof arg === 'object' ? JSON.stringify(arg) : String(arg)\r\n            ).join(' '),\r\n            timestamp: Date.now()\r\n          }, '*');\r\n        }\r\n        \r\n        console.log = (...args) => {\r\n          originalConsole.log(...args);\r\n          captureConsole('log', args);\r\n        };\r\n        console.error = (...args) => {\r\n          originalConsole.error(...args);\r\n          captureConsole('error', args);\r\n        };\r\n        console.warn = (...args) => {\r\n          originalConsole.warn(...args);\r\n          captureConsole('warn', args);\r\n        };\r\n      </script>\r\n    </head>\r\n    <body>\r\n      ${html}\r\n    </body>\r\n  </html>\r\n`;\r\n\r\nfunction PurePreviewPanel({ isReadonly = false, isVisible = false }: PreviewPanelProps) {\r\n  const { preview, setPreview } = usePreview();\r\n  const [activeTab, setActiveTab] = useState<Tab>('preview');\r\n  const [isFullscreen, setIsFullscreen] = useState(false);\r\n  const [consoleLogs, setConsoleLogs] = useState<ConsoleLog[]>([]);\r\n  const [navigation, setNavigation] = useState<NavigationState>({\r\n    history: [],\r\n    currentIndex: -1\r\n  });\r\n\r\n  useEffect(() => {\r\n    if (preview.content && !navigation.history.includes(preview.content)) {\r\n      setNavigation(prev => ({\r\n        history: [...prev.history, preview.content],\r\n        currentIndex: prev.history.length\r\n      }));\r\n    }\r\n  }, [preview.content]);\r\n\r\n  const handleNavigate = (direction: 'back' | 'forward') => {\r\n    const newIndex = direction === 'back'\r\n      ? navigation.currentIndex - 1\r\n      : navigation.currentIndex + 1;\r\n\r\n    if (newIndex >= 0 && newIndex < navigation.history.length) {\r\n      setNavigation(prev => ({ ...prev, currentIndex: newIndex }));\r\n      setPreview(prev => ({\r\n        ...prev,\r\n        content: navigation.history[newIndex]\r\n      }));\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    const handleConsoleMessage = (event: MessageEvent) => {\r\n      if (event.data.type === 'console') {\r\n        setConsoleLogs(prev => [...prev, {\r\n          type: event.data.logType,\r\n          timestamp: event.data.timestamp,\r\n          content: event.data.content\r\n        }]);\r\n      }\r\n    };\r\n\r\n    window.addEventListener('message', handleConsoleMessage);\r\n    return () => window.removeEventListener('message', handleConsoleMessage);\r\n  }, []);\r\n\r\n  const handleClose = () => {\r\n    setPreview(prev => ({ ...prev, isVisible: false }));\r\n    setIsFullscreen(false);\r\n  };\r\n\r\n  const containerClassName = cn(\r\n    \"relative border-l border-border/30 overflow-hidden backdrop-blur-sm transition-all duration-300\",\r\n    {\r\n      \"fixed inset-0 z-50\": isFullscreen,\r\n    }\r\n  );\r\n\r\n  return (\r\n    <AnimatePresence>\r\n      {isVisible && (\r\n        <motion.div\r\n          initial={{ width:'100%', x: '100%', opacity: 0 }}\r\n          animate={{\r\n            x: 0,\r\n            opacity: 1\r\n          }}\r\n          exit={{ \r\n            x: '100%', \r\n            opacity: 0, \r\n            transition: { \r\n              duration: 0.1, \r\n              ease: \"easeOut\" \r\n            } \r\n          }}\r\n          transition={{\r\n            duration: 0.1,\r\n            ease: \"easeOut\"\r\n          }}\r\n          className={containerClassName}\r\n        >\r\n          <div className=\"flex flex-col h-full bg-background/80\">\r\n            <div className=\"flex items-center justify-between px-4 py-2 border-b border-border/30\">\r\n              <div className=\"flex items-center\">\r\n\r\n                <button\r\n                  onClick={handleClose}\r\n                  className=\"p-2 hover:bg-muted rounded-md text-muted-foreground hover:text-foreground\"\r\n                >\r\n                  <X size={16} />\r\n                </button>\r\n\r\n                <div className=\"ml-2 flex items-center gap-2\">\r\n                  <TabButton\r\n                    selected={activeTab === 'preview'}\r\n                    label=\"Preview\"\r\n                    onClick={() => setActiveTab('preview')}\r\n                  />\r\n                  <TabButton\r\n                    selected={activeTab === 'code'}\r\n                    label=\"Code\"\r\n                    onClick={() => setActiveTab('code')}\r\n                  />\r\n                  <TabButton\r\n                    selected={activeTab === 'console'}\r\n                    label=\"Console\"\r\n                    onClick={() => setActiveTab('console')}\r\n                  />\r\n                </div>\r\n              </div>\r\n\r\n              <div className=\"flex items-center gap-2\">\r\n                <button\r\n                  onClick={() => handleNavigate('back')}\r\n                  disabled={navigation.currentIndex <= 0}\r\n                  className=\"p-2 hover:bg-muted rounded-md text-muted-foreground hover:text-foreground disabled:opacity-50\"\r\n                >\r\n                  <ChevronLeft size={16} />\r\n                </button>\r\n                <button\r\n                  onClick={() => handleNavigate('forward')}\r\n                  disabled={navigation.currentIndex >= navigation.history.length - 1}\r\n                  className=\"p-2 hover:bg-muted rounded-md text-muted-foreground hover:text-foreground disabled:opacity-50\"\r\n                >\r\n                  <ChevronRight size={16} />\r\n                </button>\r\n                <button\r\n                  onClick={() => setIsFullscreen(prev => !prev)}\r\n                  className=\"p-2 hover:bg-muted rounded-md text-muted-foreground hover:text-foreground\"\r\n                >\r\n                  {isFullscreen ? <Minimize2 size={16} /> : <Maximize2 size={16} />}\r\n                </button>\r\n\r\n              </div>\r\n            </div>\r\n\r\n            <div className=\"flex-1 overflow-auto styled-scrollbar\">\r\n              <div className=\"h-full\">\r\n                {activeTab === 'preview' && preview.kind === 'html' && (\r\n                  <iframe\r\n                    srcDoc={injectConsoleCapture(preview.content)}\r\n                    className=\"w-full h-full border-0 bg-white dark:bg-zinc-900\"\r\n                    sandbox=\"allow-scripts\"\r\n                    title=\"HTML Preview\"\r\n                  />\r\n                )}\r\n                {activeTab === 'preview' && preview.kind !== 'html' && (\r\n                  <CodeEditor\r\n                    content={preview.content}\r\n                    language={preview.kind}\r\n                    mode=\"edit\"\r\n                    status=\"idle\"\r\n                  />\r\n                )}\r\n                {activeTab === 'code' && (\r\n                  <CodeEditor\r\n                    content={preview.content}\r\n                    language={preview.kind}\r\n                    mode=\"edit\"\r\n                    status=\"idle\"\r\n                    lineNumbers={true}\r\n                  />\r\n                )}\r\n                {activeTab === 'console' && (\r\n                  <ConsoleOutput logs={consoleLogs} />\r\n                )}\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </motion.div>\r\n      )}\r\n    </AnimatePresence>\r\n  );\r\n}\r\n\r\nexport const PreviewPanel = memo(PurePreviewPanel, (prevProps, nextProps) => {\r\n  return prevProps.isVisible === nextProps.isVisible &&\r\n    prevProps.isReadonly === nextProps.isReadonly;\r\n});\r\n\r\nPreviewPanel.displayName = 'PreviewPanel';"], "names": [], "mappings": ";;;;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;AA2BA,MAAM,0BAAY,CAAA,GAAA,sQAAA,CAAA,OAAI,AAAD,EAAE,CAAC,EAAE,QAAQ,EAAE,KAAK,EAAE,OAAO,EAA6D,iBAC7G,sSAAC;QACC,SAAS;QACT,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,mDACA,WAAW,8CAA8C;kBAG1D;;;;;;KARC;AAYN,UAAU,WAAW,GAAG;AAExB,MAAM,8BAAgB,CAAA,GAAA,sQAAA,CAAA,OAAI,AAAD,EAAE,CAAC,EAAE,IAAI,EAA0B,iBAC1D,sSAAC;QAAI,WAAU;kBACZ,KAAK,GAAG,CAAC,CAAC,KAAK,kBACd,sSAAC;gBAAY,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,0BAA0B;oBACnD,gBAAgB,IAAI,IAAI,KAAK;oBAC7B,mBAAmB,IAAI,IAAI,KAAK;gBAClC;;kCACE,sSAAC;wBAAK,WAAU;kCACb,IAAI,KAAK,IAAI,SAAS,EAAE,kBAAkB;;;;;;kCAE7C,sSAAC;kCAAM,IAAI,OAAO;;;;;;;eAPV;;;;;;;;;;MAHV;AAgBN,cAAc,WAAW,GAAG;AAE5B,mCAAmC;AACnC,MAAM,uBAAuB,CAAC,OAAiB,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;MAqC1C,EAAE,KAAK;;;AAGb,CAAC;AAED,SAAS,iBAAiB,EAAE,aAAa,KAAK,EAAE,YAAY,KAAK,EAAqB;;IACpF,MAAM,EAAE,OAAO,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,aAAU,AAAD;IACzC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAO;IAChD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAgB,EAAE;IAC/D,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAmB;QAC5D,SAAS,EAAE;QACX,cAAc,CAAC;IACjB;IAEA,CAAA,GAAA,sQAAA,CAAA,YAAS,AAAD;sCAAE;YACR,IAAI,QAAQ,OAAO,IAAI,CAAC,WAAW,OAAO,CAAC,QAAQ,CAAC,QAAQ,OAAO,GAAG;gBACpE;kDAAc,CAAA,OAAQ,CAAC;4BACrB,SAAS;mCAAI,KAAK,OAAO;gCAAE,QAAQ,OAAO;6BAAC;4BAC3C,cAAc,KAAK,OAAO,CAAC,MAAM;wBACnC,CAAC;;YACH;QACF;qCAAG;QAAC,QAAQ,OAAO;KAAC;IAEpB,MAAM,iBAAiB,CAAC;QACtB,MAAM,WAAW,cAAc,SAC3B,WAAW,YAAY,GAAG,IAC1B,WAAW,YAAY,GAAG;QAE9B,IAAI,YAAY,KAAK,WAAW,WAAW,OAAO,CAAC,MAAM,EAAE;YACzD,cAAc,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,cAAc;gBAAS,CAAC;YAC1D,WAAW,CAAA,OAAQ,CAAC;oBAClB,GAAG,IAAI;oBACP,SAAS,WAAW,OAAO,CAAC,SAAS;gBACvC,CAAC;QACH;IACF;IAEA,CAAA,GAAA,sQAAA,CAAA,YAAS,AAAD;sCAAE;YACR,MAAM;mEAAuB,CAAC;oBAC5B,IAAI,MAAM,IAAI,CAAC,IAAI,KAAK,WAAW;wBACjC;+EAAe,CAAA,OAAQ;uCAAI;oCAAM;wCAC/B,MAAM,MAAM,IAAI,CAAC,OAAO;wCACxB,WAAW,MAAM,IAAI,CAAC,SAAS;wCAC/B,SAAS,MAAM,IAAI,CAAC,OAAO;oCAC7B;iCAAE;;oBACJ;gBACF;;YAEA,OAAO,gBAAgB,CAAC,WAAW;YACnC;8CAAO,IAAM,OAAO,mBAAmB,CAAC,WAAW;;QACrD;qCAAG,EAAE;IAEL,MAAM,cAAc;QAClB,WAAW,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,WAAW;YAAM,CAAC;QACjD,gBAAgB;IAClB;IAEA,MAAM,qBAAqB,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAC1B,mGACA;QACE,sBAAsB;IACxB;IAGF,qBACE,sSAAC,kSAAA,CAAA,kBAAe;kBACb,2BACC,sSAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;YACT,SAAS;gBAAE,OAAM;gBAAQ,GAAG;gBAAQ,SAAS;YAAE;YAC/C,SAAS;gBACP,GAAG;gBACH,SAAS;YACX;YACA,MAAM;gBACJ,GAAG;gBACH,SAAS;gBACT,YAAY;oBACV,UAAU;oBACV,MAAM;gBACR;YACF;YACA,YAAY;gBACV,UAAU;gBACV,MAAM;YACR;YACA,WAAW;sBAEX,cAAA,sSAAC;gBAAI,WAAU;;kCACb,sSAAC;wBAAI,WAAU;;0CACb,sSAAC;gCAAI,WAAU;;kDAEb,sSAAC;wCACC,SAAS;wCACT,WAAU;kDAEV,cAAA,sSAAC,mRAAA,CAAA,IAAC;4CAAC,MAAM;;;;;;;;;;;kDAGX,sSAAC;wCAAI,WAAU;;0DACb,sSAAC;gDACC,UAAU,cAAc;gDACxB,OAAM;gDACN,SAAS,IAAM,aAAa;;;;;;0DAE9B,sSAAC;gDACC,UAAU,cAAc;gDACxB,OAAM;gDACN,SAAS,IAAM,aAAa;;;;;;0DAE9B,sSAAC;gDACC,UAAU,cAAc;gDACxB,OAAM;gDACN,SAAS,IAAM,aAAa;;;;;;;;;;;;;;;;;;0CAKlC,sSAAC;gCAAI,WAAU;;kDACb,sSAAC;wCACC,SAAS,IAAM,eAAe;wCAC9B,UAAU,WAAW,YAAY,IAAI;wCACrC,WAAU;kDAEV,cAAA,sSAAC,2SAAA,CAAA,cAAW;4CAAC,MAAM;;;;;;;;;;;kDAErB,sSAAC;wCACC,SAAS,IAAM,eAAe;wCAC9B,UAAU,WAAW,YAAY,IAAI,WAAW,OAAO,CAAC,MAAM,GAAG;wCACjE,WAAU;kDAEV,cAAA,sSAAC,6SAAA,CAAA,eAAY;4CAAC,MAAM;;;;;;;;;;;kDAEtB,sSAAC;wCACC,SAAS,IAAM,gBAAgB,CAAA,OAAQ,CAAC;wCACxC,WAAU;kDAET,6BAAe,sSAAC,uSAAA,CAAA,YAAS;4CAAC,MAAM;;;;;iEAAS,sSAAC,uSAAA,CAAA,YAAS;4CAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;kCAMjE,sSAAC;wBAAI,WAAU;kCACb,cAAA,sSAAC;4BAAI,WAAU;;gCACZ,cAAc,aAAa,QAAQ,IAAI,KAAK,wBAC3C,sSAAC;oCACC,QAAQ,qBAAqB,QAAQ,OAAO;oCAC5C,WAAU;oCACV,SAAQ;oCACR,OAAM;;;;;;gCAGT,cAAc,aAAa,QAAQ,IAAI,KAAK,wBAC3C,sSAAC,gIAAA,CAAA,aAAU;oCACT,SAAS,QAAQ,OAAO;oCACxB,UAAU,QAAQ,IAAI;oCACtB,MAAK;oCACL,QAAO;;;;;;gCAGV,cAAc,wBACb,sSAAC,gIAAA,CAAA,aAAU;oCACT,SAAS,QAAQ,OAAO;oCACxB,UAAU,QAAQ,IAAI;oCACtB,MAAK;oCACL,QAAO;oCACP,aAAa;;;;;;gCAGhB,cAAc,2BACb,sSAAC;oCAAc,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASvC;GA/KS;;QACyB,iIAAA,CAAA,aAAU;;;MADnC;AAiLF,MAAM,6BAAe,CAAA,GAAA,sQAAA,CAAA,OAAI,AAAD,EAAE,kBAAkB,CAAC,WAAW;IAC7D,OAAO,UAAU,SAAS,KAAK,UAAU,SAAS,IAChD,UAAU,UAAU,KAAK,UAAU,UAAU;AACjD;;AAEA,aAAa,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 6943, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/components/preview/chat.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport type { Attachment, Message } from \"ai\";\r\nimport { useChat } from \"ai/react\";\r\nimport { useState, useEffect } from \"react\";\r\nimport { useWindowSize } from \"usehooks-ts\";\r\n\r\nimport { <PERSON><PERSON><PERSON>eader } from \"@/components/preview/chat-header\";\r\n\r\nimport { MultimodalInput } from \"@/components/multimodal-input\";\r\nimport { Messages } from \"@/components/preview/messages\";\r\nimport { VisibilityType } from \"@/components/visibility-selector\";\r\nimport { FooterText } from \"../footer\";\r\nimport { PreviewPanel } from \"../preview-panel\";\r\nimport { usePreview } from \"@/lib/hooks/use-preview\";\r\n\r\nexport function Chat({\r\n  id,\r\n  initialMessages,\r\n  selectedModelId,\r\n  selectedVisibilityType,\r\n  isReadonly,\r\n}: {\r\n  id: string;\r\n  initialMessages: Array<Message>;\r\n  selectedModelId: string;\r\n  selectedVisibilityType: VisibilityType;\r\n  isReadonly: boolean;\r\n}) {\r\n  const [conversationId, setConversationId] = useState<string | undefined>(undefined);\r\n\r\n  const {\r\n    messages,\r\n    input,\r\n    setInput,\r\n    setMessages,\r\n    handleSubmit,\r\n    append,\r\n    isLoading,\r\n    stop,\r\n    reload,\r\n    data,\r\n    metadata,\r\n  } = useChat({\r\n    api: \"/api/dev-chat\",\r\n    id,\r\n    body: {\r\n      id,\r\n      modelId: selectedModelId,\r\n      conversationId: conversationId // conversation ID를 요청에 포함\r\n    },\r\n    initialMessages\r\n  });\r\n\r\n  useEffect(() => {\r\n    if (data && data.length > 0) {\r\n      const conversationData = data.find(item => \r\n        typeof item === 'object' && item !== null && 'type' in item && item.type === 'conversation-id'\r\n      );\r\n      if (conversationData && typeof conversationData === 'object' && 'id' in conversationData) {\r\n        if (conversationData.id !== null) {\r\n          setConversationId(conversationData.id as string);\r\n        }\r\n      }\r\n    }\r\n  }, [data]);\r\n\r\n  const { width: windowWidth = 1920, height: windowHeight = 1080 } =\r\n    useWindowSize();\r\n\r\n  const { preview } = usePreview();\r\n\r\n  const [attachments, setAttachments] = useState<Array<Attachment>>([]);\r\n\r\n  return (\r\n    <div className=\"flex flex-row min-w-0 h-dvh overflow-hidden bg-background\">\r\n      <div className=\"flex flex-col min-w-0 w-full overflow-hidden\">\r\n        <ChatHeader\r\n          chatId={id}\r\n          selectedModelId={selectedModelId}\r\n          isReadonly={isReadonly}\r\n        />\r\n\r\n        <Messages\r\n          chatId={id}\r\n          isLoading={isLoading}\r\n          votes={[]}\r\n          messages={messages}\r\n          setMessages={setMessages}\r\n          reload={reload}\r\n          isReadonly={isReadonly}\r\n          selectedModelId={selectedModelId}\r\n        />\r\n\r\n        <form className=\"flex mx-auto px-4 bg-background gap-2 w-full md:max-w-3xl\">\r\n          {!isReadonly && (\r\n            <MultimodalInput\r\n              chatId={id}\r\n              input={input}\r\n              setInput={setInput}\r\n              handleSubmit={handleSubmit}\r\n              isLoading={isLoading}\r\n              stop={stop}\r\n              attachments={attachments}\r\n              setAttachments={setAttachments}\r\n              messages={messages}\r\n              setMessages={setMessages}\r\n              append={append}\r\n              selectedModelId={selectedModelId}\r\n            />\r\n          )}\r\n        </form>\r\n        <FooterText className=\"py-2\" />\r\n      </div>\r\n\r\n      <PreviewPanel isReadonly={isReadonly} isVisible={preview?.isVisible} />\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AAEA;AAEA;AACA;AAEA;AACA;AACA;;;AAdA;;;;;;;;;;AAgBO,SAAS,KAAK,EACnB,EAAE,EACF,eAAe,EACf,eAAe,EACf,sBAAsB,EACtB,UAAU,EAOX;;IACC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAsB;IAEzE,MAAM,EACJ,QAAQ,EACR,KAAK,EACL,QAAQ,EACR,WAAW,EACX,YAAY,EACZ,MAAM,EACN,SAAS,EACT,IAAI,EACJ,MAAM,EACN,IAAI,EACJ,QAAQ,EACT,GAAG,CAAA,GAAA,0OAAA,CAAA,UAAO,AAAD,EAAE;QACV,KAAK;QACL;QACA,MAAM;YACJ;YACA,SAAS;YACT,gBAAgB,eAAe,0BAA0B;QAC3D;QACA;IACF;IAEA,CAAA,GAAA,sQAAA,CAAA,YAAS,AAAD;0BAAE;YACR,IAAI,QAAQ,KAAK,MAAM,GAAG,GAAG;gBAC3B,MAAM,mBAAmB,KAAK,IAAI;uDAAC,CAAA,OACjC,OAAO,SAAS,YAAY,SAAS,QAAQ,UAAU,QAAQ,KAAK,IAAI,KAAK;;gBAE/E,IAAI,oBAAoB,OAAO,qBAAqB,YAAY,QAAQ,kBAAkB;oBACxF,IAAI,iBAAiB,EAAE,KAAK,MAAM;wBAChC,kBAAkB,iBAAiB,EAAE;oBACvC;gBACF;YACF;QACF;yBAAG;QAAC;KAAK;IAET,MAAM,EAAE,OAAO,cAAc,IAAI,EAAE,QAAQ,eAAe,IAAI,EAAE,GAC9D,CAAA,GAAA,mOAAA,CAAA,gBAAa,AAAD;IAEd,MAAM,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,aAAU,AAAD;IAE7B,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAqB,EAAE;IAEpE,qBACE,sSAAC;QAAI,WAAU;;0BACb,sSAAC;gBAAI,WAAU;;kCACb,sSAAC,2IAAA,CAAA,aAAU;wBACT,QAAQ;wBACR,iBAAiB;wBACjB,YAAY;;;;;;kCAGd,sSAAC,qIAAA,CAAA,WAAQ;wBACP,QAAQ;wBACR,WAAW;wBACX,OAAO,EAAE;wBACT,UAAU;wBACV,aAAa;wBACb,QAAQ;wBACR,YAAY;wBACZ,iBAAiB;;;;;;kCAGnB,sSAAC;wBAAK,WAAU;kCACb,CAAC,4BACA,sSAAC,qIAAA,CAAA,kBAAe;4BACd,QAAQ;4BACR,OAAO;4BACP,UAAU;4BACV,cAAc;4BACd,WAAW;4BACX,MAAM;4BACN,aAAa;4BACb,gBAAgB;4BAChB,UAAU;4BACV,aAAa;4BACb,QAAQ;4BACR,iBAAiB;;;;;;;;;;;kCAIvB,sSAAC,wHAAA,CAAA,aAAU;wBAAC,WAAU;;;;;;;;;;;;0BAGxB,sSAAC,kIAAA,CAAA,eAAY;gBAAC,YAAY;gBAAY,WAAW,SAAS;;;;;;;;;;;;AAGhE;GAtGgB;;QA2BV,0OAAA,CAAA,UAAO;QAyBT,mOAAA,CAAA,gBAAa;QAEK,iIAAA,CAAA,aAAU;;;KAtDhB", "debugId": null}}, {"offset": {"line": 7101, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/lib/hooks/use-block.ts"], "sourcesContent": ["'use client';\r\n\r\nimport { UIBlock } from '@/components/block';\r\nimport { useCallback, useMemo } from 'react';\r\nimport useSWR from 'swr';\r\n\r\nexport const initialBlockData: UIBlock = {\r\n  documentId: 'init',\r\n  content: '',\r\n  kind: 'text',\r\n  title: '',\r\n  status: 'idle',\r\n  isVisible: false,\r\n  boundingBox: {\r\n    top: 0,\r\n    left: 0,\r\n    width: 0,\r\n    height: 0,\r\n  },\r\n};\r\n\r\n// Add type for selector function\r\ntype Selector<T> = (state: UIBlock) => T;\r\n\r\nexport function useBlockSelector<Selected>(selector: Selector<Selected>) {\r\n  const { data: localBlock } = useSWR<UIBlock>('block', null, {\r\n    fallbackData: initialBlockData,\r\n  });\r\n\r\n  const selectedValue = useMemo(() => {\r\n    if (!localBlock) return selector(initialBlockData);\r\n    return selector(localBlock);\r\n  }, [localBlock, selector]);\r\n\r\n  return selectedValue;\r\n}\r\n\r\nexport function useBlock() {\r\n  const { data: localBlock, mutate: setLocalBlock } = useSWR<UIBlock>(\r\n    'block',\r\n    null,\r\n    {\r\n      fallbackData: initialBlockData,\r\n    },\r\n  );\r\n\r\n  const block = useMemo(() => {\r\n    if (!localBlock) return initialBlockData;\r\n    return localBlock;\r\n  }, [localBlock]);\r\n\r\n  const setBlock = useCallback(\r\n    (updaterFn: UIBlock | ((currentBlock: UIBlock) => UIBlock)) => {\r\n      setLocalBlock((currentBlock) => {\r\n        const blockToUpdate = currentBlock || initialBlockData;\r\n\r\n        if (typeof updaterFn === 'function') {\r\n          return updaterFn(blockToUpdate);\r\n        }\r\n\r\n        return updaterFn;\r\n      });\r\n    },\r\n    [setLocalBlock],\r\n  );\r\n\r\n  return useMemo(() => ({ block, setBlock }), [block, setBlock]);\r\n}\r\n"], "names": [], "mappings": ";;;;;AAGA;AACA;AAAA;;AAJA;;;AAMO,MAAM,mBAA4B;IACvC,YAAY;IACZ,SAAS;IACT,MAAM;IACN,OAAO;IACP,QAAQ;IACR,WAAW;IACX,aAAa;QACX,KAAK;QACL,MAAM;QACN,OAAO;QACP,QAAQ;IACV;AACF;AAKO,SAAS,iBAA2B,QAA4B;;IACrE,MAAM,EAAE,MAAM,UAAU,EAAE,GAAG,CAAA,GAAA,sOAAA,CAAA,UAAM,AAAD,EAAW,SAAS,MAAM;QAC1D,cAAc;IAChB;IAEA,MAAM,gBAAgB,CAAA,GAAA,sQAAA,CAAA,UAAO,AAAD;mDAAE;YAC5B,IAAI,CAAC,YAAY,OAAO,SAAS;YACjC,OAAO,SAAS;QAClB;kDAAG;QAAC;QAAY;KAAS;IAEzB,OAAO;AACT;GAXgB;;QACe,sOAAA,CAAA,UAAM;;;AAY9B,SAAS;;IACd,MAAM,EAAE,MAAM,UAAU,EAAE,QAAQ,aAAa,EAAE,GAAG,CAAA,GAAA,sOAAA,CAAA,UAAM,AAAD,EACvD,SACA,MACA;QACE,cAAc;IAChB;IAGF,MAAM,QAAQ,CAAA,GAAA,sQAAA,CAAA,UAAO,AAAD;mCAAE;YACpB,IAAI,CAAC,YAAY,OAAO;YACxB,OAAO;QACT;kCAAG;QAAC;KAAW;IAEf,MAAM,WAAW,CAAA,GAAA,sQAAA,CAAA,cAAW,AAAD;0CACzB,CAAC;YACC;kDAAc,CAAC;oBACb,MAAM,gBAAgB,gBAAgB;oBAEtC,IAAI,OAAO,cAAc,YAAY;wBACnC,OAAO,UAAU;oBACnB;oBAEA,OAAO;gBACT;;QACF;yCACA;QAAC;KAAc;IAGjB,OAAO,CAAA,GAAA,sQAAA,CAAA,UAAO,AAAD;4BAAE,IAAM,CAAC;gBAAE;gBAAO;YAAS,CAAC;2BAAG;QAAC;QAAO;KAAS;AAC/D;IA9BgB;;QACsC,sOAAA,CAAA,UAAM", "debugId": null}}, {"offset": {"line": 7200, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/components/data-stream-handler.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useChat } from 'ai/react';\r\nimport { useEffect, useRef } from 'react';\r\nimport { BlockKind } from './block';\r\nimport { initialBlockData, useBlock } from '@/lib/hooks/use-block';\r\nimport { useUserMessageId } from '@/lib/hooks/use-user-message-id';\r\nimport { cx } from 'class-variance-authority';\r\n\r\ntype DataStreamDelta = {\r\n  type:\r\n    | 'text-delta'\r\n    | 'code-delta'\r\n    | 'title'\r\n    | 'id'\r\n    | 'suggestion'\r\n    | 'clear'\r\n    | 'finish'\r\n    | 'user-message-id'\r\n    | 'kind';\r\n  content: string;\r\n};\r\n\r\nexport function DataStreamHandler({ id }: { id: string }) {\r\n  const { data: dataStream } = useChat({ id, api: '/api/dev-chat' });\r\n  const { setUserMessageIdFromServer } = useUserMessageId();\r\n  const { setBlock } = useBlock();\r\n  const lastProcessedIndex = useRef(-1);\r\n\r\n  useEffect(() => {\r\n    if (!dataStream?.length) return;\r\n    const newDeltas = dataStream.slice(lastProcessedIndex.current + 1);\r\n    lastProcessedIndex.current = dataStream.length - 1;\r\n\r\n    (newDeltas as DataStreamDelta[]).forEach((delta: DataStreamDelta) => {\r\n      if (delta.type === 'user-message-id') {\r\n        setUserMessageIdFromServer(delta.content as string);\r\n        return;\r\n      }\r\n      setBlock((draftBlock) => {\r\n        if (!draftBlock) {\r\n          return { ...initialBlockData, status: 'streaming' };\r\n        }\r\n\r\n        switch (delta.type) {\r\n          case 'id':\r\n            return {\r\n              ...draftBlock,\r\n              documentId: delta.content as string,\r\n              status: 'streaming',\r\n            };\r\n\r\n          case 'title':\r\n            return {\r\n              ...draftBlock,\r\n              title: delta.content as string,\r\n              status: 'streaming',\r\n            };\r\n\r\n          case 'kind':\r\n            return {\r\n              ...draftBlock,\r\n              kind: delta.content as BlockKind,\r\n              status: 'streaming',\r\n            };\r\n\r\n          case 'text-delta':\r\n            return {\r\n              ...draftBlock,\r\n              content: draftBlock.content + (delta.content as string),\r\n              isVisible:\r\n                draftBlock.status === 'streaming' &&\r\n                draftBlock.content.length > 400 &&\r\n                draftBlock.content.length < 450\r\n                  ? true\r\n                  : draftBlock.isVisible,\r\n              status: 'streaming',\r\n            };\r\n\r\n          case 'code-delta':\r\n            return {\r\n              ...draftBlock,\r\n              content: draftBlock.content + delta.content as string,\r\n              isVisible:\r\n                draftBlock.status === 'streaming' &&\r\n                draftBlock.content.length > 300 &&\r\n                draftBlock.content.length < 310\r\n                  ? true\r\n                  : draftBlock.isVisible,\r\n              status: 'streaming',\r\n            };\r\n\r\n          case 'clear':\r\n            return {\r\n              ...draftBlock,\r\n              content: '',\r\n              status: 'streaming',\r\n            };\r\n\r\n          case 'finish':\r\n            return {\r\n              ...draftBlock,\r\n              status: 'idle',\r\n            };\r\n\r\n          default:\r\n            return draftBlock;\r\n        }\r\n      });\r\n    });\r\n  }, [dataStream, setBlock, setUserMessageIdFromServer]);\r\n\r\n  return null;\r\n}\r\n"], "names": [], "mappings": ";;;AAEA;AACA;AAEA;AACA;;AANA;;;;;AAuBO,SAAS,kBAAkB,EAAE,EAAE,EAAkB;;IACtD,MAAM,EAAE,MAAM,UAAU,EAAE,GAAG,CAAA,GAAA,0OAAA,CAAA,UAAO,AAAD,EAAE;QAAE;QAAI,KAAK;IAAgB;IAChE,MAAM,EAAE,0BAA0B,EAAE,GAAG,CAAA,GAAA,+IAAA,CAAA,mBAAgB,AAAD;IACtD,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,WAAQ,AAAD;IAC5B,MAAM,qBAAqB,CAAA,GAAA,sQAAA,CAAA,SAAM,AAAD,EAAE,CAAC;IAEnC,CAAA,GAAA,sQAAA,CAAA,YAAS,AAAD;uCAAE;YACR,IAAI,CAAC,YAAY,QAAQ;YACzB,MAAM,YAAY,WAAW,KAAK,CAAC,mBAAmB,OAAO,GAAG;YAChE,mBAAmB,OAAO,GAAG,WAAW,MAAM,GAAG;YAEhD,UAAgC,OAAO;+CAAC,CAAC;oBACxC,IAAI,MAAM,IAAI,KAAK,mBAAmB;wBACpC,2BAA2B,MAAM,OAAO;wBACxC;oBACF;oBACA;uDAAS,CAAC;4BACR,IAAI,CAAC,YAAY;gCACf,OAAO;oCAAE,GAAG,+HAAA,CAAA,mBAAgB;oCAAE,QAAQ;gCAAY;4BACpD;4BAEA,OAAQ,MAAM,IAAI;gCAChB,KAAK;oCACH,OAAO;wCACL,GAAG,UAAU;wCACb,YAAY,MAAM,OAAO;wCACzB,QAAQ;oCACV;gCAEF,KAAK;oCACH,OAAO;wCACL,GAAG,UAAU;wCACb,OAAO,MAAM,OAAO;wCACpB,QAAQ;oCACV;gCAEF,KAAK;oCACH,OAAO;wCACL,GAAG,UAAU;wCACb,MAAM,MAAM,OAAO;wCACnB,QAAQ;oCACV;gCAEF,KAAK;oCACH,OAAO;wCACL,GAAG,UAAU;wCACb,SAAS,WAAW,OAAO,GAAI,MAAM,OAAO;wCAC5C,WACE,WAAW,MAAM,KAAK,eACtB,WAAW,OAAO,CAAC,MAAM,GAAG,OAC5B,WAAW,OAAO,CAAC,MAAM,GAAG,MACxB,OACA,WAAW,SAAS;wCAC1B,QAAQ;oCACV;gCAEF,KAAK;oCACH,OAAO;wCACL,GAAG,UAAU;wCACb,SAAS,WAAW,OAAO,GAAG,MAAM,OAAO;wCAC3C,WACE,WAAW,MAAM,KAAK,eACtB,WAAW,OAAO,CAAC,MAAM,GAAG,OAC5B,WAAW,OAAO,CAAC,MAAM,GAAG,MACxB,OACA,WAAW,SAAS;wCAC1B,QAAQ;oCACV;gCAEF,KAAK;oCACH,OAAO;wCACL,GAAG,UAAU;wCACb,SAAS;wCACT,QAAQ;oCACV;gCAEF,KAAK;oCACH,OAAO;wCACL,GAAG,UAAU;wCACb,QAAQ;oCACV;gCAEF;oCACE,OAAO;4BACX;wBACF;;gBACF;;QACF;sCAAG;QAAC;QAAY;QAAU;KAA2B;IAErD,OAAO;AACT;GA1FgB;;QACe,0OAAA,CAAA,UAAO;QACG,+IAAA,CAAA,mBAAgB;QAClC,+HAAA,CAAA,WAAQ;;;KAHf", "debugId": null}}]}
import {
  streamText,
  createDataStreamResponse,
  generateObject,
  smoothStream,
  appendResponseMessages,
  UIMessage,
} from "ai";
import { evaluateAgentResult } from "./agents/evaluate";
import { agentConfigs, AgentName } from "./agents";
import { openai } from "@ai-sdk/openai";
import { saveMessages } from "@/lib/db/queries";
import { generateUUID } from "@/lib/utils";

// HIL 도구 완료 처리 함수
async function handleHILCompletion(
  updatedMessages: any[],
  chatId: string,
  dataStream: any
) {
  const hilTools = ['chooseOption', 'getUserInput', 'confirmWithCheckbox', 'getLocation'];

  // 최종 assistant 응답 찾기 (toolInvocations 배열이 있는 메시지)
  const finalResponseMessage = updatedMessages.find((msg: any) =>
    msg.role === 'assistant' && msg.id && typeof msg.content === 'string' && msg.toolInvocations
  );

  if (!finalResponseMessage || !finalResponseMessage.toolInvocations) return;

  // HIL 도구의 tool-call 찾기
  const hilToolCall = updatedMessages.find((msg: any) =>
    msg.role === 'assistant' && Array.isArray(msg.content) &&
    msg.content.some((content: any) =>
      content.type === 'tool-call' && hilTools.includes(content.toolName)
    )
  );

  if (!hilToolCall) return;

  const hilToolCallContent = hilToolCall.content.find((content: any) =>
    content.type === 'tool-call' && hilTools.includes(content.toolName)
  );

  // HIL 도구의 tool-result 찾기
  const hilToolResult = updatedMessages.find((msg: any) =>
    msg.role === 'tool' && Array.isArray(msg.content) &&
    msg.content.some((content: any) =>
      content.type === 'tool-result' && content.toolCallId === hilToolCallContent.toolCallId
    )
  );

  if (!hilToolResult) return;

  const hilToolResultContent = hilToolResult.content.find((content: any) =>
    content.type === 'tool-result' && content.toolCallId === hilToolCallContent.toolCallId
  );

  // HIL 도구를 toolInvocation 형태로 변환
  const hilToolInvocation = {
    state: "result",
    step: 0,
    args: hilToolCallContent.args,
    toolCallId: hilToolCallContent.toolCallId,
    toolName: hilToolCallContent.toolName,
    result: hilToolResultContent.result
  };

  // 기존 toolInvocations의 step을 1씩 증가
  const updatedToolInvocations = [
    hilToolInvocation,
    ...finalResponseMessage.toolInvocations.map((inv: any) => ({
      ...inv,
      step: inv.step + 1
    }))
  ];

  // parts 형태로 변환
  const integratedParts = [
    { type: "step-start" },
    ...updatedToolInvocations.flatMap((invocation: any, index: number) => [
      {
        type: "tool-invocation",
        toolInvocation: invocation
      },
      ...(index < updatedToolInvocations.length - 1 ? [{ type: "step-start" }] : [])
    ]),
    { type: "step-start" },
    { type: "text", text: finalResponseMessage.content }
  ];

  // 새로운 통합 메시지로 저장
  try {
    const newMessageId = generateUUID();

    const integratedMessage = {
      id: newMessageId,
      chatId,
      role: "assistant",
      content: "", // AI SDK 권장: content는 빈 문자열
      parts: integratedParts,
      attachments: [],
      createdAt: new Date(),
      deletedAt: null,
      enableReasoning: null,
      modelId: null,
    };

    await saveMessages({
      messages: [integratedMessage],
    });

    // 새로운 메시지 ID를 dataStream에 알림
    dataStream.writeMessageAnnotation({
      messageIdFromServer: newMessageId,
    });

    console.log(`HIL 도구 완료: 새 메시지 ${newMessageId} 저장됨 (HIL + ${finalResponseMessage.toolInvocations.length}개 도구)`);
  } catch (error) {
    console.error("HIL 메시지 저장 실패:", error);
  }
}

interface ExecuteWithRetryOptions {
  model: any;
  agentName: AgentName;
  messages: any[];
  stateMessage: any;
  intentMessage: string;
  dataStream: any;
  session: any;
  enable_smart_navigation: boolean;
  isNonGeonProvider: boolean;
  iteration?: number;
  maxIterations?: number;
  onEvaluationComplete?: (evaluationResult: any) => void;
  chatId?: string; // AI 응답을 DB에 저장하기 위한 chatId 추가
  modelId?: string; // 사용된 모델 ID 추가
  enableReasoning?: boolean; // 사용자의 추론 활성화 여부 추가
}

/**
 * Agent 실행 및 평가 결과 반환
 */
export async function executeAgentWithRetry({
  model,
  agentName,
  messages,
  stateMessage,
  intentMessage,
  dataStream,
  session,
  enable_smart_navigation,
  isNonGeonProvider,
  iteration = 0, // 0부터 시작하여 재시도 시마다 증가
  maxIterations = 3,
  onEvaluationComplete,
  chatId,
  modelId,
  enableReasoning,
}: ExecuteWithRetryOptions) {
  console.log(`=== Agent 실행 (${iteration + 1}/${maxIterations}) ===`);

  const agentConfig = agentConfigs[agentName];
  // 평가를 위한 정보 수집 변수들
  let allToolCalls: any[] = [];
  let agentResponse = "";
  let evaluationResult: any = null;

  const result = streamText({
    model,
    messages: [
      {
        role: "system",
        content: agentConfig.system,
      },
      stateMessage,
      ...messages,
    ],
    temperature: 0,
    tools: agentConfig.tools,
    toolCallStreaming: true,
    maxSteps: agentConfig.maxSteps || 5,
    experimental_transform: smoothStream({ chunking: "word" }),
    experimental_continueSteps: true,
    experimental_repairToolCall: async ({
      toolCall,
      tools,
      parameterSchema,
      error,
    }) => {
      if (error.message.includes("No such tool")) {
        console.error(`[TOOL_REPAIR] Tool not found, cannot repair`);
        return null;
      }

      const tool = tools[toolCall.toolName as keyof typeof tools];
      const { object: repairedArgs } = await generateObject({
        model: openai("gpt-4.1-mini", { structuredOutputs: true }),
        schema: tool.parameters,
        prompt: [
          `The model tried to call the tool "${toolCall.toolName}" with the following arguments:`,
          JSON.stringify(toolCall.args),
          `The tool accepts the following schema:`,
          JSON.stringify(parameterSchema(toolCall)),
          "Please fix the arguments.",
        ].join("\n"),
      });

      return { ...toolCall, args: JSON.stringify(repairedArgs) };
    },
    ...(isNonGeonProvider
      ? {}
      : {
          providerOptions: {
            geon: {
              metadata: {
                chat_template_kwargs: { enable_thinking: false },
              },
            },
          },
        }),
    onStepFinish: ({ toolCalls, text, toolResults }) => {
      // 평가를 위한 정보 수집
      if (toolCalls && toolCalls.length > 0) {
        allToolCalls.push(...toolCalls);
      }

      // 도구 결과도 수집 (다음 시도에서 참조할 수 있도록)
      if (toolResults && toolResults.length > 0) {
        // toolResults를 allToolCalls에 매핑하여 저장
        toolResults.forEach((result: any, index) => {
          const toolCallIndex =
            allToolCalls.length - toolResults.length + index;
          if (allToolCalls[toolCallIndex]) {
            const toolCall = allToolCalls[toolCallIndex];
            const tool = agentConfig.tools[toolCall.toolName];

            // 원본 결과는 항상 result 필드에 저장 (response.messages에 원본이 담기도록)
            (toolCall as any).result = result.result;

            // toToolResultContent가 있는 도구는 프루닝된 결과를 prunedResult에 추가 저장
            if (
              tool &&
              "experimental_toToolResultContent" in tool &&
              tool.experimental_toToolResultContent
            ) {
              // 프루닝된 결과를 별도 필드에 저장 (평가자용)
              (toolCall as any).prunedResult =
                tool.experimental_toToolResultContent(result.result);
            }
          }
        });
      }

      // 도구 호출 정보를 어노테이션으로 전송
      if (toolCalls && toolCalls.length > 0) {
        toolCalls.forEach((toolCall) => {
          dataStream.writeMessageAnnotation({
            type: "tool_call",
            toolName: toolCall.toolName,
            args: toolCall.args,
            enableSmartNavigation: enable_smart_navigation,
          });
        });
      }
    },
    onFinish: async ({ text, toolCalls, response }) => {
      // 최종 정보 수집
      if (text) {
        agentResponse += text;
      }
      if (toolCalls && toolCalls.length > 0) {
        allToolCalls.push(...toolCalls);
      }

      // 평가자 실행 시작 알림
      dataStream.writeMessageAnnotation({
        type: "evaluation_start",
        iteration: iteration + 1,
        maxIterations,
        message: "작업 결과를 평가하고 있습니다...",
      });

      // 평가자 실행
      try {
        evaluationResult = await evaluateAgentResult(
          intentMessage,
          agentResponse,
          allToolCalls
        );

        // 평가 완료 어노테이션
        const evaluationMessage = evaluationResult.isCompleted
          ? "작업이 성공적으로 완료되었습니다"
          : "작업이 미완료되어 계속 진행합니다";

        dataStream.writeMessageAnnotation({
          type: "evaluation_completed",
          iteration: iteration + 1,
          maxIterations,
          isCompleted: evaluationResult.isCompleted,
          shouldContinue: !evaluationResult.isCompleted,
          message: evaluationMessage,
          reason: evaluationResult.reason,
          improvementSuggestions: evaluationResult.improvementSuggestions,
        });

        // 콜백으로 평가 결과와 도구 호출 정보 전달
        if (onEvaluationComplete) {
          onEvaluationComplete({
            ...evaluationResult,
            toolCalls: allToolCalls,
            agentResponse: agentResponse,
          });
        }
      } catch (error) {
        console.error("평가자 실행 실패:", error);
        evaluationResult = {
          isCompleted: false,
          reason: "평가자 실행 중 오류가 발생하여 미완료로 처리",
          improvementSuggestions: ["평가자 오류로 인한 재시도가 필요합니다"],
        };
      }

      // AI SDK 4 공식 패턴: response.messages를 사용하여 tool calls와 results 포함하여 저장
      if (
        session.user?.id &&
        chatId &&
        response.messages &&
        response.messages.length > 0
      ) {
        try {
          // AI SDK의 appendResponseMessages를 사용하여 기존 메시지에 응답 메시지들을 추가
          const updatedMessages = appendResponseMessages({
            messages,
            responseMessages: response.messages,
          });
          console.log("[updatedMessages]:", JSON.stringify(updatedMessages));
          // HIL 도구 완료 감지 및 처리
          const hilTools = ['chooseOption', 'getUserInput', 'confirmWithCheckbox', 'getLocation'];

          // HIL 도구가 포함된 경우 감지
          const hasHILTool = updatedMessages.some((msg: any) =>
            msg.role === 'assistant' && Array.isArray(msg.content) &&
            msg.content.some((content: any) =>
              content.type === 'tool-call' && hilTools.includes(content.toolName)
            )
          );

          // 최종 assistant 응답이 있는지 확인 (모든 도구 호출이 완료된 후)
          const hasFinalResponse = updatedMessages.some((msg: any) =>
            msg.role === 'assistant' && msg.id && typeof msg.content === 'string'
          );

          // HIL 도구가 포함되고 최종 응답이 있으면 HIL 완료로 간주
          const isHILComplete = hasHILTool && hasFinalResponse;

          console.log(`HIL 완료 감지: hasHILTool=${hasHILTool}, hasFinalResponse=${hasFinalResponse}, isHILComplete=${isHILComplete}`);

          if (isHILComplete) {
            // HIL 도구 완료 시 기존 메시지 업데이트 로직
            await handleHILCompletion(updatedMessages, chatId, dataStream);
          } else {
            // 일반적인 새 메시지 저장 로직
            const newMessages = updatedMessages.slice(messages.length);

            const messagesToSave = newMessages.map((message) => {
              const messageId = generateUUID();

              if (message.role === "assistant") {
                dataStream.writeMessageAnnotation({
                  messageIdFromServer: messageId,
                });
              }

              // AI SDK 메시지 구조에 맞게 parts 처리
              let parts: any[] = [];
              if (message.parts && Array.isArray(message.parts)) {
                parts = message.parts;
              } else if (Array.isArray(message.content)) {
                parts = message.content;
              } else if (typeof message.content === "string") {
                parts = [{ type: "text", text: message.content }];
              }

              return {
                id: messageId,
                chatId,
                role: message.role,
                content: "", // AI SDK 권장: content는 빈 문자열
                parts, // 올바르게 처리된 parts 배열
                attachments: message.experimental_attachments || [], // AI SDK attachments
                createdAt: new Date(),
                deletedAt: null, // 새 메시지는 삭제되지 않은 상태
                enableReasoning: enableReasoning || null, // 사용자의 추론 활성화 여부 기록
                modelId: message.role === "assistant" ? (modelId || null) : null, // assistant 메시지에만 모델 ID 저장
              };
            });

            if (messagesToSave.length > 0) {
              await saveMessages({
                messages: messagesToSave,
              });
            }
          }
        } catch (error) {
          console.error("메시지 저장 실패:", error);
        }
      }

      // 작업 완료 메시지 전송
      if (session.user?.id) {
        try {
          dataStream.writeMessageAnnotation({
            type: "agent_completed",
            agent: agentName,
            message: evaluationResult?.isCompleted
              ? "작업이 완료되었습니다."
              : "작업이 진행 중입니다.",
            finalEvaluation: evaluationResult,
            iteration: iteration + 1,
            maxIterations,
          });
        } catch (error) {
          console.error("메시지 저장 실패:", error);
        }
      }
    },
  });

  return result;
}

/**
 * 평가 결과를 포함한 Agent 실행 래퍼
 */
export async function executeAgentWithEvaluation({
  model,
  agentName,
  messages,
  stateMessage,
  intentMessage,
  dataStream,
  session,
  enable_smart_navigation,
  isNonGeonProvider,
  iteration = 0,
  maxIterations = 3,
  chatId,
}: ExecuteWithRetryOptions): Promise<{
  streamResult: any;
  evaluationResult: any;
}> {
  let evaluationResult: any = null;

  // Agent 실행
  const result = await executeAgentWithRetry({
    model,
    agentName,
    messages,
    stateMessage,
    intentMessage,
    dataStream,
    session,
    enable_smart_navigation,
    isNonGeonProvider,
    iteration,
    maxIterations,
    chatId,
  });

  // 스트림을 데이터스트림에 병합하고 완료 대기
  await new Promise<void>((resolve) => {
    result.mergeIntoDataStream(dataStream, {
      sendReasoning: true,
    });
  });

  return {
    streamResult: result,
    evaluationResult: evaluationResult,
  };
}

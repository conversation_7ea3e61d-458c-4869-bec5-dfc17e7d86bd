{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_c50ec21a._.js", "server/edge/chunks/9c5b9_@auth_core_16e273d6._.js", "server/edge/chunks/96a70_jose_dist_webapi_68ebb33a._.js", "server/edge/chunks/node_modules__pnpm_60fd9d8f._.js", "server/edge/chunks/[root-of-the-server]__b9a0598e._.js", "server/edge/chunks/edge-wrapper_22e6e254.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!api/|_next/|_static/|_vercel|[\\w-]+\\.\\w+).*){(\\\\.json)}?", "originalSource": "/((?!api/|_next/|_static/|_vercel|[\\w-]+\\.\\w+).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "RzLJEFYUW/CFqaruOjruBpAlZrrsX4lJc3SEBXoIqAE=", "__NEXT_PREVIEW_MODE_ID": "4b7329871596839f05d5c2586a8b67aa", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "866f5d83c9d98c08ff0205446905a135f4c8d93269731c7df127b9a780f24e3e", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "75f2607102d9dc87b6936c23feae9c24d9c9f092364f9016f4bf70e7a5c1e98b"}}}, "instrumentation": null, "functions": {}}
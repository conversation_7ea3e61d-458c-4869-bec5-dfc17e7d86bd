{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/providers/basemap-provider.tsx"], "sourcesContent": ["\"use client\";\n\nimport React, { createContext, useContext, useState, useEffect, useCallback } from \"react\";\nimport { UseMapReturn } from \"@geon-map/odf\";\n\ntype BasemapId = 'eMapBasic' | 'eMapAIR' | 'eMapColor' | 'eMapWhite';\n\ninterface BasemapContextType {\n  currentBasemap: BasemapId;\n  setCurrentBasemap: (basemap: BasemapId) => void;\n  changeBasemap: (basemap: BasemapId) => void;\n}\n\nconst BasemapContext = createContext<BasemapContextType | null>(null);\n\ninterface BasemapProviderProps {\n  children: React.ReactNode;\n  mapState?: UseMapReturn;\n}\n\nexport function BasemapProvider({ children, mapState }: BasemapProviderProps) {\n  const [currentBasemap, setCurrentBasemap] = useState<BasemapId>('eMapBasic');\n\n  // ODF 맵이 초기화되면 배경지도 변경 콜백 설정\n  useEffect(() => {\n    if (!mapState?.map) return;\n\n    const map = mapState.map;\n    \n    // basemapControl이 있는지 확인하고 콜백 설정\n    if (map.basemapControl && typeof map.basemapControl.setSwitchBaseLayerCallback === 'function') {\n      map.basemapControl.setSwitchBaseLayerCallback((beforeLayer: string, afterLayer: string) => {\n        setCurrentBasemap(afterLayer as BasemapId);\n      });\n    }\n\n    // 초기 배경지도 상태 설정 (가능한 경우)\n    if (map.basemapControl && typeof map.basemapControl.getPresentBaseGrpKey === 'function') {\n      try {\n        const currentKey = map.basemapControl.getPresentBaseGrpKey();\n        if (currentKey && ['eMapBasic', 'eMapAIR', 'eMapColor', 'eMapWhite'].includes(currentKey)) {\n          setCurrentBasemap(currentKey as BasemapId);\n        }\n      } catch (error) {\n        console.warn('Failed to get current basemap:', error);\n      }\n    }\n  }, [mapState?.map]);\n\n  const changeBasemap = useCallback((basemap: BasemapId) => {\n    if (!mapState?.view?.setBasemap) {\n      console.error(\"map.setBasemap is not available or map not ready.\");\n      return;\n    }\n\n    // ODF 맵에서 배경지도 변경 (콜백에서 상태 업데이트됨)\n    mapState.view.setBasemap(basemap);\n    setCurrentBasemap(basemap);\n  }, [mapState]);\n\n  const contextValue: BasemapContextType = {\n    currentBasemap,\n    setCurrentBasemap,\n    changeBasemap,\n  };\n\n  return (\n    <BasemapContext.Provider value={contextValue}>\n      {children}\n    </BasemapContext.Provider>\n  );\n}\n\nexport function useBasemap(): BasemapContextType {\n  const context = useContext(BasemapContext);\n  if (!context) {\n    throw new Error('useBasemap must be used within a BasemapProvider');\n  }\n  return context;\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAFA;;;AAaA,MAAM,+BAAiB,CAAA,GAAA,8SAAA,CAAA,gBAAa,AAAD,EAA6B;AAOzD,SAAS,gBAAgB,EAAE,QAAQ,EAAE,QAAQ,EAAwB;IAC1E,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,8SAAA,CAAA,WAAQ,AAAD,EAAa;IAEhE,6BAA6B;IAC7B,CAAA,GAAA,8SAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,UAAU,KAAK;QAEpB,MAAM,MAAM,SAAS,GAAG;QAExB,iCAAiC;QACjC,IAAI,IAAI,cAAc,IAAI,OAAO,IAAI,cAAc,CAAC,0BAA0B,KAAK,YAAY;YAC7F,IAAI,cAAc,CAAC,0BAA0B,CAAC,CAAC,aAAqB;gBAClE,kBAAkB;YACpB;QACF;QAEA,yBAAyB;QACzB,IAAI,IAAI,cAAc,IAAI,OAAO,IAAI,cAAc,CAAC,oBAAoB,KAAK,YAAY;YACvF,IAAI;gBACF,MAAM,aAAa,IAAI,cAAc,CAAC,oBAAoB;gBAC1D,IAAI,cAAc;oBAAC;oBAAa;oBAAW;oBAAa;iBAAY,CAAC,QAAQ,CAAC,aAAa;oBACzF,kBAAkB;gBACpB;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,IAAI,CAAC,kCAAkC;YACjD;QACF;IACF,GAAG;QAAC,UAAU;KAAI;IAElB,MAAM,gBAAgB,CAAA,GAAA,8SAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACjC,IAAI,CAAC,UAAU,MAAM,YAAY;YAC/B,QAAQ,KAAK,CAAC;YACd;QACF;QAEA,kCAAkC;QAClC,SAAS,IAAI,CAAC,UAAU,CAAC;QACzB,kBAAkB;IACpB,GAAG;QAAC;KAAS;IAEb,MAAM,eAAmC;QACvC;QACA;QACA;IACF;IAEA,qBACE,uVAAC,eAAe,QAAQ;QAAC,OAAO;kBAC7B;;;;;;AAGP;AAEO,SAAS;IACd,MAAM,UAAU,CAAA,GAAA,8SAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 86, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/providers/tool-invocation-provider.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, {\r\n  createContext,\r\n  useContext,\r\n  useEffect,\r\n  useMemo,\r\n  useReducer,\r\n  useRef,\r\n  useState,\r\n  useCallback,\r\n} from \"react\";\r\nimport type { Message, ToolInvocation } from \"ai\";\r\nimport {\r\n  ManagedLayerProps,\r\n  LayerAction,\r\n  type LayerManagerContext as ILayerManagerContext,\r\n  layerTransformers\r\n} from \"@/types/layer-manager\";\r\nimport { UseMapReturn } from \"@geon-map/odf\";\r\nimport { WMSLayerStyle, createDefaultPointStyle, createDefaultLineStyle, createDefaultPolygonStyle } from \"@/types/layer-style\";\r\nimport { highlightPointOnMap } from \"@/lib/map-utils\";\r\nimport { toast } from \"sonner\";\r\nimport { useBasemap } from \"./basemap-provider\";\r\n\r\n\r\n\r\n// toolName => array of invocation results\r\nexport type ToolResultMap = Record<string, ToolInvocation[]>;\r\n\r\n// 단순한 스타일 속성을 WMS 스타일 구조로 변환하는 함수\r\nconst convertSimpleStyleToWMS = (\r\n  simpleStyle: any,\r\n  layerInfo?: { geometryType?: string; currentStyle?: any }\r\n): WMSLayerStyle => {\r\n  let baseStyle: WMSLayerStyle;\r\n\r\n  // 기존 스타일이 있다면 그것을 베이스로 사용\r\n  if (layerInfo?.currentStyle && layerInfo.currentStyle.rules) {\r\n    baseStyle = { ...layerInfo.currentStyle };\r\n  } else {\r\n    // 지오메트리 타입에 따라 적절한 기본 스타일 선택\r\n    const geometryType = layerInfo?.geometryType || 'point';\r\n    switch (geometryType) {\r\n      case 'polygon':\r\n      case '3':\r\n        baseStyle = createDefaultPolygonStyle();\r\n        break;\r\n      case 'line':\r\n      case '2':\r\n        baseStyle = createDefaultLineStyle();\r\n        break;\r\n      case 'point':\r\n      case '1':\r\n      default:\r\n        baseStyle = createDefaultPointStyle();\r\n        break;\r\n    }\r\n  }\r\n\r\n  // 기존 스타일을 복사하여 수정\r\n  const updatedStyle = JSON.parse(JSON.stringify(baseStyle));\r\n  const rule = updatedStyle.rules[0];\r\n  const symbolizer = rule.symbolizers[0] as any;\r\n\r\n  // 단순 스타일 속성을 WMS 구조에 매핑\r\n  if (simpleStyle.color) {\r\n    symbolizer.color = simpleStyle.color;\r\n  }\r\n  if (simpleStyle.fillOpacity !== undefined) {\r\n    symbolizer.fillOpacity = simpleStyle.fillOpacity;\r\n  }\r\n  if (simpleStyle.strokeColor) {\r\n    if (symbolizer.kind === 'Fill') {\r\n      symbolizer.outlineColor = simpleStyle.strokeColor;\r\n    } else {\r\n      symbolizer.strokeColor = simpleStyle.strokeColor;\r\n    }\r\n  }\r\n  if (simpleStyle.strokeWidth !== undefined) {\r\n    if (symbolizer.kind === 'Fill') {\r\n      symbolizer.outlineWidth = simpleStyle.strokeWidth;\r\n    } else {\r\n      symbolizer.strokeWidth = simpleStyle.strokeWidth;\r\n    }\r\n  }\r\n  if (simpleStyle.radius !== undefined && symbolizer.kind === 'Mark') {\r\n    symbolizer.radius = simpleStyle.radius;\r\n  }\r\n  if (simpleStyle.width !== undefined && symbolizer.kind === 'Line') {\r\n    symbolizer.width = simpleStyle.width;\r\n  }\r\n  if (simpleStyle.symbol && symbolizer.kind === 'Mark') {\r\n    symbolizer.wellKnownName = simpleStyle.symbol;\r\n  }\r\n\r\n  return updatedStyle;\r\n};\r\n\r\n// 현재 위치 정보 타입\r\nexport interface LocationInfo {\r\n  latitude: number;\r\n  longitude: number;\r\n  accuracy?: number;\r\n  timestamp?: number;\r\n  projectedCoord: [number, number];\r\n}\r\n\r\n// 출발지/목적지 정보 타입\r\nexport interface RoutePointInfo {\r\n  address: {\r\n    roadAddr: string;\r\n    jibunAddr?: string;\r\n    buildName?: string;\r\n    buildLo: string;\r\n    buildLa: string;\r\n  };\r\n  projectedCoord: [number, number];\r\n  toolCallId: string;\r\n}\r\n\r\n// 위치 컨텍스트\r\ninterface LocationContextType {\r\n  currentLocation: LocationInfo | null;\r\n  setCurrentLocation: (location: LocationInfo | null) => void;\r\n  originPoint: RoutePointInfo | null;\r\n  setOriginPoint: (origin: RoutePointInfo | null) => void;\r\n  destinationPoint: RoutePointInfo | null;\r\n  setDestinationPoint: (destination: RoutePointInfo | null) => void;\r\n}\r\n\r\nconst ToolContext = createContext<ToolResultMap>({});\r\nconst LayerManagerContext = createContext<ILayerManagerContext | null>(null);\r\nconst LocationContext = createContext<LocationContextType>({\r\n  currentLocation: null,\r\n  setCurrentLocation: () => { },\r\n  originPoint: null,\r\n  setOriginPoint: () => { },\r\n  destinationPoint: null,\r\n  setDestinationPoint: () => { },\r\n});\r\n\r\n// 레이어 상태 관리 리듀서\r\nfunction layerReducer(state: ManagedLayerProps[], action: LayerAction): ManagedLayerProps[] {\r\n  switch (action.type) {\r\n    case 'ADD_LAYER':\r\n      // 중복 방지: 같은 ID가 있으면 업데이트\r\n      const existingIndex = state.findIndex(layer => layer.id === action.payload.id);\r\n      if (existingIndex >= 0) {\r\n        const newState = [...state];\r\n        newState[existingIndex] = { ...newState[existingIndex], ...action.payload };\r\n        return newState;\r\n      }\r\n\r\n      // 새 레이어는 가장 높은 zIndex를 가져야 함 (가장 위에 표시)\r\n      const maxZIndex = state.length > 0 ? Math.max(...state.map(layer => layer.zIndex || 0)) : 0;\r\n      const newLayer = {\r\n        ...action.payload,\r\n        zIndex: maxZIndex + 1\r\n      };\r\n\r\n      return [...state, newLayer];\r\n\r\n    case 'UPDATE_LAYER':\r\n      return state.map(layer =>\r\n        layer.id === action.payload.id\r\n          ? { ...layer, ...action.payload.updates }\r\n          : layer\r\n      );\r\n\r\n    case 'REMOVE_LAYER':\r\n      return state.filter(layer => layer.id !== action.payload);\r\n\r\n    case 'TOGGLE_VISIBILITY':\r\n      return state.map(layer =>\r\n        layer.id === action.payload\r\n          ? { ...layer, visible: !layer.visible, userModified: true }\r\n          : layer\r\n      );\r\n\r\n    case 'UPDATE_FILTER':\r\n      return state.map(layer =>\r\n        layer.id === action.payload.id\r\n          ? { ...layer, filter: action.payload.filter }\r\n          : layer\r\n      );\r\n\r\n    case 'UPDATE_STYLE':\r\n      return state.map(layer =>\r\n        layer.id === action.payload.id\r\n          ? { ...layer, style: action.payload.style }\r\n          : layer\r\n      );\r\n\r\n    case 'UPDATE_Z_INDEX':\r\n      return state.map(layer =>\r\n        layer.id === action.payload.id\r\n          ? { ...layer, zIndex: action.payload.zIndex }\r\n          : layer\r\n      );\r\n\r\n    case 'REORDER_LAYERS':\r\n      const { layerIds } = action.payload;\r\n\r\n      // 기존 state를 복사하여 객체 참조를 최대한 유지\r\n      return state.map(layer => {\r\n        const layerIndex = layerIds.indexOf(layer.id);\r\n        if (layerIndex >= 0) {\r\n          // DnD 인덱스와 zIndex는 반대 관계\r\n          // DnD에서 index 0 = 가장 아래 = 가장 낮은 zIndex\r\n          // DnD에서 마지막 index = 가장 위 = 가장 높은 zIndex\r\n          const newZIndex = layerIds.length - layerIndex;\r\n\r\n          // zIndex만 변경되었을 때만 새 객체 생성\r\n          if (layer.zIndex !== newZIndex) {\r\n            return {\r\n              ...layer,\r\n              zIndex: newZIndex\r\n            };\r\n          }\r\n          return layer; // zIndex가 같으면 기존 객체 그대로 반환\r\n        }\r\n\r\n        // 순서에 포함되지 않은 레이어는 zIndex 0으로 설정\r\n        if (layer.zIndex !== 0) {\r\n          return {\r\n            ...layer,\r\n            zIndex: 0\r\n          };\r\n        }\r\n        return layer; // 변경사항이 없으면 기존 객체 그대로 반환\r\n      });\r\n\r\n    case 'SET_LAYERS':\r\n      return action.payload;\r\n\r\n    default:\r\n      return state;\r\n  }\r\n}\r\n\r\nexport function ToolInvocationProvider({\r\n  messages,\r\n  children,\r\n  enableSmartNavigation = true,\r\n  mapState,\r\n}: {\r\n  messages: Message[];\r\n  children: React.ReactNode;\r\n  enableSmartNavigation?: boolean;\r\n  mapState?: UseMapReturn;\r\n}) {\r\n  const [resultMap, setResultMap] = useState<ToolResultMap>({});\r\n  const [layers, dispatch] = useReducer(layerReducer, []);\r\n  const [currentLocation, setCurrentLocation] = useState<LocationInfo | null>(null);\r\n  const [originPoint, setOriginPoint] = useState<RoutePointInfo | null>(null);\r\n  const [destinationPoint, setDestinationPoint] = useState<RoutePointInfo | null>(null);\r\n  const processedToolCallsRef = useRef<Set<string>>(new Set());\r\n  const processedSmartNavigationRef = useRef<Set<string>>(new Set()); // 스마트 네비게이션 처리 추적\r\n  const previousMessagesRef = useRef<Message[]>([]); // 이전 메시지 상태 추적\r\n\r\n  // 배경지도 상태 관리를 위한 useBasemap 훅\r\n  const { setCurrentBasemap } = useBasemap();\r\n\r\n  // 지연된 배치 처리를 위한 상태\r\n  const pendingAddressesRef = useRef<Array<{\r\n    address: any;\r\n    toolCallId: string;\r\n  }>>([]);\r\n  const addressBatchTimeoutRef = useRef<NodeJS.Timeout | null>(null);\r\n  const BATCH_DELAY = 1000; // 2초 지연\r\n\r\n  // 배치 처리된 주소들을 지도에 표시하는 함수\r\n  const processBatchedAddresses = useCallback(() => {\r\n    if (!enableSmartNavigation || !mapState || pendingAddressesRef.current.length === 0) {\r\n      return;\r\n    }\r\n\r\n    try {\r\n      const addresses = pendingAddressesRef.current;\r\n\r\n      if (addresses.length === 1) {\r\n        // 단일 주소인 경우 개선된 함수 사용\r\n        const { address } = addresses[0];\r\n        const success = highlightPointOnMap(\r\n          mapState,\r\n          parseFloat(address.buildLo),\r\n          parseFloat(address.buildLa),\r\n          address.buildGeom || address.geom,\r\n          13,\r\n          {\r\n            clearPrevious: true,\r\n            useZIndexUp: true,\r\n            fitPadding: 1000\r\n          }\r\n        );\r\n        if (success) {\r\n          toast.success(`${address.roadAddr}로 자동 이동했습니다`);\r\n        }\r\n      } else {\r\n        // 여러 주소인 경우 모든 주소를 포함하는 영역으로 이동\r\n        const coordinates = addresses.map(({ address }) => ({\r\n          lng: parseFloat(address.buildLo),\r\n          lat: parseFloat(address.buildLa),\r\n          geometry: address.buildGeom || address.geom\r\n        }));\r\n\r\n        // 모든 포인트를 하이라이트 (개선된 함수 사용)\r\n        coordinates.forEach((coord, index) => {\r\n          highlightPointOnMap(\r\n            mapState,\r\n            coord.lng,\r\n            coord.lat,\r\n            coord.geometry,\r\n            16,\r\n            {\r\n              clearPrevious: index === 0, // 첫 번째만 이전 하이라이트 제거\r\n              useZIndexUp: true, // 하이라이트 레이어를 최상단으로\r\n              fitPadding: 1000\r\n            }\r\n          );\r\n        });\r\n\r\n        toast.success(`${addresses.length}개 위치를 지도에 표시했습니다`);\r\n      }\r\n\r\n      // 처리 완료 후 초기화\r\n      pendingAddressesRef.current = [];\r\n    } catch (error) {\r\n      console.error('배치 주소 처리 중 오류:', error);\r\n      pendingAddressesRef.current = [];\r\n    }\r\n  }, [enableSmartNavigation, mapState]);\r\n\r\n  // 스마트 지도 제어 함수들\r\n  const handleSmartNavigation = useCallback((toolName: string, result: any, toolCallId: string) => {\r\n    if (!enableSmartNavigation || !mapState) return;\r\n\r\n    const smartNavKey = `${toolName}-${toolCallId}`;\r\n    if (processedSmartNavigationRef.current.has(smartNavKey)) {\r\n      return; // 이미 처리됨\r\n    }\r\n\r\n    try {\r\n      switch (toolName) {\r\n        case \"searchAddress\":\r\n          if (result.result?.jusoList?.length > 0) {\r\n            const firstAddress = result.result.jusoList[0];\r\n            if (firstAddress.buildLo && firstAddress.buildLa) {\r\n              // 주소를 배치 처리 큐에 추가\r\n              pendingAddressesRef.current.push({\r\n                address: firstAddress,\r\n                toolCallId\r\n              });\r\n\r\n              // 기존 타이머 취소\r\n              if (addressBatchTimeoutRef.current) {\r\n                clearTimeout(addressBatchTimeoutRef.current);\r\n              }\r\n\r\n              // 새 타이머 설정\r\n              addressBatchTimeoutRef.current = setTimeout(() => {\r\n                processBatchedAddresses();\r\n                addressBatchTimeoutRef.current = null;\r\n              }, BATCH_DELAY);\r\n            }\r\n          }\r\n          break;\r\n\r\n        case \"searchOrigin\":\r\n          if (result.result?.jusoList?.length > 0) {\r\n            const firstAddress = result.result.jusoList[0];\r\n            if (firstAddress.buildLo && firstAddress.buildLa && mapState?.map) {\r\n              const projection = mapState.map.getProjection();\r\n              const projectedCoord = projection.project([\r\n                parseFloat(firstAddress.buildLo),\r\n                parseFloat(firstAddress.buildLa)\r\n              ], \"4326\");\r\n\r\n              // 출발지 정보 설정\r\n              setOriginPoint({\r\n                address: firstAddress,\r\n                projectedCoord: projectedCoord as [number, number],\r\n                toolCallId\r\n              });\r\n\r\n            }\r\n          }\r\n          break;\r\n\r\n        case \"searchDestination\":\r\n          if (result.result?.jusoList?.length > 0) {\r\n            const firstAddress = result.result.jusoList[0];\r\n            if (firstAddress.buildLo && firstAddress.buildLa && mapState?.map) {\r\n              const projection = mapState.map.getProjection();\r\n              const projectedCoord = projection.project([\r\n                parseFloat(firstAddress.buildLo),\r\n                parseFloat(firstAddress.buildLa)\r\n              ], \"4326\");\r\n\r\n              // 목적지 정보 설정\r\n              setDestinationPoint({\r\n                address: firstAddress,\r\n                projectedCoord: projectedCoord as [number, number],\r\n                toolCallId\r\n              });\r\n\r\n            }\r\n          }\r\n          break;\r\n\r\n        case \"searchDirections\":\r\n          if (result.routes?.length > 0 && result.routes[0].result_code === 0) {\r\n            // 새로운 타입에서는 레이어 변환기를 통해 자동으로 지도에 표시됨\r\n            // 별도의 showRouteOnMap 호출 불필요\r\n            toast.success(\"경로를 지도에 자동으로 표시했습니다\");\r\n          }\r\n          break;\r\n\r\n        case \"getLocation\":\r\n          if (!mapState?.map) return;\r\n          const prj = mapState.map.getProjection();\r\n          if (result.latitude && result.longitude) {\r\n            const projectedCoord = prj.project([result.longitude, result.latitude], \"4326\");\r\n\r\n            // 위치 정보를 상태에 저장\r\n            const locationInfo: LocationInfo = {\r\n              latitude: result.latitude,\r\n              longitude: result.longitude,\r\n              accuracy: result.accuracy,\r\n              timestamp: result.timestamp,\r\n              projectedCoord: projectedCoord as [number, number]\r\n            };\r\n            setCurrentLocation(locationInfo);\r\n\r\n            mapState.map.setZoom(14);\r\n            mapState.map.setCenter(projectedCoord);\r\n          }\r\n          break;\r\n\r\n        case \"getLayer\":\r\n          // 서울 건물통합정보 레이어인 경우 서울 중심 좌표로 지도 이동 (EPSG:5186 표준 서울 중심좌표)\r\n          if (result.lyrId === \"LR0000004299\" || result.name === \"GIS건물통합정보_서울\") {\r\n            if (!mapState?.map) return;\r\n\r\n            const center = new odf.Coordinate(955156.7761, 1951925.0984);\r\n            const newZoom = 11;\r\n\r\n            mapState.map.setCenter(center);\r\n            mapState.map.setZoom(newZoom);\r\n            toast.success(`${result.name || result.id} 레이어를 지도에 추가했습니다`);\r\n          } else if (result.bbox && Array.isArray(result.bbox) && result.bbox.length >= 4) {\r\n            const [minX, minY, maxX, maxY] = result.bbox;\r\n            const centerX = (minX + maxX) / 2;\r\n            const centerY = (minY + maxY) / 2;\r\n            highlightPointOnMap(mapState, centerX, centerY, undefined, 12);\r\n            toast.success(`${result.name || result.id} 레이어 영역으로 지도를 이동했습니다`);\r\n          } else {\r\n            toast.success(`${result.name || result.id} 레이어를 지도에 추가했습니다`);\r\n          }\r\n          break;\r\n\r\n        case \"changeBasemap\":\r\n          if (result.basemap && mapState?.view?.setBasemap) {\r\n            // 1. ODF 맵에서 배경지도 변경\r\n            mapState.view.setBasemap(result.basemap);\r\n            // 2. 전역 상태 업데이트 (ODF 콜백에서 자동으로 처리되지만 보험용)\r\n            setCurrentBasemap(result.basemap);\r\n            toast.success(`배경지도가 변경되었습니다`);\r\n          }\r\n          break;\r\n\r\n        case \"setMapCenter\":\r\n          if (result.center && mapState?.view?.setCenter) {\r\n            const [longitude, latitude] = result.center;\r\n            mapState.view.setCenter([longitude, latitude]);\r\n            toast.success(result.message || `지도 중심점이 이동되었습니다`);\r\n          }\r\n          break;\r\n\r\n        case \"setMapZoom\":\r\n          if (result.zoom !== undefined && mapState?.view?.setZoom) {\r\n            if (result.zoomType === \"relative\") {\r\n              // 상대적 확대/축소인 경우 현재 줌 레벨 기준으로 계산\r\n              const currentZoom = mapState.view.getZoom();\r\n              const zoomChange = result.zoomDirection === \"in\" ? result.zoom : -result.zoom;\r\n              const newZoom = Math.max(1, Math.min(20, currentZoom + zoomChange));\r\n              mapState.view.setZoom(newZoom);\r\n            } else {\r\n              // 절대적 확대/축소\r\n              mapState.view.setZoom(result.zoom);\r\n            }\r\n            toast.success(result.message || `지도 확대/축소 레벨이 변경되었습니다`);\r\n          }\r\n          break;\r\n\r\n        case \"moveMapByDirection\":\r\n          if (result.deltaX !== undefined && result.deltaY !== undefined && mapState?.map?.setCenter && mapState?.map?.getCenter) {\r\n            const currentCenter = mapState.map.getCenter();\r\n            // EPSG:5186 좌표계에서는 X, Y 좌표를 직접 사용\r\n            const newCenter: [number, number] = [\r\n              currentCenter[0] + result.deltaX, // X축 (동서 방향)\r\n              currentCenter[1] + result.deltaY  // Y축 (남북 방향)\r\n            ];\r\n            mapState.map.setCenter(newCenter);\r\n            toast.success(result.message || `지도가 이동되었습니다`);\r\n          }\r\n          break;\r\n\r\n        default:\r\n          // 기타 도구들은 특별한 지도 제어가 필요하지 않음\r\n          break;\r\n      }\r\n\r\n      processedSmartNavigationRef.current.add(smartNavKey);\r\n    } catch (error) {\r\n      console.error(`Error handling smart navigation for ${toolName}:`, error);\r\n    }\r\n  }, [enableSmartNavigation, mapState]);\r\n\r\n  // Tool 결과를 파싱하여 resultMap 업데이트\r\n  useEffect(() => {\r\n    const next: ToolResultMap = {};\r\n    messages.forEach((msg) => {\r\n      msg.parts?.forEach((part) => {\r\n        if (part.type !== \"tool-invocation\") return;\r\n        const { toolName, state } = part.toolInvocation;\r\n        if (state !== \"result\" || !('result' in part.toolInvocation)) return;\r\n        (next[toolName] ??= []).push(part.toolInvocation as any);\r\n      });\r\n    });\r\n    setResultMap(next);\r\n  }, [messages]);\r\n\r\n  // 메시지가 변경될 때 (이전 대화 로드 시) 처리 상태 초기화\r\n  useEffect(() => {\r\n    const previousMessages = previousMessagesRef.current;\r\n\r\n    // 새로운 대화를 불러온 경우를 감지\r\n    const isNewConversation =\r\n      // 이전에 메시지가 없었고 현재 메시지가 있는 경우 (초기 로드)\r\n      (previousMessages.length === 0 && messages.length > 0) ||\r\n      // 첫 번째 메시지의 ID가 다른 경우 (다른 대화 로드)\r\n      (previousMessages.length > 0 && messages.length > 0 &&\r\n        previousMessages[0]?.id !== messages[0]?.id) ||\r\n      // 메시지 수가 크게 줄어든 경우 (다른 대화 로드)\r\n      (previousMessages.length > messages.length && messages.length > 0);\r\n\r\n    if (isNewConversation) {\r\n      // 이전 대화를 불러올 때 처리 상태를 초기화하여 스타일 등이 다시 적용되도록 함\r\n      processedToolCallsRef.current.clear();\r\n      processedSmartNavigationRef.current.clear();\r\n      console.log('🔄 New conversation detected - clearing processed tool calls for style reapplication');\r\n    }\r\n\r\n    // 현재 메시지 상태를 저장\r\n    previousMessagesRef.current = [...messages];\r\n  }, [messages]);\r\n\r\n  // Tool 결과를 기반으로 레이어 상태 업데이트\r\n  useEffect(() => {\r\n    const newLayers: ManagedLayerProps[] = [];\r\n\r\n    Object.entries(resultMap).forEach(([toolName, invocations]) => {\r\n      const transformer = layerTransformers[toolName];\r\n\r\n      invocations.forEach((invocation) => {\r\n        if (invocation.state === 'result' && 'result' in invocation) {\r\n          // 이미 처리된 tool call인지 확인\r\n          const toolCallKey = `${toolName}-${invocation.toolCallId}`;\r\n          if (processedToolCallsRef.current.has(toolCallKey)) {\r\n            return; // 이미 처리됨, 건너뛰기\r\n          }\r\n\r\n          try {\r\n            if (toolName === 'createLayerFilter') {\r\n              // 필터 업데이트는 기존 레이어에 적용\r\n              const filterResult = invocation.result as any;\r\n              if (filterResult.lyr_id && filterResult.filter) {\r\n                dispatch({\r\n                  type: 'UPDATE_FILTER',\r\n                  payload: { id: filterResult.lyr_id, filter: filterResult.filter }\r\n                });\r\n                processedToolCallsRef.current.add(toolCallKey);\r\n              }\r\n            } else if (toolName === 'updateLayerStyle') {\r\n              // 스타일 업데이트는 기존 레이어에 적용\r\n              const styleResult = invocation.result as any;\r\n              console.log('Style result:', styleResult);\r\n              if (styleResult.layerId && styleResult.styleUpdate && styleResult.success) {\r\n                // 현재 레이어 정보 가져오기\r\n                const currentLayer = layers.find(layer => layer.id === styleResult.layerId);\r\n                const layerInfo = currentLayer ? {\r\n                  geometryType: (currentLayer as any).geometryType,\r\n                  currentStyle: (currentLayer as any).style || (currentLayer as any).renderOptions?.style\r\n                } : undefined;\r\n\r\n                console.log('Current layer info:', layerInfo);\r\n\r\n                // 단순한 스타일 속성을 WMS 스타일 구조로 변환\r\n                const convertedStyle = convertSimpleStyleToWMS(styleResult.styleUpdate, layerInfo);\r\n                console.log('Converted style:', convertedStyle);\r\n\r\n                dispatch({\r\n                  type: 'UPDATE_STYLE',\r\n                  payload: { id: styleResult.layerId, style: convertedStyle }\r\n                });\r\n                processedToolCallsRef.current.add(toolCallKey);\r\n              }\r\n            } else if (toolName === 'generateCategoricalStyle') {\r\n              // 유형별 스타일 생성 결과 처리\r\n              const categoricalResult = invocation.result as any;\r\n              console.log('Categorical style result:', categoricalResult);\r\n              if (categoricalResult.layerId && categoricalResult.styleRules && categoricalResult.success) {\r\n                // 현재 레이어 정보 가져오기\r\n                const currentLayer = layers.find(layer => layer.id === categoricalResult.layerId);\r\n                const geometryType = (currentLayer as any)?.geometryType || 'point';\r\n\r\n                // 지오메트리 타입에 따른 심볼라이저 생성 함수\r\n                const createSymbolizerForGeometry = (color: string) => {\r\n                  if (geometryType === 'point' || geometryType === '1') {\r\n                    return {\r\n                      kind: 'Mark' as const,\r\n                      wellKnownName: 'circle' as const,\r\n                      radius: 6,\r\n                      color: color,\r\n                      fillOpacity: 1,\r\n                      strokeColor: '#000000',\r\n                      strokeWidth: 1,\r\n                      strokeOpacity: 1\r\n                    };\r\n                  } else if (geometryType === 'line' || geometryType === '2') {\r\n                    return {\r\n                      kind: 'Line' as const,\r\n                      color: color,\r\n                      width: 2,\r\n                      opacity: 1,\r\n                      cap: 'round' as const,\r\n                      join: 'round' as const\r\n                    };\r\n                  } else { // polygon\r\n                    return {\r\n                      kind: 'Fill' as const,\r\n                      color: color,\r\n                      fillOpacity: 0.7,\r\n                      outlineColor: '#000000',\r\n                      outlineWidth: 1\r\n                    };\r\n                  }\r\n                };\r\n\r\n                // 단일 조건 필터 생성 함수\r\n                const createSingleFilter = (condition: string, value: string, attributeName: string) => {\r\n                  switch (condition) {\r\n                    case 'like':\r\n                      return ['*=', attributeName, `*${value}*`];\r\n                    case 'equal':\r\n                      return ['==', attributeName, value];\r\n                    case 'greater':\r\n                      return ['>', attributeName, value];\r\n                    case 'less':\r\n                      return ['<', attributeName, value];\r\n                    case 'greaterEqual':\r\n                      return ['>=', attributeName, value];\r\n                    case 'lessEqual':\r\n                      return ['<=', attributeName, value];\r\n                    case 'default':\r\n                    default:\r\n                      return null; // 기본 스타일은 필터 없음\r\n                  }\r\n                };\r\n\r\n                // 복합 조건 필터 생성 함수\r\n                const createComplexFilter = (conditions: any[], logicalOperator: string = 'AND') => {\r\n                  if (!conditions || conditions.length === 0) {\r\n                    return null; // 기본 스타일은 필터 없음\r\n                  }\r\n\r\n                  if (conditions.length === 1) {\r\n                    const cond = conditions[0];\r\n                    return createSingleFilter(cond.condition, cond.value, cond.attributeName);\r\n                  }\r\n\r\n                  // 두 개 이상의 조건 처리\r\n                  const filterConditions = conditions.map(cond =>\r\n                    createSingleFilter(cond.condition, cond.value, cond.attributeName)\r\n                  ).filter(filter => filter !== null);\r\n\r\n                  if (filterConditions.length === 0) {\r\n                    return null;\r\n                  }\r\n\r\n                  if (filterConditions.length === 1) {\r\n                    return filterConditions[0];\r\n                  }\r\n\r\n                  // 논리 연산자에 따라 필터 조합\r\n                  const operator = logicalOperator === 'OR' ? '||' : '&&';\r\n                  return [operator, ...filterConditions];\r\n                };\r\n\r\n                // 규칙 순서 조정: default 규칙을 첫 번째로, 구체적인 조건들을 나중에\r\n                const sortedStyleRules = [...categoricalResult.styleRules].sort((a, b) => {\r\n                  // default 조건을 첫 번째로 (conditions가 비어있거나 default 조건이 있는 경우)\r\n                  const aIsDefault = !a.conditions || a.conditions.length === 0 ||\r\n                    a.conditions.some((cond: any) => cond.condition === 'default');\r\n                  const bIsDefault = !b.conditions || b.conditions.length === 0 ||\r\n                    b.conditions.some((cond: any) => cond.condition === 'default');\r\n\r\n                  if (aIsDefault && !bIsDefault) return -1;\r\n                  if (bIsDefault && !aIsDefault) return 1;\r\n                  // 나머지는 원래 순서 유지\r\n                  return 0;\r\n                });\r\n\r\n                // 유형별 스타일 규칙들을 WMS SLD 형식으로 변환\r\n                const sldRules = sortedStyleRules.map((rule: any, index: number) => {\r\n                  // AI가 생성한 헥스 색상 그대로 사용\r\n                  const hexColor = rule.color || '#808080';\r\n\r\n                  const sldRule: any = {\r\n                    name: rule.description || `Rule ${index + 1}`,\r\n                    symbolizers: [createSymbolizerForGeometry(hexColor)]\r\n                  };\r\n\r\n                  // 복합 조건 필터 생성\r\n                  const filter = createComplexFilter(rule.conditions, rule.logicalOperator);\r\n                  if (filter) {\r\n                    sldRule.filter = filter;\r\n                  }\r\n\r\n                  return sldRule;\r\n                });\r\n\r\n                const wmsStyle = {\r\n                  rules: sldRules\r\n                };\r\n\r\n                console.log('Converted categorical style:', wmsStyle);\r\n\r\n                dispatch({\r\n                  type: 'UPDATE_STYLE',\r\n                  payload: { id: categoricalResult.layerId, style: wmsStyle }\r\n                });\r\n                processedToolCallsRef.current.add(toolCallKey);\r\n              }\r\n            } else if (toolName === 'removeLayer') {\r\n              // 레이어 삭제 처리\r\n              const removeResult = invocation.result as any;\r\n              console.log('Remove layer result:', removeResult);\r\n              if (removeResult.layerId && removeResult.success) {\r\n                dispatch({\r\n                  type: 'REMOVE_LAYER',\r\n                  payload: removeResult.layerId\r\n                });\r\n                processedToolCallsRef.current.add(toolCallKey);\r\n              }\r\n            } else if (transformer) {\r\n              // transformer 사용 (mapState 전달)\r\n              const layerProps = transformer(invocation.result, invocation.toolCallId, mapState);\r\n\r\n              // 스마트 네비게이션이 비활성화된 경우 레이어를 숨김 상태로 생성\r\n              if (!enableSmartNavigation) {\r\n                layerProps.visible = false;\r\n                console.log('🚫 Smart navigation disabled - hiding layer:', layerProps.id);\r\n              } else {\r\n                console.log('✅ Smart navigation enabled - showing layer:', layerProps.id);\r\n              }\r\n\r\n              newLayers.push(layerProps);\r\n              processedToolCallsRef.current.add(toolCallKey);\r\n\r\n              // 스마트 네비게이션 수행 (새로운 도구 결과에 대해서만 1회 실행)\r\n              handleSmartNavigation(toolName, invocation.result, invocation.toolCallId);\r\n            } else {\r\n              // 레이어를 생성하지 않는 도구들에 대한 스마트 네비게이션 처리\r\n              handleSmartNavigation(toolName, invocation.result, invocation.toolCallId);\r\n              processedToolCallsRef.current.add(toolCallKey);\r\n            }\r\n          } catch (error) {\r\n            console.error(`Error transforming ${toolName} result:`, error);\r\n          }\r\n        }\r\n      });\r\n    });\r\n\r\n    // 새 레이어들 추가\r\n    newLayers.forEach(layer => {\r\n      dispatch({ type: 'ADD_LAYER', payload: layer });\r\n    });\r\n  }, [resultMap, enableSmartNavigation, mapState]);\r\n\r\n  // 스마트 네비게이션 상태 변경 시 기존 레이어들의 가시성 업데이트\r\n  useEffect(() => {\r\n    layers.forEach(layer => {\r\n      // 도구 결과로 생성된 레이어들만 제어 (toolCallId가 있는 레이어)\r\n      // 단, 사용자가 수동으로 수정한 레이어는 제외\r\n      if (layer.toolCallId && !layer.userModified) {\r\n        const shouldBeVisible = enableSmartNavigation;\r\n        if (layer.visible !== shouldBeVisible) {\r\n          console.log(`🔄 Auto-updating layer visibility: ${layer.id} -> ${shouldBeVisible} (smart navigation)`);\r\n          dispatch({\r\n            type: 'UPDATE_LAYER',\r\n            payload: { id: layer.id, updates: { visible: shouldBeVisible } }\r\n          });\r\n        }\r\n      } else if (layer.userModified) {\r\n        console.log(`⏭️ Skipping auto-update for user-modified layer: ${layer.id}`);\r\n      }\r\n    });\r\n  }, [enableSmartNavigation, layers]);\r\n\r\n  // 컴포넌트 언마운트 시 타이머 정리\r\n  useEffect(() => {\r\n    return () => {\r\n      if (addressBatchTimeoutRef.current) {\r\n        clearTimeout(addressBatchTimeoutRef.current);\r\n        addressBatchTimeoutRef.current = null;\r\n      }\r\n      pendingAddressesRef.current = [];\r\n    };\r\n  }, []);\r\n\r\n  // 레이어 관리 컨텍스트 값\r\n  const layerManagerValue: ILayerManagerContext = useMemo(() => ({\r\n    layers,\r\n    addLayer: (layer: ManagedLayerProps) => dispatch({ type: 'ADD_LAYER', payload: layer }),\r\n    updateLayer: (id: string, updates: Partial<ManagedLayerProps>) =>\r\n      dispatch({ type: 'UPDATE_LAYER', payload: { id, updates } }),\r\n    removeLayer: (id: string) => dispatch({ type: 'REMOVE_LAYER', payload: id }),\r\n    toggleVisibility: (id: string) => dispatch({ type: 'TOGGLE_VISIBILITY', payload: id }),\r\n    updateFilter: (id: string, filter: string) =>\r\n      dispatch({ type: 'UPDATE_FILTER', payload: { id, filter } }),\r\n    updateStyle: (id: string, style: any) =>\r\n      dispatch({ type: 'UPDATE_STYLE', payload: { id, style } }),\r\n    updateZIndex: (id: string, zIndex: number) =>\r\n      dispatch({ type: 'UPDATE_Z_INDEX', payload: { id, zIndex } }),\r\n    reorderLayers: (layerIds: string[]) =>\r\n      dispatch({ type: 'REORDER_LAYERS', payload: { layerIds } }),\r\n    getLayerById: (id: string) => layers.find(layer => layer.id === id),\r\n  }), [layers]);\r\n\r\n  // 위치 컨텍스트 값\r\n  const locationValue: LocationContextType = useMemo(() => ({\r\n    currentLocation,\r\n    setCurrentLocation,\r\n    originPoint,\r\n    setOriginPoint,\r\n    destinationPoint,\r\n    setDestinationPoint,\r\n  }), [currentLocation, originPoint, destinationPoint]);\r\n\r\n  return (\r\n    <ToolContext.Provider value={resultMap}>\r\n      <LayerManagerContext.Provider value={layerManagerValue}>\r\n        <LocationContext.Provider value={locationValue}>\r\n          {children}\r\n        </LocationContext.Provider>\r\n      </LayerManagerContext.Provider>\r\n    </ToolContext.Provider>\r\n  );\r\n}\r\n\r\nexport function useToolResults<T = unknown>(\r\n  toolName: string,\r\n  transform?: (raw: unknown) => T,\r\n): T[] {\r\n  const map = useContext(ToolContext);\r\n  return useMemo(\r\n    () =>\r\n      (map[toolName] ?? [])\r\n        .filter((ti): ti is Extract<ToolInvocation, { state: \"result\" }> =>\r\n          ti.state === \"result\" && 'result' in ti\r\n        )\r\n        .map((ti) =>\r\n          transform ? transform(ti.result) : (ti.result as T)\r\n        ),\r\n    [map, toolName, transform],\r\n  );\r\n}\r\n\r\n// 레이어 관리자 훅\r\nexport function useLayerManager(): ILayerManagerContext {\r\n  const context = useContext(LayerManagerContext);\r\n  if (!context) {\r\n    throw new Error('useLayerManager must be used within a ToolInvocationProvider');\r\n  }\r\n  return context;\r\n}\r\n\r\n// 특정 레이어 정보를 가져오는 훅\r\nexport function useLayerById(id: string): ManagedLayerProps | undefined {\r\n  const { getLayerById } = useLayerManager();\r\n  return getLayerById(id);\r\n}\r\n\r\n// 필터가 적용된 레이어들을 가져오는 훅\r\nexport function useFilteredLayers(): ManagedLayerProps[] {\r\n  const { layers } = useLayerManager();\r\n  return layers.filter(layer => layer.filter);\r\n}\r\n\r\n// 위치 정보를 사용하는 훅\r\nexport function useLocation(): LocationContextType {\r\n  const context = useContext(LocationContext);\r\n  if (!context) {\r\n    throw new Error('useLocation must be used within a ToolInvocationProvider');\r\n  }\r\n  return context;\r\n}\r\n\r\n"], "names": [], "mappings": ";;;;;;;;;AAEA;AAWA;AAOA;AACA;AACA;AACA;AAvBA;;;;;;;;AA8BA,kCAAkC;AAClC,MAAM,0BAA0B,CAC9B,aACA;IAEA,IAAI;IAEJ,0BAA0B;IAC1B,IAAI,WAAW,gBAAgB,UAAU,YAAY,CAAC,KAAK,EAAE;QAC3D,YAAY;YAAE,GAAG,UAAU,YAAY;QAAC;IAC1C,OAAO;QACL,6BAA6B;QAC7B,MAAM,eAAe,WAAW,gBAAgB;QAChD,OAAQ;YACN,KAAK;YACL,KAAK;gBACH,YAAY,CAAA,GAAA,uHAAA,CAAA,4BAAyB,AAAD;gBACpC;YACF,KAAK;YACL,KAAK;gBACH,YAAY,CAAA,GAAA,uHAAA,CAAA,yBAAsB,AAAD;gBACjC;YACF,KAAK;YACL,KAAK;YACL;gBACE,YAAY,CAAA,GAAA,uHAAA,CAAA,0BAAuB,AAAD;gBAClC;QACJ;IACF;IAEA,kBAAkB;IAClB,MAAM,eAAe,KAAK,KAAK,CAAC,KAAK,SAAS,CAAC;IAC/C,MAAM,OAAO,aAAa,KAAK,CAAC,EAAE;IAClC,MAAM,aAAa,KAAK,WAAW,CAAC,EAAE;IAEtC,wBAAwB;IACxB,IAAI,YAAY,KAAK,EAAE;QACrB,WAAW,KAAK,GAAG,YAAY,KAAK;IACtC;IACA,IAAI,YAAY,WAAW,KAAK,WAAW;QACzC,WAAW,WAAW,GAAG,YAAY,WAAW;IAClD;IACA,IAAI,YAAY,WAAW,EAAE;QAC3B,IAAI,WAAW,IAAI,KAAK,QAAQ;YAC9B,WAAW,YAAY,GAAG,YAAY,WAAW;QACnD,OAAO;YACL,WAAW,WAAW,GAAG,YAAY,WAAW;QAClD;IACF;IACA,IAAI,YAAY,WAAW,KAAK,WAAW;QACzC,IAAI,WAAW,IAAI,KAAK,QAAQ;YAC9B,WAAW,YAAY,GAAG,YAAY,WAAW;QACnD,OAAO;YACL,WAAW,WAAW,GAAG,YAAY,WAAW;QAClD;IACF;IACA,IAAI,YAAY,MAAM,KAAK,aAAa,WAAW,IAAI,KAAK,QAAQ;QAClE,WAAW,MAAM,GAAG,YAAY,MAAM;IACxC;IACA,IAAI,YAAY,KAAK,KAAK,aAAa,WAAW,IAAI,KAAK,QAAQ;QACjE,WAAW,KAAK,GAAG,YAAY,KAAK;IACtC;IACA,IAAI,YAAY,MAAM,IAAI,WAAW,IAAI,KAAK,QAAQ;QACpD,WAAW,aAAa,GAAG,YAAY,MAAM;IAC/C;IAEA,OAAO;AACT;AAkCA,MAAM,4BAAc,CAAA,GAAA,8SAAA,CAAA,gBAAa,AAAD,EAAiB,CAAC;AAClD,MAAM,oCAAsB,CAAA,GAAA,8SAAA,CAAA,gBAAa,AAAD,EAA+B;AACvE,MAAM,gCAAkB,CAAA,GAAA,8SAAA,CAAA,gBAAa,AAAD,EAAuB;IACzD,iBAAiB;IACjB,oBAAoB,KAAQ;IAC5B,aAAa;IACb,gBAAgB,KAAQ;IACxB,kBAAkB;IAClB,qBAAqB,KAAQ;AAC/B;AAEA,gBAAgB;AAChB,SAAS,aAAa,KAA0B,EAAE,MAAmB;IACnE,OAAQ,OAAO,IAAI;QACjB,KAAK;YACH,yBAAyB;YACzB,MAAM,gBAAgB,MAAM,SAAS,CAAC,CAAA,QAAS,MAAM,EAAE,KAAK,OAAO,OAAO,CAAC,EAAE;YAC7E,IAAI,iBAAiB,GAAG;gBACtB,MAAM,WAAW;uBAAI;iBAAM;gBAC3B,QAAQ,CAAC,cAAc,GAAG;oBAAE,GAAG,QAAQ,CAAC,cAAc;oBAAE,GAAG,OAAO,OAAO;gBAAC;gBAC1E,OAAO;YACT;YAEA,wCAAwC;YACxC,MAAM,YAAY,MAAM,MAAM,GAAG,IAAI,KAAK,GAAG,IAAI,MAAM,GAAG,CAAC,CAAA,QAAS,MAAM,MAAM,IAAI,MAAM;YAC1F,MAAM,WAAW;gBACf,GAAG,OAAO,OAAO;gBACjB,QAAQ,YAAY;YACtB;YAEA,OAAO;mBAAI;gBAAO;aAAS;QAE7B,KAAK;YACH,OAAO,MAAM,GAAG,CAAC,CAAA,QACf,MAAM,EAAE,KAAK,OAAO,OAAO,CAAC,EAAE,GAC1B;oBAAE,GAAG,KAAK;oBAAE,GAAG,OAAO,OAAO,CAAC,OAAO;gBAAC,IACtC;QAGR,KAAK;YACH,OAAO,MAAM,MAAM,CAAC,CAAA,QAAS,MAAM,EAAE,KAAK,OAAO,OAAO;QAE1D,KAAK;YACH,OAAO,MAAM,GAAG,CAAC,CAAA,QACf,MAAM,EAAE,KAAK,OAAO,OAAO,GACvB;oBAAE,GAAG,KAAK;oBAAE,SAAS,CAAC,MAAM,OAAO;oBAAE,cAAc;gBAAK,IACxD;QAGR,KAAK;YACH,OAAO,MAAM,GAAG,CAAC,CAAA,QACf,MAAM,EAAE,KAAK,OAAO,OAAO,CAAC,EAAE,GAC1B;oBAAE,GAAG,KAAK;oBAAE,QAAQ,OAAO,OAAO,CAAC,MAAM;gBAAC,IAC1C;QAGR,KAAK;YACH,OAAO,MAAM,GAAG,CAAC,CAAA,QACf,MAAM,EAAE,KAAK,OAAO,OAAO,CAAC,EAAE,GAC1B;oBAAE,GAAG,KAAK;oBAAE,OAAO,OAAO,OAAO,CAAC,KAAK;gBAAC,IACxC;QAGR,KAAK;YACH,OAAO,MAAM,GAAG,CAAC,CAAA,QACf,MAAM,EAAE,KAAK,OAAO,OAAO,CAAC,EAAE,GAC1B;oBAAE,GAAG,KAAK;oBAAE,QAAQ,OAAO,OAAO,CAAC,MAAM;gBAAC,IAC1C;QAGR,KAAK;YACH,MAAM,EAAE,QAAQ,EAAE,GAAG,OAAO,OAAO;YAEnC,+BAA+B;YAC/B,OAAO,MAAM,GAAG,CAAC,CAAA;gBACf,MAAM,aAAa,SAAS,OAAO,CAAC,MAAM,EAAE;gBAC5C,IAAI,cAAc,GAAG;oBACnB,yBAAyB;oBACzB,uCAAuC;oBACvC,wCAAwC;oBACxC,MAAM,YAAY,SAAS,MAAM,GAAG;oBAEpC,2BAA2B;oBAC3B,IAAI,MAAM,MAAM,KAAK,WAAW;wBAC9B,OAAO;4BACL,GAAG,KAAK;4BACR,QAAQ;wBACV;oBACF;oBACA,OAAO,OAAO,2BAA2B;gBAC3C;gBAEA,iCAAiC;gBACjC,IAAI,MAAM,MAAM,KAAK,GAAG;oBACtB,OAAO;wBACL,GAAG,KAAK;wBACR,QAAQ;oBACV;gBACF;gBACA,OAAO,OAAO,yBAAyB;YACzC;QAEF,KAAK;YACH,OAAO,OAAO,OAAO;QAEvB;YACE,OAAO;IACX;AACF;AAEO,SAAS,uBAAuB,EACrC,QAAQ,EACR,QAAQ,EACR,wBAAwB,IAAI,EAC5B,QAAQ,EAMT;IACC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,8SAAA,CAAA,WAAQ,AAAD,EAAiB,CAAC;IAC3D,MAAM,CAAC,QAAQ,SAAS,GAAG,CAAA,GAAA,8SAAA,CAAA,aAAU,AAAD,EAAE,cAAc,EAAE;IACtD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,8SAAA,CAAA,WAAQ,AAAD,EAAuB;IAC5E,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,8SAAA,CAAA,WAAQ,AAAD,EAAyB;IACtE,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,8SAAA,CAAA,WAAQ,AAAD,EAAyB;IAChF,MAAM,wBAAwB,CAAA,GAAA,8SAAA,CAAA,SAAM,AAAD,EAAe,IAAI;IACtD,MAAM,8BAA8B,CAAA,GAAA,8SAAA,CAAA,SAAM,AAAD,EAAe,IAAI,QAAQ,kBAAkB;IACtF,MAAM,sBAAsB,CAAA,GAAA,8SAAA,CAAA,SAAM,AAAD,EAAa,EAAE,GAAG,eAAe;IAElE,8BAA8B;IAC9B,MAAM,EAAE,iBAAiB,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,aAAU,AAAD;IAEvC,mBAAmB;IACnB,MAAM,sBAAsB,CAAA,GAAA,8SAAA,CAAA,SAAM,AAAD,EAG7B,EAAE;IACN,MAAM,yBAAyB,CAAA,GAAA,8SAAA,CAAA,SAAM,AAAD,EAAyB;IAC7D,MAAM,cAAc,MAAM,QAAQ;IAElC,0BAA0B;IAC1B,MAAM,0BAA0B,CAAA,GAAA,8SAAA,CAAA,cAAW,AAAD,EAAE;QAC1C,IAAI,CAAC,yBAAyB,CAAC,YAAY,oBAAoB,OAAO,CAAC,MAAM,KAAK,GAAG;YACnF;QACF;QAEA,IAAI;YACF,MAAM,YAAY,oBAAoB,OAAO;YAE7C,IAAI,UAAU,MAAM,KAAK,GAAG;gBAC1B,sBAAsB;gBACtB,MAAM,EAAE,OAAO,EAAE,GAAG,SAAS,CAAC,EAAE;gBAChC,MAAM,UAAU,CAAA,GAAA,mHAAA,CAAA,sBAAmB,AAAD,EAChC,UACA,WAAW,QAAQ,OAAO,GAC1B,WAAW,QAAQ,OAAO,GAC1B,QAAQ,SAAS,IAAI,QAAQ,IAAI,EACjC,IACA;oBACE,eAAe;oBACf,aAAa;oBACb,YAAY;gBACd;gBAEF,IAAI,SAAS;oBACX,wQAAA,CAAA,QAAK,CAAC,OAAO,CAAC,GAAG,QAAQ,QAAQ,CAAC,WAAW,CAAC;gBAChD;YACF,OAAO;gBACL,gCAAgC;gBAChC,MAAM,cAAc,UAAU,GAAG,CAAC,CAAC,EAAE,OAAO,EAAE,GAAK,CAAC;wBAClD,KAAK,WAAW,QAAQ,OAAO;wBAC/B,KAAK,WAAW,QAAQ,OAAO;wBAC/B,UAAU,QAAQ,SAAS,IAAI,QAAQ,IAAI;oBAC7C,CAAC;gBAED,4BAA4B;gBAC5B,YAAY,OAAO,CAAC,CAAC,OAAO;oBAC1B,CAAA,GAAA,mHAAA,CAAA,sBAAmB,AAAD,EAChB,UACA,MAAM,GAAG,EACT,MAAM,GAAG,EACT,MAAM,QAAQ,EACd,IACA;wBACE,eAAe,UAAU;wBACzB,aAAa;wBACb,YAAY;oBACd;gBAEJ;gBAEA,wQAAA,CAAA,QAAK,CAAC,OAAO,CAAC,GAAG,UAAU,MAAM,CAAC,gBAAgB,CAAC;YACrD;YAEA,cAAc;YACd,oBAAoB,OAAO,GAAG,EAAE;QAClC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kBAAkB;YAChC,oBAAoB,OAAO,GAAG,EAAE;QAClC;IACF,GAAG;QAAC;QAAuB;KAAS;IAEpC,gBAAgB;IAChB,MAAM,wBAAwB,CAAA,GAAA,8SAAA,CAAA,cAAW,AAAD,EAAE,CAAC,UAAkB,QAAa;QACxE,IAAI,CAAC,yBAAyB,CAAC,UAAU;QAEzC,MAAM,cAAc,GAAG,SAAS,CAAC,EAAE,YAAY;QAC/C,IAAI,4BAA4B,OAAO,CAAC,GAAG,CAAC,cAAc;YACxD,QAAQ,SAAS;QACnB;QAEA,IAAI;YACF,OAAQ;gBACN,KAAK;oBACH,IAAI,OAAO,MAAM,EAAE,UAAU,SAAS,GAAG;wBACvC,MAAM,eAAe,OAAO,MAAM,CAAC,QAAQ,CAAC,EAAE;wBAC9C,IAAI,aAAa,OAAO,IAAI,aAAa,OAAO,EAAE;4BAChD,kBAAkB;4BAClB,oBAAoB,OAAO,CAAC,IAAI,CAAC;gCAC/B,SAAS;gCACT;4BACF;4BAEA,YAAY;4BACZ,IAAI,uBAAuB,OAAO,EAAE;gCAClC,aAAa,uBAAuB,OAAO;4BAC7C;4BAEA,WAAW;4BACX,uBAAuB,OAAO,GAAG,WAAW;gCAC1C;gCACA,uBAAuB,OAAO,GAAG;4BACnC,GAAG;wBACL;oBACF;oBACA;gBAEF,KAAK;oBACH,IAAI,OAAO,MAAM,EAAE,UAAU,SAAS,GAAG;wBACvC,MAAM,eAAe,OAAO,MAAM,CAAC,QAAQ,CAAC,EAAE;wBAC9C,IAAI,aAAa,OAAO,IAAI,aAAa,OAAO,IAAI,UAAU,KAAK;4BACjE,MAAM,aAAa,SAAS,GAAG,CAAC,aAAa;4BAC7C,MAAM,iBAAiB,WAAW,OAAO,CAAC;gCACxC,WAAW,aAAa,OAAO;gCAC/B,WAAW,aAAa,OAAO;6BAChC,EAAE;4BAEH,YAAY;4BACZ,eAAe;gCACb,SAAS;gCACT,gBAAgB;gCAChB;4BACF;wBAEF;oBACF;oBACA;gBAEF,KAAK;oBACH,IAAI,OAAO,MAAM,EAAE,UAAU,SAAS,GAAG;wBACvC,MAAM,eAAe,OAAO,MAAM,CAAC,QAAQ,CAAC,EAAE;wBAC9C,IAAI,aAAa,OAAO,IAAI,aAAa,OAAO,IAAI,UAAU,KAAK;4BACjE,MAAM,aAAa,SAAS,GAAG,CAAC,aAAa;4BAC7C,MAAM,iBAAiB,WAAW,OAAO,CAAC;gCACxC,WAAW,aAAa,OAAO;gCAC/B,WAAW,aAAa,OAAO;6BAChC,EAAE;4BAEH,YAAY;4BACZ,oBAAoB;gCAClB,SAAS;gCACT,gBAAgB;gCAChB;4BACF;wBAEF;oBACF;oBACA;gBAEF,KAAK;oBACH,IAAI,OAAO,MAAM,EAAE,SAAS,KAAK,OAAO,MAAM,CAAC,EAAE,CAAC,WAAW,KAAK,GAAG;wBACnE,qCAAqC;wBACrC,4BAA4B;wBAC5B,wQAAA,CAAA,QAAK,CAAC,OAAO,CAAC;oBAChB;oBACA;gBAEF,KAAK;oBACH,IAAI,CAAC,UAAU,KAAK;oBACpB,MAAM,MAAM,SAAS,GAAG,CAAC,aAAa;oBACtC,IAAI,OAAO,QAAQ,IAAI,OAAO,SAAS,EAAE;wBACvC,MAAM,iBAAiB,IAAI,OAAO,CAAC;4BAAC,OAAO,SAAS;4BAAE,OAAO,QAAQ;yBAAC,EAAE;wBAExE,gBAAgB;wBAChB,MAAM,eAA6B;4BACjC,UAAU,OAAO,QAAQ;4BACzB,WAAW,OAAO,SAAS;4BAC3B,UAAU,OAAO,QAAQ;4BACzB,WAAW,OAAO,SAAS;4BAC3B,gBAAgB;wBAClB;wBACA,mBAAmB;wBAEnB,SAAS,GAAG,CAAC,OAAO,CAAC;wBACrB,SAAS,GAAG,CAAC,SAAS,CAAC;oBACzB;oBACA;gBAEF,KAAK;oBACH,2DAA2D;oBAC3D,IAAI,OAAO,KAAK,KAAK,kBAAkB,OAAO,IAAI,KAAK,gBAAgB;wBACrE,IAAI,CAAC,UAAU,KAAK;wBAEpB,MAAM,SAAS,IAAI,IAAI,UAAU,CAAC,aAAa;wBAC/C,MAAM,UAAU;wBAEhB,SAAS,GAAG,CAAC,SAAS,CAAC;wBACvB,SAAS,GAAG,CAAC,OAAO,CAAC;wBACrB,wQAAA,CAAA,QAAK,CAAC,OAAO,CAAC,GAAG,OAAO,IAAI,IAAI,OAAO,EAAE,CAAC,gBAAgB,CAAC;oBAC7D,OAAO,IAAI,OAAO,IAAI,IAAI,MAAM,OAAO,CAAC,OAAO,IAAI,KAAK,OAAO,IAAI,CAAC,MAAM,IAAI,GAAG;wBAC/E,MAAM,CAAC,MAAM,MAAM,MAAM,KAAK,GAAG,OAAO,IAAI;wBAC5C,MAAM,UAAU,CAAC,OAAO,IAAI,IAAI;wBAChC,MAAM,UAAU,CAAC,OAAO,IAAI,IAAI;wBAChC,CAAA,GAAA,mHAAA,CAAA,sBAAmB,AAAD,EAAE,UAAU,SAAS,SAAS,WAAW;wBAC3D,wQAAA,CAAA,QAAK,CAAC,OAAO,CAAC,GAAG,OAAO,IAAI,IAAI,OAAO,EAAE,CAAC,oBAAoB,CAAC;oBACjE,OAAO;wBACL,wQAAA,CAAA,QAAK,CAAC,OAAO,CAAC,GAAG,OAAO,IAAI,IAAI,OAAO,EAAE,CAAC,gBAAgB,CAAC;oBAC7D;oBACA;gBAEF,KAAK;oBACH,IAAI,OAAO,OAAO,IAAI,UAAU,MAAM,YAAY;wBAChD,qBAAqB;wBACrB,SAAS,IAAI,CAAC,UAAU,CAAC,OAAO,OAAO;wBACvC,0CAA0C;wBAC1C,kBAAkB,OAAO,OAAO;wBAChC,wQAAA,CAAA,QAAK,CAAC,OAAO,CAAC,CAAC,aAAa,CAAC;oBAC/B;oBACA;gBAEF,KAAK;oBACH,IAAI,OAAO,MAAM,IAAI,UAAU,MAAM,WAAW;wBAC9C,MAAM,CAAC,WAAW,SAAS,GAAG,OAAO,MAAM;wBAC3C,SAAS,IAAI,CAAC,SAAS,CAAC;4BAAC;4BAAW;yBAAS;wBAC7C,wQAAA,CAAA,QAAK,CAAC,OAAO,CAAC,OAAO,OAAO,IAAI,CAAC,eAAe,CAAC;oBACnD;oBACA;gBAEF,KAAK;oBACH,IAAI,OAAO,IAAI,KAAK,aAAa,UAAU,MAAM,SAAS;wBACxD,IAAI,OAAO,QAAQ,KAAK,YAAY;4BAClC,gCAAgC;4BAChC,MAAM,cAAc,SAAS,IAAI,CAAC,OAAO;4BACzC,MAAM,aAAa,OAAO,aAAa,KAAK,OAAO,OAAO,IAAI,GAAG,CAAC,OAAO,IAAI;4BAC7E,MAAM,UAAU,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,IAAI,cAAc;4BACvD,SAAS,IAAI,CAAC,OAAO,CAAC;wBACxB,OAAO;4BACL,YAAY;4BACZ,SAAS,IAAI,CAAC,OAAO,CAAC,OAAO,IAAI;wBACnC;wBACA,wQAAA,CAAA,QAAK,CAAC,OAAO,CAAC,OAAO,OAAO,IAAI,CAAC,oBAAoB,CAAC;oBACxD;oBACA;gBAEF,KAAK;oBACH,IAAI,OAAO,MAAM,KAAK,aAAa,OAAO,MAAM,KAAK,aAAa,UAAU,KAAK,aAAa,UAAU,KAAK,WAAW;wBACtH,MAAM,gBAAgB,SAAS,GAAG,CAAC,SAAS;wBAC5C,kCAAkC;wBAClC,MAAM,YAA8B;4BAClC,aAAa,CAAC,EAAE,GAAG,OAAO,MAAM;4BAChC,aAAa,CAAC,EAAE,GAAG,OAAO,MAAM,CAAE,aAAa;yBAChD;wBACD,SAAS,GAAG,CAAC,SAAS,CAAC;wBACvB,wQAAA,CAAA,QAAK,CAAC,OAAO,CAAC,OAAO,OAAO,IAAI,CAAC,WAAW,CAAC;oBAC/C;oBACA;gBAEF;oBAEE;YACJ;YAEA,4BAA4B,OAAO,CAAC,GAAG,CAAC;QAC1C,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,oCAAoC,EAAE,SAAS,CAAC,CAAC,EAAE;QACpE;IACF,GAAG;QAAC;QAAuB;KAAS;IAEpC,+BAA+B;IAC/B,CAAA,GAAA,8SAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,OAAsB,CAAC;QAC7B,SAAS,OAAO,CAAC,CAAC;YAChB,IAAI,KAAK,EAAE,QAAQ,CAAC;gBAClB,IAAI,KAAK,IAAI,KAAK,mBAAmB;gBACrC,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,GAAG,KAAK,cAAc;gBAC/C,IAAI,UAAU,YAAY,CAAC,CAAC,YAAY,KAAK,cAAc,GAAG;gBAC9D,CAAC,IAAI,CAAC,SAAS,KAAK,EAAE,EAAE,IAAI,CAAC,KAAK,cAAc;YAClD;QACF;QACA,aAAa;IACf,GAAG;QAAC;KAAS;IAEb,oCAAoC;IACpC,CAAA,GAAA,8SAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,mBAAmB,oBAAoB,OAAO;QAEpD,qBAAqB;QACrB,MAAM,oBACJ,qCAAqC;QACpC,iBAAiB,MAAM,KAAK,KAAK,SAAS,MAAM,GAAG,KAEnD,iBAAiB,MAAM,GAAG,KAAK,SAAS,MAAM,GAAG,KAChD,gBAAgB,CAAC,EAAE,EAAE,OAAO,QAAQ,CAAC,EAAE,EAAE,MAE1C,iBAAiB,MAAM,GAAG,SAAS,MAAM,IAAI,SAAS,MAAM,GAAG;QAElE,IAAI,mBAAmB;YACrB,8CAA8C;YAC9C,sBAAsB,OAAO,CAAC,KAAK;YACnC,4BAA4B,OAAO,CAAC,KAAK;YACzC,QAAQ,GAAG,CAAC;QACd;QAEA,gBAAgB;QAChB,oBAAoB,OAAO,GAAG;eAAI;SAAS;IAC7C,GAAG;QAAC;KAAS;IAEb,4BAA4B;IAC5B,CAAA,GAAA,8SAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,YAAiC,EAAE;QAEzC,OAAO,OAAO,CAAC,WAAW,OAAO,CAAC,CAAC,CAAC,UAAU,YAAY;YACxD,MAAM,cAAc,yHAAA,CAAA,oBAAiB,CAAC,SAAS;YAE/C,YAAY,OAAO,CAAC,CAAC;gBACnB,IAAI,WAAW,KAAK,KAAK,YAAY,YAAY,YAAY;oBAC3D,wBAAwB;oBACxB,MAAM,cAAc,GAAG,SAAS,CAAC,EAAE,WAAW,UAAU,EAAE;oBAC1D,IAAI,sBAAsB,OAAO,CAAC,GAAG,CAAC,cAAc;wBAClD,QAAQ,eAAe;oBACzB;oBAEA,IAAI;wBACF,IAAI,aAAa,qBAAqB;4BACpC,sBAAsB;4BACtB,MAAM,eAAe,WAAW,MAAM;4BACtC,IAAI,aAAa,MAAM,IAAI,aAAa,MAAM,EAAE;gCAC9C,SAAS;oCACP,MAAM;oCACN,SAAS;wCAAE,IAAI,aAAa,MAAM;wCAAE,QAAQ,aAAa,MAAM;oCAAC;gCAClE;gCACA,sBAAsB,OAAO,CAAC,GAAG,CAAC;4BACpC;wBACF,OAAO,IAAI,aAAa,oBAAoB;4BAC1C,uBAAuB;4BACvB,MAAM,cAAc,WAAW,MAAM;4BACrC,QAAQ,GAAG,CAAC,iBAAiB;4BAC7B,IAAI,YAAY,OAAO,IAAI,YAAY,WAAW,IAAI,YAAY,OAAO,EAAE;gCACzE,iBAAiB;gCACjB,MAAM,eAAe,OAAO,IAAI,CAAC,CAAA,QAAS,MAAM,EAAE,KAAK,YAAY,OAAO;gCAC1E,MAAM,YAAY,eAAe;oCAC/B,cAAc,AAAC,aAAqB,YAAY;oCAChD,cAAc,AAAC,aAAqB,KAAK,IAAI,AAAC,aAAqB,aAAa,EAAE;gCACpF,IAAI;gCAEJ,QAAQ,GAAG,CAAC,uBAAuB;gCAEnC,6BAA6B;gCAC7B,MAAM,iBAAiB,wBAAwB,YAAY,WAAW,EAAE;gCACxE,QAAQ,GAAG,CAAC,oBAAoB;gCAEhC,SAAS;oCACP,MAAM;oCACN,SAAS;wCAAE,IAAI,YAAY,OAAO;wCAAE,OAAO;oCAAe;gCAC5D;gCACA,sBAAsB,OAAO,CAAC,GAAG,CAAC;4BACpC;wBACF,OAAO,IAAI,aAAa,4BAA4B;4BAClD,mBAAmB;4BACnB,MAAM,oBAAoB,WAAW,MAAM;4BAC3C,QAAQ,GAAG,CAAC,6BAA6B;4BACzC,IAAI,kBAAkB,OAAO,IAAI,kBAAkB,UAAU,IAAI,kBAAkB,OAAO,EAAE;gCAC1F,iBAAiB;gCACjB,MAAM,eAAe,OAAO,IAAI,CAAC,CAAA,QAAS,MAAM,EAAE,KAAK,kBAAkB,OAAO;gCAChF,MAAM,eAAe,AAAC,cAAsB,gBAAgB;gCAE5D,2BAA2B;gCAC3B,MAAM,8BAA8B,CAAC;oCACnC,IAAI,iBAAiB,WAAW,iBAAiB,KAAK;wCACpD,OAAO;4CACL,MAAM;4CACN,eAAe;4CACf,QAAQ;4CACR,OAAO;4CACP,aAAa;4CACb,aAAa;4CACb,aAAa;4CACb,eAAe;wCACjB;oCACF,OAAO,IAAI,iBAAiB,UAAU,iBAAiB,KAAK;wCAC1D,OAAO;4CACL,MAAM;4CACN,OAAO;4CACP,OAAO;4CACP,SAAS;4CACT,KAAK;4CACL,MAAM;wCACR;oCACF,OAAO;wCACL,OAAO;4CACL,MAAM;4CACN,OAAO;4CACP,aAAa;4CACb,cAAc;4CACd,cAAc;wCAChB;oCACF;gCACF;gCAEA,iBAAiB;gCACjB,MAAM,qBAAqB,CAAC,WAAmB,OAAe;oCAC5D,OAAQ;wCACN,KAAK;4CACH,OAAO;gDAAC;gDAAM;gDAAe,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;6CAAC;wCAC5C,KAAK;4CACH,OAAO;gDAAC;gDAAM;gDAAe;6CAAM;wCACrC,KAAK;4CACH,OAAO;gDAAC;gDAAK;gDAAe;6CAAM;wCACpC,KAAK;4CACH,OAAO;gDAAC;gDAAK;gDAAe;6CAAM;wCACpC,KAAK;4CACH,OAAO;gDAAC;gDAAM;gDAAe;6CAAM;wCACrC,KAAK;4CACH,OAAO;gDAAC;gDAAM;gDAAe;6CAAM;wCACrC,KAAK;wCACL;4CACE,OAAO,MAAM,gBAAgB;oCACjC;gCACF;gCAEA,iBAAiB;gCACjB,MAAM,sBAAsB,CAAC,YAAmB,kBAA0B,KAAK;oCAC7E,IAAI,CAAC,cAAc,WAAW,MAAM,KAAK,GAAG;wCAC1C,OAAO,MAAM,gBAAgB;oCAC/B;oCAEA,IAAI,WAAW,MAAM,KAAK,GAAG;wCAC3B,MAAM,OAAO,UAAU,CAAC,EAAE;wCAC1B,OAAO,mBAAmB,KAAK,SAAS,EAAE,KAAK,KAAK,EAAE,KAAK,aAAa;oCAC1E;oCAEA,gBAAgB;oCAChB,MAAM,mBAAmB,WAAW,GAAG,CAAC,CAAA,OACtC,mBAAmB,KAAK,SAAS,EAAE,KAAK,KAAK,EAAE,KAAK,aAAa,GACjE,MAAM,CAAC,CAAA,SAAU,WAAW;oCAE9B,IAAI,iBAAiB,MAAM,KAAK,GAAG;wCACjC,OAAO;oCACT;oCAEA,IAAI,iBAAiB,MAAM,KAAK,GAAG;wCACjC,OAAO,gBAAgB,CAAC,EAAE;oCAC5B;oCAEA,mBAAmB;oCACnB,MAAM,WAAW,oBAAoB,OAAO,OAAO;oCACnD,OAAO;wCAAC;2CAAa;qCAAiB;gCACxC;gCAEA,6CAA6C;gCAC7C,MAAM,mBAAmB;uCAAI,kBAAkB,UAAU;iCAAC,CAAC,IAAI,CAAC,CAAC,GAAG;oCAClE,0DAA0D;oCAC1D,MAAM,aAAa,CAAC,EAAE,UAAU,IAAI,EAAE,UAAU,CAAC,MAAM,KAAK,KAC1D,EAAE,UAAU,CAAC,IAAI,CAAC,CAAC,OAAc,KAAK,SAAS,KAAK;oCACtD,MAAM,aAAa,CAAC,EAAE,UAAU,IAAI,EAAE,UAAU,CAAC,MAAM,KAAK,KAC1D,EAAE,UAAU,CAAC,IAAI,CAAC,CAAC,OAAc,KAAK,SAAS,KAAK;oCAEtD,IAAI,cAAc,CAAC,YAAY,OAAO,CAAC;oCACvC,IAAI,cAAc,CAAC,YAAY,OAAO;oCACtC,gBAAgB;oCAChB,OAAO;gCACT;gCAEA,+BAA+B;gCAC/B,MAAM,WAAW,iBAAiB,GAAG,CAAC,CAAC,MAAW;oCAChD,uBAAuB;oCACvB,MAAM,WAAW,KAAK,KAAK,IAAI;oCAE/B,MAAM,UAAe;wCACnB,MAAM,KAAK,WAAW,IAAI,CAAC,KAAK,EAAE,QAAQ,GAAG;wCAC7C,aAAa;4CAAC,4BAA4B;yCAAU;oCACtD;oCAEA,cAAc;oCACd,MAAM,SAAS,oBAAoB,KAAK,UAAU,EAAE,KAAK,eAAe;oCACxE,IAAI,QAAQ;wCACV,QAAQ,MAAM,GAAG;oCACnB;oCAEA,OAAO;gCACT;gCAEA,MAAM,WAAW;oCACf,OAAO;gCACT;gCAEA,QAAQ,GAAG,CAAC,gCAAgC;gCAE5C,SAAS;oCACP,MAAM;oCACN,SAAS;wCAAE,IAAI,kBAAkB,OAAO;wCAAE,OAAO;oCAAS;gCAC5D;gCACA,sBAAsB,OAAO,CAAC,GAAG,CAAC;4BACpC;wBACF,OAAO,IAAI,aAAa,eAAe;4BACrC,YAAY;4BACZ,MAAM,eAAe,WAAW,MAAM;4BACtC,QAAQ,GAAG,CAAC,wBAAwB;4BACpC,IAAI,aAAa,OAAO,IAAI,aAAa,OAAO,EAAE;gCAChD,SAAS;oCACP,MAAM;oCACN,SAAS,aAAa,OAAO;gCAC/B;gCACA,sBAAsB,OAAO,CAAC,GAAG,CAAC;4BACpC;wBACF,OAAO,IAAI,aAAa;4BACtB,+BAA+B;4BAC/B,MAAM,aAAa,YAAY,WAAW,MAAM,EAAE,WAAW,UAAU,EAAE;4BAEzE,qCAAqC;4BACrC,IAAI,CAAC,uBAAuB;gCAC1B,WAAW,OAAO,GAAG;gCACrB,QAAQ,GAAG,CAAC,gDAAgD,WAAW,EAAE;4BAC3E,OAAO;gCACL,QAAQ,GAAG,CAAC,+CAA+C,WAAW,EAAE;4BAC1E;4BAEA,UAAU,IAAI,CAAC;4BACf,sBAAsB,OAAO,CAAC,GAAG,CAAC;4BAElC,uCAAuC;4BACvC,sBAAsB,UAAU,WAAW,MAAM,EAAE,WAAW,UAAU;wBAC1E,OAAO;4BACL,oCAAoC;4BACpC,sBAAsB,UAAU,WAAW,MAAM,EAAE,WAAW,UAAU;4BACxE,sBAAsB,OAAO,CAAC,GAAG,CAAC;wBACpC;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,CAAC,mBAAmB,EAAE,SAAS,QAAQ,CAAC,EAAE;oBAC1D;gBACF;YACF;QACF;QAEA,YAAY;QACZ,UAAU,OAAO,CAAC,CAAA;YAChB,SAAS;gBAAE,MAAM;gBAAa,SAAS;YAAM;QAC/C;IACF,GAAG;QAAC;QAAW;QAAuB;KAAS;IAE/C,sCAAsC;IACtC,CAAA,GAAA,8SAAA,CAAA,YAAS,AAAD,EAAE;QACR,OAAO,OAAO,CAAC,CAAA;YACb,2CAA2C;YAC3C,2BAA2B;YAC3B,IAAI,MAAM,UAAU,IAAI,CAAC,MAAM,YAAY,EAAE;gBAC3C,MAAM,kBAAkB;gBACxB,IAAI,MAAM,OAAO,KAAK,iBAAiB;oBACrC,QAAQ,GAAG,CAAC,CAAC,mCAAmC,EAAE,MAAM,EAAE,CAAC,IAAI,EAAE,gBAAgB,mBAAmB,CAAC;oBACrG,SAAS;wBACP,MAAM;wBACN,SAAS;4BAAE,IAAI,MAAM,EAAE;4BAAE,SAAS;gCAAE,SAAS;4BAAgB;wBAAE;oBACjE;gBACF;YACF,OAAO,IAAI,MAAM,YAAY,EAAE;gBAC7B,QAAQ,GAAG,CAAC,CAAC,iDAAiD,EAAE,MAAM,EAAE,EAAE;YAC5E;QACF;IACF,GAAG;QAAC;QAAuB;KAAO;IAElC,qBAAqB;IACrB,CAAA,GAAA,8SAAA,CAAA,YAAS,AAAD,EAAE;QACR,OAAO;YACL,IAAI,uBAAuB,OAAO,EAAE;gBAClC,aAAa,uBAAuB,OAAO;gBAC3C,uBAAuB,OAAO,GAAG;YACnC;YACA,oBAAoB,OAAO,GAAG,EAAE;QAClC;IACF,GAAG,EAAE;IAEL,gBAAgB;IAChB,MAAM,oBAA0C,CAAA,GAAA,8SAAA,CAAA,UAAO,AAAD,EAAE,IAAM,CAAC;YAC7D;YACA,UAAU,CAAC,QAA6B,SAAS;oBAAE,MAAM;oBAAa,SAAS;gBAAM;YACrF,aAAa,CAAC,IAAY,UACxB,SAAS;oBAAE,MAAM;oBAAgB,SAAS;wBAAE;wBAAI;oBAAQ;gBAAE;YAC5D,aAAa,CAAC,KAAe,SAAS;oBAAE,MAAM;oBAAgB,SAAS;gBAAG;YAC1E,kBAAkB,CAAC,KAAe,SAAS;oBAAE,MAAM;oBAAqB,SAAS;gBAAG;YACpF,cAAc,CAAC,IAAY,SACzB,SAAS;oBAAE,MAAM;oBAAiB,SAAS;wBAAE;wBAAI;oBAAO;gBAAE;YAC5D,aAAa,CAAC,IAAY,QACxB,SAAS;oBAAE,MAAM;oBAAgB,SAAS;wBAAE;wBAAI;oBAAM;gBAAE;YAC1D,cAAc,CAAC,IAAY,SACzB,SAAS;oBAAE,MAAM;oBAAkB,SAAS;wBAAE;wBAAI;oBAAO;gBAAE;YAC7D,eAAe,CAAC,WACd,SAAS;oBAAE,MAAM;oBAAkB,SAAS;wBAAE;oBAAS;gBAAE;YAC3D,cAAc,CAAC,KAAe,OAAO,IAAI,CAAC,CAAA,QAAS,MAAM,EAAE,KAAK;QAClE,CAAC,GAAG;QAAC;KAAO;IAEZ,YAAY;IACZ,MAAM,gBAAqC,CAAA,GAAA,8SAAA,CAAA,UAAO,AAAD,EAAE,IAAM,CAAC;YACxD;YACA;YACA;YACA;YACA;YACA;QACF,CAAC,GAAG;QAAC;QAAiB;QAAa;KAAiB;IAEpD,qBACE,uVAAC,YAAY,QAAQ;QAAC,OAAO;kBAC3B,cAAA,uVAAC,oBAAoB,QAAQ;YAAC,OAAO;sBACnC,cAAA,uVAAC,gBAAgB,QAAQ;gBAAC,OAAO;0BAC9B;;;;;;;;;;;;;;;;AAKX;AAEO,SAAS,eACd,QAAgB,EAChB,SAA+B;IAE/B,MAAM,MAAM,CAAA,GAAA,8SAAA,CAAA,aAAU,AAAD,EAAE;IACvB,OAAO,CAAA,GAAA,8SAAA,CAAA,UAAO,AAAD,EACX,IACE,CAAC,GAAG,CAAC,SAAS,IAAI,EAAE,EACjB,MAAM,CAAC,CAAC,KACP,GAAG,KAAK,KAAK,YAAY,YAAY,IAEtC,GAAG,CAAC,CAAC,KACJ,YAAY,UAAU,GAAG,MAAM,IAAK,GAAG,MAAM,GAEnD;QAAC;QAAK;QAAU;KAAU;AAE9B;AAGO,SAAS;IACd,MAAM,UAAU,CAAA,GAAA,8SAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;AAGO,SAAS,aAAa,EAAU;IACrC,MAAM,EAAE,YAAY,EAAE,GAAG;IACzB,OAAO,aAAa;AACtB;AAGO,SAAS;IACd,MAAM,EAAE,MAAM,EAAE,GAAG;IACnB,OAAO,OAAO,MAAM,CAAC,CAAA,QAAS,MAAM,MAAM;AAC5C;AAGO,SAAS;IACd,MAAM,UAAU,CAAA,GAAA,8SAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 944, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/types/layer-manager.ts"], "sourcesContent": ["import { UseMapReturn } from \"@geon-map/odf\";\r\nimport { LayerStyle } from \"./layer-style\";\r\nimport { DirectionsResponse } from \"@/types/tools\";\r\n\r\n// GeoJSON 타입 정의 (ODF 호환)\r\ninterface GeoJSONCoordinate extends Array<number> {\r\n  0: number; // longitude/x\r\n  1: number; // latitude/y\r\n}\r\n\r\ninterface GeoJSONLineString {\r\n  type: \"LineString\";\r\n  coordinates: GeoJSONCoordinate[];\r\n}\r\n\r\ninterface GeoJSONFeature {\r\n  type: \"Feature\";\r\n  geometry: GeoJSONLineString;\r\n  properties: {\r\n    name?: string;\r\n    distance?: number;\r\n    duration?: number;\r\n    toolCallId?: string;\r\n    [key: string]: any;\r\n  };\r\n}\r\n\r\ninterface GeoJSONFeatureCollection {\r\n  type: \"FeatureCollection\";\r\n  features: GeoJSONFeature[];\r\n}\r\n\r\n// 레이어 상태 관리를 위한 확장 타입\r\nexport interface ManagedLayerProps {\r\n  // 기본 식별자\r\n  id: string;\r\n  name?: string;\r\n  visible?: boolean;\r\n  opacity?: number;\r\n  zIndex?: number;\r\n  filter?: string;\r\n  style?: LayerStyle | any;\r\n  source?: 'tool-result' | 'user-added';\r\n  toolCallId?: string; // tool 결과와 연결\r\n  userModified?: boolean; // 사용자가 수동으로 수정했는지 여부\r\n  autoFit?: boolean;\r\n\r\n  // ODF LayerProps와 호환되는 필수 속성들\r\n  type: string;\r\n\r\n  // Geoserver 레이어 속성들 (선택적)\r\n  server?: string;\r\n  layer?: string;\r\n  service?: string;\r\n  bbox?: boolean;\r\n  method?: 'get' | 'post';\r\n  crtfckey?: string;\r\n  projection?: string;\r\n  geometryType?: string;\r\n  serviceTy?: string;\r\n\r\n  // GeoJSON 레이어 속성들 (선택적)\r\n  data?: any;\r\n  dataProjectionCode?: string;\r\n  featureProjectionCode?: string;\r\n\r\n  // 레이어 정보\r\n  info?: {\r\n    lyrId: string;\r\n    lyrNm: string;\r\n    description?: string;\r\n    metadata?: {\r\n      cntntsId: string;\r\n      jobClCode?: string;\r\n      lyrClCode: string;\r\n      lyrTySeCode: string;\r\n      namespace?: string;\r\n    };\r\n  };\r\n\r\n  // 기타 속성들\r\n  [key: string]: any;\r\n}\r\n\r\n// 레이어 액션 타입\r\nexport type LayerAction =\r\n  | { type: 'ADD_LAYER'; payload: ManagedLayerProps }\r\n  | { type: 'UPDATE_LAYER'; payload: { id: string; updates: Partial<ManagedLayerProps> } }\r\n  | { type: 'REMOVE_LAYER'; payload: string }\r\n  | { type: 'TOGGLE_VISIBILITY'; payload: string }\r\n  | { type: 'UPDATE_FILTER'; payload: { id: string; filter: string } }\r\n  | { type: 'UPDATE_STYLE'; payload: { id: string; style: LayerStyle | any } }\r\n  | { type: 'UPDATE_Z_INDEX'; payload: { id: string; zIndex: number } }\r\n  | { type: 'REORDER_LAYERS'; payload: { layerIds: string[] } }\r\n  | { type: 'SET_LAYERS'; payload: ManagedLayerProps[] };\r\n\r\n// Tool 결과에서 레이어로 변환하는 함수 타입\r\nexport type LayerTransformer<T = any> = (toolResult: T, toolCallId: string, mapState?: UseMapReturn) => ManagedLayerProps;\r\n\r\n// 레이어 관리자 컨텍스트 타입\r\nexport interface LayerManagerContext {\r\n  layers: ManagedLayerProps[];\r\n  addLayer: (layer: ManagedLayerProps) => void;\r\n  updateLayer: (id: string, updates: Partial<ManagedLayerProps>) => void;\r\n  removeLayer: (id: string) => void;\r\n  toggleVisibility: (id: string) => void;\r\n  updateFilter: (id: string, filter: string) => void;\r\n  updateStyle: (id: string, style: LayerStyle | any) => void;\r\n  updateZIndex: (id: string, zIndex: number) => void;\r\n  reorderLayers: (layerIds: string[]) => void;\r\n  getLayerById: (id: string) => ManagedLayerProps | undefined;\r\n}\r\n\r\n// Tool 결과 변환기들\r\nexport const layerTransformers: Record<string, LayerTransformer> = {\r\n  getLayer: (result: any, toolCallId: string, mapState?: UseMapReturn): ManagedLayerProps => {\r\n    // 서울 건물통합정보 레이어인 경우 autoFit을 false로 설정 (수동으로 지도 이동)\r\n    const isSeoulBuildingLayer = result.lyrId === \"LR0000004299\" ||\r\n                                result.name === \"GIS건물통합정보_서울\" ||\r\n                                result.lyrNm === \"GIS건물통합정보_서울\";\r\n\r\n    return {\r\n      id: result.lyrId || result.id || toolCallId,\r\n      name: result.lyrNm || result.name,\r\n      type: result.type || 'geoserver',\r\n      service: result.service,\r\n      server: result.server,\r\n      layer: result.layer,\r\n      crtfckey: result.crtfckey,\r\n      bbox: result.bbox,\r\n      autoFit: isSeoulBuildingLayer ? false : true,\r\n      method: result.method,\r\n      projection: result.projection,\r\n      geometryType: result.geometryType,\r\n      serviceTy: result.serviceTy,\r\n      visible: result.visible !== false, // 기본값 true\r\n      opacity: result.opacity ?? 1,\r\n      zIndex: result.zIndex ?? 0,\r\n      filter: result.filter,\r\n      style: result.style,\r\n      source: 'tool-result',\r\n      toolCallId,\r\n      info: result.info || {\r\n        lyrId: result.lyrId || result.id || toolCallId,\r\n        lyrNm: result.lyrNm || result.name || 'Unknown Layer',\r\n        description: result.description,\r\n        metadata: result.metadata\r\n      }\r\n    };\r\n  },\r\n\r\n  performDensityAnalysis: (result: any, toolCallId: string): ManagedLayerProps => ({\r\n    id: `density-${toolCallId}`,\r\n    name: `밀도 분석 - ${result.layerName || 'Unknown'}`,\r\n    type: \"geojson\",\r\n    data: { ...result },\r\n    service: \"heatmap\",\r\n    dataProjectionCode: \"EPSG:5186\",\r\n    featureProjectionCode: \"EPSG:5179\",\r\n    visible: true,\r\n    opacity: 0.8,\r\n    zIndex: 100,\r\n    source: 'tool-result',\r\n    toolCallId,\r\n    info: {\r\n      lyrId: `density-${toolCallId}`,\r\n      lyrNm: `밀도 분석 - ${result.layerName || 'Unknown'}`,\r\n      description: '밀도 분석 결과 레이어'\r\n    }\r\n  }),\r\n\r\n  searchDirections: (result: DirectionsResponse, toolCallId: string, mapState?: UseMapReturn): ManagedLayerProps => {\r\n    const route = result.routes?.[0];\r\n\r\n    // 거리와 시간 정보 포맷팅\r\n    const distanceText = route?.summary?.distance\r\n      ? `${(route.summary.distance / 1000).toFixed(1)}km`\r\n      : '거리 정보 없음';\r\n    const durationText = route?.summary?.duration\r\n      ? `${Math.floor(route.summary.duration / 60)}분`\r\n      : '시간 정보 없음';\r\n\r\n    // 기본 레이어 속성 설정\r\n    const layerProps: ManagedLayerProps = {\r\n      id: `route-${toolCallId}`,\r\n      name: `경로 - ${distanceText}`,\r\n      type: \"geojson\",\r\n      visible: true,\r\n      opacity: 0.8,\r\n      zIndex: 50,\r\n      source: 'tool-result',\r\n      toolCallId,\r\n      style: {\r\n        stroke: {\r\n          color: '#2563eb', // 파란색 경로 라인\r\n          width: 8\r\n        },\r\n        fill: { color: [255, 255, 255, 0.4] }, // 흰색 채우기\r\n      },\r\n      info: {\r\n        lyrId: `route-${toolCallId}`,\r\n        lyrNm: `경로 - ${distanceText}`,\r\n        description: `길찾기 결과 경로 (${durationText})`\r\n      }\r\n    };\r\n\r\n    // mapState가 있고 유효한 경로 데이터가 있는 경우 GeoJSON 생성\r\n    if (mapState?.map && route && route.result_code === 0 && route.sections) {\r\n      try {\r\n        const coordinates: GeoJSONCoordinate[] = [];\r\n        const projection = mapState.map.getProjection();\r\n\r\n        // 모든 섹션의 도로 정보에서 좌표 추출 및 변환\r\n        route.sections.forEach(section => {\r\n          section.roads?.forEach(road => {\r\n            if (road.vertexes && Array.isArray(road.vertexes) && road.vertexes.length >= 2) {\r\n              // vertexes는 [x1, y1, x2, y2, ...] 형태의 배열\r\n              for (let i = 0; i < road.vertexes.length; i += 2) {\r\n                const lon = road.vertexes[i];\r\n                const lat = road.vertexes[i + 1];\r\n\r\n                if (typeof lon === 'number' && typeof lat === 'number') {\r\n                  // 좌표계 변환: EPSG:4326 -> 지도 좌표계\r\n                  const projectedCoord = projection.project([lon, lat], \"4326\");\r\n                  if (Array.isArray(projectedCoord) && projectedCoord.length >= 2) {\r\n                    coordinates.push([projectedCoord[0], projectedCoord[1]] as GeoJSONCoordinate);\r\n                  }\r\n                }\r\n              }\r\n            }\r\n          });\r\n        });\r\n\r\n        // GeoJSON LineString 생성 (타입 안전성 보장)\r\n        if (coordinates.length > 0) {\r\n          const geoJsonData: GeoJSONFeatureCollection = {\r\n            type: \"FeatureCollection\",\r\n            features: [\r\n              {\r\n                type: \"Feature\",\r\n                geometry: {\r\n                  type: \"LineString\",\r\n                  coordinates: coordinates\r\n                },\r\n                properties: {\r\n                  name: \"경로\",\r\n                  distance: route.summary?.distance || 0,\r\n                  duration: route.summary?.duration || 0,\r\n                  toolCallId\r\n                }\r\n              }\r\n            ]\r\n          };\r\n\r\n          layerProps.data = geoJsonData;\r\n        }\r\n      } catch (error) {\r\n        layerProps.data = result;\r\n      }\r\n    } else {\r\n      // mapState가 없거나 유효하지 않은 경우 원본 데이터 사용\r\n      layerProps.data = result;\r\n    }\r\n\r\n    return layerProps;\r\n  },\r\n};\r\n"], "names": [], "mappings": ";;;AAkHO,MAAM,oBAAsD;IACjE,UAAU,CAAC,QAAa,YAAoB;QAC1C,oDAAoD;QACpD,MAAM,uBAAuB,OAAO,KAAK,KAAK,kBAClB,OAAO,IAAI,KAAK,kBAChB,OAAO,KAAK,KAAK;QAE7C,OAAO;YACL,IAAI,OAAO,KAAK,IAAI,OAAO,EAAE,IAAI;YACjC,MAAM,OAAO,KAAK,IAAI,OAAO,IAAI;YACjC,MAAM,OAAO,IAAI,IAAI;YACrB,SAAS,OAAO,OAAO;YACvB,QAAQ,OAAO,MAAM;YACrB,OAAO,OAAO,KAAK;YACnB,UAAU,OAAO,QAAQ;YACzB,MAAM,OAAO,IAAI;YACjB,SAAS,uBAAuB,QAAQ;YACxC,QAAQ,OAAO,MAAM;YACrB,YAAY,OAAO,UAAU;YAC7B,cAAc,OAAO,YAAY;YACjC,WAAW,OAAO,SAAS;YAC3B,SAAS,OAAO,OAAO,KAAK;YAC5B,SAAS,OAAO,OAAO,IAAI;YAC3B,QAAQ,OAAO,MAAM,IAAI;YACzB,QAAQ,OAAO,MAAM;YACrB,OAAO,OAAO,KAAK;YACnB,QAAQ;YACR;YACA,MAAM,OAAO,IAAI,IAAI;gBACnB,OAAO,OAAO,KAAK,IAAI,OAAO,EAAE,IAAI;gBACpC,OAAO,OAAO,KAAK,IAAI,OAAO,IAAI,IAAI;gBACtC,aAAa,OAAO,WAAW;gBAC/B,UAAU,OAAO,QAAQ;YAC3B;QACF;IACF;IAEA,wBAAwB,CAAC,QAAa,aAA0C,CAAC;YAC/E,IAAI,CAAC,QAAQ,EAAE,YAAY;YAC3B,MAAM,CAAC,QAAQ,EAAE,OAAO,SAAS,IAAI,WAAW;YAChD,MAAM;YACN,MAAM;gBAAE,GAAG,MAAM;YAAC;YAClB,SAAS;YACT,oBAAoB;YACpB,uBAAuB;YACvB,SAAS;YACT,SAAS;YACT,QAAQ;YACR,QAAQ;YACR;YACA,MAAM;gBACJ,OAAO,CAAC,QAAQ,EAAE,YAAY;gBAC9B,OAAO,CAAC,QAAQ,EAAE,OAAO,SAAS,IAAI,WAAW;gBACjD,aAAa;YACf;QACF,CAAC;IAED,kBAAkB,CAAC,QAA4B,YAAoB;QACjE,MAAM,QAAQ,OAAO,MAAM,EAAE,CAAC,EAAE;QAEhC,gBAAgB;QAChB,MAAM,eAAe,OAAO,SAAS,WACjC,GAAG,CAAC,MAAM,OAAO,CAAC,QAAQ,GAAG,IAAI,EAAE,OAAO,CAAC,GAAG,EAAE,CAAC,GACjD;QACJ,MAAM,eAAe,OAAO,SAAS,WACjC,GAAG,KAAK,KAAK,CAAC,MAAM,OAAO,CAAC,QAAQ,GAAG,IAAI,CAAC,CAAC,GAC7C;QAEJ,eAAe;QACf,MAAM,aAAgC;YACpC,IAAI,CAAC,MAAM,EAAE,YAAY;YACzB,MAAM,CAAC,KAAK,EAAE,cAAc;YAC5B,MAAM;YACN,SAAS;YACT,SAAS;YACT,QAAQ;YACR,QAAQ;YACR;YACA,OAAO;gBACL,QAAQ;oBACN,OAAO;oBACP,OAAO;gBACT;gBACA,MAAM;oBAAE,OAAO;wBAAC;wBAAK;wBAAK;wBAAK;qBAAI;gBAAC;YACtC;YACA,MAAM;gBACJ,OAAO,CAAC,MAAM,EAAE,YAAY;gBAC5B,OAAO,CAAC,KAAK,EAAE,cAAc;gBAC7B,aAAa,CAAC,WAAW,EAAE,aAAa,CAAC,CAAC;YAC5C;QACF;QAEA,4CAA4C;QAC5C,IAAI,UAAU,OAAO,SAAS,MAAM,WAAW,KAAK,KAAK,MAAM,QAAQ,EAAE;YACvE,IAAI;gBACF,MAAM,cAAmC,EAAE;gBAC3C,MAAM,aAAa,SAAS,GAAG,CAAC,aAAa;gBAE7C,4BAA4B;gBAC5B,MAAM,QAAQ,CAAC,OAAO,CAAC,CAAA;oBACrB,QAAQ,KAAK,EAAE,QAAQ,CAAA;wBACrB,IAAI,KAAK,QAAQ,IAAI,MAAM,OAAO,CAAC,KAAK,QAAQ,KAAK,KAAK,QAAQ,CAAC,MAAM,IAAI,GAAG;4BAC9E,yCAAyC;4BACzC,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,QAAQ,CAAC,MAAM,EAAE,KAAK,EAAG;gCAChD,MAAM,MAAM,KAAK,QAAQ,CAAC,EAAE;gCAC5B,MAAM,MAAM,KAAK,QAAQ,CAAC,IAAI,EAAE;gCAEhC,IAAI,OAAO,QAAQ,YAAY,OAAO,QAAQ,UAAU;oCACtD,8BAA8B;oCAC9B,MAAM,iBAAiB,WAAW,OAAO,CAAC;wCAAC;wCAAK;qCAAI,EAAE;oCACtD,IAAI,MAAM,OAAO,CAAC,mBAAmB,eAAe,MAAM,IAAI,GAAG;wCAC/D,YAAY,IAAI,CAAC;4CAAC,cAAc,CAAC,EAAE;4CAAE,cAAc,CAAC,EAAE;yCAAC;oCACzD;gCACF;4BACF;wBACF;oBACF;gBACF;gBAEA,oCAAoC;gBACpC,IAAI,YAAY,MAAM,GAAG,GAAG;oBAC1B,MAAM,cAAwC;wBAC5C,MAAM;wBACN,UAAU;4BACR;gCACE,MAAM;gCACN,UAAU;oCACR,MAAM;oCACN,aAAa;gCACf;gCACA,YAAY;oCACV,MAAM;oCACN,UAAU,MAAM,OAAO,EAAE,YAAY;oCACrC,UAAU,MAAM,OAAO,EAAE,YAAY;oCACrC;gCACF;4BACF;yBACD;oBACH;oBAEA,WAAW,IAAI,GAAG;gBACpB;YACF,EAAE,OAAO,OAAO;gBACd,WAAW,IAAI,GAAG;YACpB;QACF,OAAO;YACL,qCAAqC;YACrC,WAAW,IAAI,GAAG;QACpB;QAEA,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 1104, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/types/layer-style.ts"], "sourcesContent": ["// WMS/WFS 레이어 스타일 타입 정의\n\n// 기본 필터 타입\nexport type FilterOperator = '==' | '!=' | '>' | '<' | '>=' | '<=' | '*=' | '&&' | '||';\n\nexport interface BasicFilter {\n  operator: FilterOperator;\n  column: string;\n  value: string | number | null;\n}\n\nexport interface LikeFilter {\n  operator: '*=';\n  column: string;\n  value: string; // wildCard=\"*\" singleChar=\".\" escape=\"!\"\n}\n\nexport interface LogicalFilter {\n  operator: '&&' | '||';\n  conditions: [BasicFilter | LikeFilter, BasicFilter | LikeFilter, ...Array<BasicFilter | LikeFilter>];\n}\n\nexport type StyleFilter = BasicFilter | LikeFilter | LogicalFilter;\n\n// 스케일 범위 타입\nexport interface ScaleDenominator {\n  min?: number;\n  max?: number;\n}\n\n// 점(Point) 스타일 심볼라이저\nexport interface MarkSymbolizer {\n  kind: 'Mark';\n  wellKnownName: 'circle' | 'square' | 'triangle' | 'star' | 'cross' | 'x' | 'Square';\n  radius: number;\n  color: string; // rgba 값 입력시 자동변환\n  fillOpacity: number; // 0~1\n  strokeColor?: string; // rgba 값 입력시 자동변환\n  strokeOpacity?: number; // 0~1\n  strokeWidth?: number;\n  offset?: [number, number]; // [x이동량, y이동량]\n  offsetGeometry?: string; // offset 적용 시킬 geometry 타입 칼럼명\n}\n\n// 선(Line) 스타일 심볼라이저\nexport interface LineSymbolizer {\n  kind: 'Line';\n  color: string; // rgba 값 입력시 자동변환\n  cap?: 'butt' | 'round' | 'square'; // 라인의 끝 표현 방식\n  join?: 'mitre' | 'round' | 'bevel'; // 라인이 꺾이는 부분 표현 방식\n  opacity: number; // 0~1\n  width: number;\n  dasharray?: number[]; // 대시 간격 조절\n  dashOffset?: number; // 선의 시작점에서 얼마나 떨어진 곳에서부터 점선을 표시할지\n}\n\n// 면(Polygon) 스타일 심볼라이저\nexport interface FillSymbolizer {\n  kind: 'Fill';\n  color: string; // rgba 값 입력시 자동변환\n  fillOpacity: number; // 0~1\n  outlineColor?: string; // rgba 값 입력시 자동변환\n  outlineWidth?: number;\n  outlineOpacity?: number; // 0~1\n  outlineDasharray?: number[]; // 윤곽선 대쉬 간격\n}\n\n// 심볼라이저 유니온 타입\nexport type Symbolizer = MarkSymbolizer | LineSymbolizer | FillSymbolizer;\n\n// 스타일 룰\nexport interface StyleRule {\n  name: string;\n  scaleDenominator?: ScaleDenominator;\n  filter?: StyleFilter;\n  symbolizers: Symbolizer[];\n}\n\n// WMS 레이어 스타일 (SLD 방식)\nexport interface WMSLayerStyle {\n  rules: StyleRule[];\n}\n\n// WFS 레이어 스타일 (Flat Style 방식)\nexport interface WFSLayerStyle {\n  'stroke-color'?: string;\n  'stroke-width'?: number;\n  'stroke-opacity'?: number;\n  'fill-color'?: string;\n  'fill-opacity'?: number;\n  'circle-radius'?: number;\n  'circle-fill-color'?: string;\n  'circle-fill-opacity'?: number;\n  'circle-stroke-color'?: string;\n  'circle-stroke-width'?: number;\n  'circle-stroke-opacity'?: number;\n}\n\n// 레이어 타입별 스타일 유니온\nexport type LayerStyle = WMSLayerStyle | WFSLayerStyle;\n\n// 레이어 렌더 옵션\nexport interface LayerRenderOptions {\n  style: LayerStyle;\n}\n\n// 지오메트리 타입별 기본 스타일 생성 헬퍼\nexport const createDefaultPointStyle = (options?: Partial<MarkSymbolizer>): WMSLayerStyle => ({\n  rules: [{\n    name: 'Default Point Rule',\n    symbolizers: [{\n      kind: 'Mark',\n      wellKnownName: 'circle',\n      radius: 6,\n      color: '#FF0000',\n      fillOpacity: 0.8,\n      strokeColor: '#000000',\n      strokeWidth: 1,\n      strokeOpacity: 1,\n      ...options\n    }]\n  }]\n});\n\nexport const createDefaultLineStyle = (options?: Partial<LineSymbolizer>): WMSLayerStyle => ({\n  rules: [{\n    name: 'Default Line Rule',\n    symbolizers: [{\n      kind: 'Line',\n      color: '#0000FF',\n      width: 2,\n      opacity: 1,\n      cap: 'round',\n      join: 'round',\n      ...options\n    }]\n  }]\n});\n\nexport const createDefaultPolygonStyle = (options?: Partial<FillSymbolizer>): WMSLayerStyle => ({\n  rules: [{\n    name: 'Default Polygon Rule',\n    symbolizers: [{\n      \n      kind: 'Fill',\n      color: '#AAAAAA',\n      fillOpacity: 0.5,\n      outlineColor: '#000000',\n      outlineWidth: 1,\n      outlineOpacity: 1,\n      ...options\n    }]\n  }]\n});\n\n// WFS 레이어용 기본 스타일 생성 헬퍼\nexport const createDefaultWFSPointStyle = (options?: Partial<WFSLayerStyle>): WFSLayerStyle => ({\n  'circle-radius': 6,\n  'circle-fill-color': '#FF0000',\n  'circle-fill-opacity': 0.8,\n  'circle-stroke-color': '#000000',\n  'circle-stroke-width': 1,\n  'circle-stroke-opacity': 1,\n  ...options\n});\n\nexport const createDefaultWFSLineStyle = (options?: Partial<WFSLayerStyle>): WFSLayerStyle => ({\n  'stroke-color': '#0000FF',\n  'stroke-width': 2,\n  'stroke-opacity': 1,\n  ...options\n});\n\nexport const createDefaultWFSPolygonStyle = (options?: Partial<WFSLayerStyle>): WFSLayerStyle => ({\n  'fill-color': '#00FF00',\n  'fill-opacity': 0.5,\n  'stroke-color': '#000000',\n  'stroke-width': 1,\n  'stroke-opacity': 1,\n  ...options\n});\n"], "names": [], "mappings": "AAAA,wBAAwB;AAExB,WAAW;;;;;;;;;AAyGJ,MAAM,0BAA0B,CAAC,UAAqD,CAAC;QAC5F,OAAO;YAAC;gBACN,MAAM;gBACN,aAAa;oBAAC;wBACZ,MAAM;wBACN,eAAe;wBACf,QAAQ;wBACR,OAAO;wBACP,aAAa;wBACb,aAAa;wBACb,aAAa;wBACb,eAAe;wBACf,GAAG,OAAO;oBACZ;iBAAE;YACJ;SAAE;IACJ,CAAC;AAEM,MAAM,yBAAyB,CAAC,UAAqD,CAAC;QAC3F,OAAO;YAAC;gBACN,MAAM;gBACN,aAAa;oBAAC;wBACZ,MAAM;wBACN,OAAO;wBACP,OAAO;wBACP,SAAS;wBACT,KAAK;wBACL,MAAM;wBACN,GAAG,OAAO;oBACZ;iBAAE;YACJ;SAAE;IACJ,CAAC;AAEM,MAAM,4BAA4B,CAAC,UAAqD,CAAC;QAC9F,OAAO;YAAC;gBACN,MAAM;gBACN,aAAa;oBAAC;wBAEZ,MAAM;wBACN,OAAO;wBACP,aAAa;wBACb,cAAc;wBACd,cAAc;wBACd,gBAAgB;wBAChB,GAAG,OAAO;oBACZ;iBAAE;YACJ;SAAE;IACJ,CAAC;AAGM,MAAM,6BAA6B,CAAC,UAAoD,CAAC;QAC9F,iBAAiB;QACjB,qBAAqB;QACrB,uBAAuB;QACvB,uBAAuB;QACvB,uBAAuB;QACvB,yBAAyB;QACzB,GAAG,OAAO;IACZ,CAAC;AAEM,MAAM,4BAA4B,CAAC,UAAoD,CAAC;QAC7F,gBAAgB;QAChB,gBAAgB;QAChB,kBAAkB;QAClB,GAAG,OAAO;IACZ,CAAC;AAEM,MAAM,+BAA+B,CAAC,UAAoD,CAAC;QAChG,cAAc;QACd,gBAAgB;QAChB,gBAAgB;QAChB,gBAAgB;QAChB,kBAAkB;QAClB,GAAG,OAAO;IACZ,CAAC", "debugId": null}}, {"offset": {"line": 1199, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/lib/map-utils.ts"], "sourcesContent": ["//@ts-nocheck\r\nimport { LayerConfig, LayerInfo, UseMapReturn } from \"@geon-map/odf\";\r\n\r\n// 하이라이트 레이어 관리를 위한 전역 변수\r\nlet HIGHLIGHT_LAYER_ID: string | null = null;\r\n\r\n/**\r\n * 지도에 포인트를 하이라이트하고 이동하는 함수 (레거시 패턴 적용)\r\n */\r\nexport const highlightPointOnMap = (\r\n  mapState: UseMapReturn,\r\n  longitude: number,\r\n  latitude: number,\r\n  geometry?: string,\r\n  zoomLevel: number = 16,\r\n  options?: {\r\n    clearPrevious?: boolean;\r\n    useZIndexUp?: boolean;\r\n    customStyle?: any;\r\n    fitPadding?: number;\r\n  }\r\n): boolean => {\r\n  if (!mapState?.map) {\r\n    return false;\r\n  }\r\n\r\n  try {\r\n    const odf = (window as any).odf;\r\n    if (!odf) {\r\n      return false;\r\n    }\r\n\r\n    // 하이라이트 레이어 재사용 또는 생성 (레거시 패턴)\r\n    let highlightLayer = HIGHLIGHT_LAYER_ID\r\n      ? mapState.map.findLayer(HIGHLIGHT_LAYER_ID)\r\n      : null;\r\n\r\n    if (!highlightLayer) {\r\n      highlightLayer = odf.LayerFactory.produce(\"empty\", {});\r\n      highlightLayer.setMap(mapState.map);\r\n      HIGHLIGHT_LAYER_ID = highlightLayer.getODFId();\r\n\r\n      // 기본 스타일 적용\r\n      const defaultStyle = options?.customStyle || {\r\n        image: {\r\n          circle: {\r\n            radius: 10,\r\n            fill: { color: [255, 255, 255, 0.4] },\r\n            stroke: { color: [237, 116, 116, 0.82], width: 2 },\r\n          },\r\n        },\r\n        fill: { color: [255, 255, 255, 0.4] },\r\n        stroke: { color: [237, 116, 116, 0.82], width: 2 },\r\n      };\r\n      highlightLayer.setStyle(odf.StyleFactory.produce(defaultStyle));\r\n    }\r\n\r\n    // 기존 하이라이트 제거 (옵션)\r\n    if (options?.clearPrevious !== false) {\r\n      highlightLayer.clear();\r\n    }\r\n\r\n    // 지오메트리가 있는 경우 사용, 없으면 포인트 생성\r\n    let wkt = geometry;\r\n    if (!wkt) {\r\n      wkt = `POINT(${longitude} ${latitude})`;\r\n    }\r\n\r\n    // WKT에서 피처 생성\r\n    let feature = odf.FeatureFactory.fromWKT(wkt);\r\n\r\n    // 좌표계 변환 처리 (레거시 패턴)\r\n    const mapProjectionCode = mapState.map.getView().getProjection().getCode();\r\n    const targetSrid = mapProjectionCode.replace(/[^0-9]/gi, \"\");\r\n    if (targetSrid !== \"4326\") {\r\n      feature = mapState.map.getProjection().projectGeom(feature, \"4326\");\r\n    }\r\n\r\n    highlightLayer.addFeature(feature);\r\n\r\n    // Z-Index 관리 (레거시 패턴)\r\n    if (options?.useZIndexUp) {\r\n      const maxZIndex = mapState.map.getMaxZIndex();\r\n      mapState.map.setZIndex(HIGHLIGHT_LAYER_ID, maxZIndex + 1);\r\n    }\r\n\r\n    // 지오메트리 타입별 줌 레벨 조정 (레거시 패턴 개선)\r\n    const geometryType = feature.getGeometry().getType();\r\n    if (geometry) {\r\n      // 복잡한 지오메트리는 fit 사용\r\n      const padding = options?.fitPadding || 1000;\r\n      highlightLayer.fit();\r\n    }\r\n    mapState.map.setZoom(\r\n      mapState.map.getZoom() > 13 ? 13 :  mapState.map.getZoom()\r\n    ); //바로 e맵 영상 최대 레벨 19\r\n\r\n    return true;\r\n  } catch (error) {\r\n    console.error(\"지도 하이라이트 중 오류:\", error);\r\n    return false;\r\n  }\r\n};\r\n\r\n/**\r\n * 지도에 경로를 표시하는 함수 (길찾기 예시 참고하여 개선)\r\n */\r\nexport const showRouteOnMap = (\r\n  mapState: UseMapReturn,\r\n  origin: { x: number; y: number },\r\n  destination: { x: number; y: number },\r\n  waypoints?: Array<{ x: number; y: number }>,\r\n  routeData?: any // 실제 경로 데이터 (sections 포함)\r\n): boolean => {\r\n  if (!mapState?.map) {\r\n    return false;\r\n  }\r\n\r\n  try {\r\n    const odf = (window as any).odf;\r\n    if (!odf) {\r\n      return false;\r\n    }\r\n\r\n    // 하이라이트 레이어 재사용 또는 생성\r\n    let highlightLayer = HIGHLIGHT_LAYER_ID\r\n      ? mapState.map.findLayer(HIGHLIGHT_LAYER_ID)\r\n      : null;\r\n\r\n    if (!highlightLayer) {\r\n      highlightLayer = odf.LayerFactory.produce(\"empty\", {});\r\n      highlightLayer.setMap(mapState.map);\r\n      HIGHLIGHT_LAYER_ID = highlightLayer.getODFId();\r\n    }\r\n\r\n    // 기존 하이라이트 제거\r\n    highlightLayer.clear();\r\n\r\n    const projection = mapState.map.getProjection();\r\n\r\n    // 실제 경로 라인 그리기 (제공된 예제 참고)\r\n    if (\r\n      routeData &&\r\n      routeData.routes &&\r\n      routeData.routes[0] &&\r\n      routeData.routes[0].sections\r\n    ) {\r\n      const coordinates: number[][] = [];\r\n\r\n      // 첫 번째 섹션의 모든 도로를 순회\r\n      routeData.routes[0].sections.forEach((section: any) => {\r\n        if (section.roads) {\r\n          section.roads.forEach((router: { vertexes: any[] }) => {\r\n            router.vertexes.forEach((vertex, index) => {\r\n              if (index % 2 === 0) {\r\n                coordinates.push(\r\n                  projection.project(\r\n                    [router.vertexes[index], router.vertexes[index + 1]],\r\n                    \"4326\"\r\n                  )\r\n                );\r\n              }\r\n            });\r\n          });\r\n        }\r\n      });\r\n\r\n      // GeoJSON LineString으로 경로 표시\r\n      if (coordinates.length > 1) {\r\n        const lineWKT = `LINESTRING(${coordinates\r\n          .map((coord) => `${coord[0]} ${coord[1]}`)\r\n          .join(\", \")})`;\r\n        let lineFeature = odf.FeatureFactory.fromWKT(lineWKT);\r\n        highlightLayer.addFeature(lineFeature);\r\n      }\r\n    }\r\n\r\n    // 출발지 표시 (파란색 원)\r\n    const originWKT = `POINT(${origin.x} ${origin.y})`;\r\n    let originFeature = odf.FeatureFactory.fromWKT(originWKT);\r\n    originFeature = projection.projectGeom(originFeature, \"4326\");\r\n    highlightLayer.addFeature(originFeature);\r\n\r\n    // 도착지 표시 (빨간색 원)\r\n    const destWKT = `POINT(${destination.x} ${destination.y})`;\r\n    let destFeature = odf.FeatureFactory.fromWKT(destWKT);\r\n    destFeature = projection.projectGeom(destFeature, \"4326\");\r\n    highlightLayer.addFeature(destFeature);\r\n\r\n    // 경유지가 있는 경우 표시\r\n    if (waypoints && waypoints.length > 0) {\r\n      waypoints.forEach((waypoint) => {\r\n        const waypointWKT = `POINT(${waypoint.x} ${waypoint.y})`;\r\n        let waypointFeature = odf.FeatureFactory.fromWKT(waypointWKT);\r\n        waypointFeature = projection.projectGeom(waypointFeature, \"4326\");\r\n        highlightLayer.addFeature(waypointFeature);\r\n      });\r\n    }\r\n\r\n    // 전체 경로가 보이도록 fit\r\n    highlightLayer.fit();\r\n\r\n    return true;\r\n  } catch (error) {\r\n    console.error(\"경로 표시 중 오류:\", error);\r\n    return false;\r\n  }\r\n};\r\n\r\n/**\r\n * 지도에 레이어를 추가하는 함수\r\n */\r\nexport const addLayerToMap = async (\r\n  mapState: UseMapReturn,\r\n  layerInfo: LayerInfo,\r\n  layerConfig: LayerConfig\r\n): Promise<boolean> => {\r\n  if (!mapState?.layer) {\r\n    return false;\r\n  }\r\n\r\n  try {\r\n    const addedLayer = await mapState.layer.add(layerInfo, layerConfig);\r\n    return !!addedLayer;\r\n  } catch (error) {\r\n    console.error(\"레이어 추가 중 오류:\", error);\r\n    return false;\r\n  }\r\n};\r\n\r\n/**\r\n * 거리에 따른 적절한 줌 레벨 계산\r\n */\r\nexport const calculateZoomLevel = (distance: number): number => {\r\n  if (distance > 50000) return 8;\r\n  if (distance > 20000) return 10;\r\n  if (distance > 5000) return 12;\r\n  if (distance > 1000) return 14;\r\n  return 16;\r\n};\r\n\r\n/**\r\n * 지도 하이라이트 레이어 초기화 (레거시 패턴 적용)\r\n */\r\nexport const clearMapHighlights = (mapState: UseMapReturn): boolean => {\r\n  if (!mapState?.map) {\r\n    return false;\r\n  }\r\n\r\n  try {\r\n    if (HIGHLIGHT_LAYER_ID) {\r\n      const highlightLayer = mapState.map.findLayer(HIGHLIGHT_LAYER_ID);\r\n      if (highlightLayer) {\r\n        highlightLayer.clear();\r\n      }\r\n    }\r\n    return true;\r\n  } catch (error) {\r\n    console.error(\"하이라이트 초기화 중 오류:\", error);\r\n    return false;\r\n  }\r\n};\r\n\r\n/**\r\n * 하이라이트 레이어 완전 제거 (레거시 패턴)\r\n */\r\nexport const removeHighlightLayer = (mapState: UseMapReturn): boolean => {\r\n  if (!mapState?.map) {\r\n    return false;\r\n  }\r\n\r\n  try {\r\n    if (HIGHLIGHT_LAYER_ID) {\r\n      const highlightLayer = mapState.map.findLayer(HIGHLIGHT_LAYER_ID);\r\n      if (highlightLayer) {\r\n        highlightLayer.removeMap(mapState.map);\r\n        HIGHLIGHT_LAYER_ID = null;\r\n      }\r\n    }\r\n    return true;\r\n  } catch (error) {\r\n    console.error(\"하이라이트 레이어 제거 중 오류:\", error);\r\n    return false;\r\n  }\r\n};\r\n\r\n// UseMapReturn은 @geon-map/odf에서 import하여 재export\r\nexport type { UseMapReturn };\r\n"], "names": [], "mappings": "AAAA,aAAa;;;;;;;;;AAGb,yBAAyB;AACzB,IAAI,qBAAoC;AAKjC,MAAM,sBAAsB,CACjC,UACA,WACA,UACA,UACA,YAAoB,EAAE,EACtB;IAOA,IAAI,CAAC,UAAU,KAAK;QAClB,OAAO;IACT;IAEA,IAAI;QACF,MAAM,MAAM,AAAC,OAAe,GAAG;QAC/B,IAAI,CAAC,KAAK;YACR,OAAO;QACT;QAEA,+BAA+B;QAC/B,IAAI,iBAAiB,qBACjB,SAAS,GAAG,CAAC,SAAS,CAAC,sBACvB;QAEJ,IAAI,CAAC,gBAAgB;YACnB,iBAAiB,IAAI,YAAY,CAAC,OAAO,CAAC,SAAS,CAAC;YACpD,eAAe,MAAM,CAAC,SAAS,GAAG;YAClC,qBAAqB,eAAe,QAAQ;YAE5C,YAAY;YACZ,MAAM,eAAe,SAAS,eAAe;gBAC3C,OAAO;oBACL,QAAQ;wBACN,QAAQ;wBACR,MAAM;4BAAE,OAAO;gCAAC;gCAAK;gCAAK;gCAAK;6BAAI;wBAAC;wBACpC,QAAQ;4BAAE,OAAO;gCAAC;gCAAK;gCAAK;gCAAK;6BAAK;4BAAE,OAAO;wBAAE;oBACnD;gBACF;gBACA,MAAM;oBAAE,OAAO;wBAAC;wBAAK;wBAAK;wBAAK;qBAAI;gBAAC;gBACpC,QAAQ;oBAAE,OAAO;wBAAC;wBAAK;wBAAK;wBAAK;qBAAK;oBAAE,OAAO;gBAAE;YACnD;YACA,eAAe,QAAQ,CAAC,IAAI,YAAY,CAAC,OAAO,CAAC;QACnD;QAEA,mBAAmB;QACnB,IAAI,SAAS,kBAAkB,OAAO;YACpC,eAAe,KAAK;QACtB;QAEA,8BAA8B;QAC9B,IAAI,MAAM;QACV,IAAI,CAAC,KAAK;YACR,MAAM,CAAC,MAAM,EAAE,UAAU,CAAC,EAAE,SAAS,CAAC,CAAC;QACzC;QAEA,cAAc;QACd,IAAI,UAAU,IAAI,cAAc,CAAC,OAAO,CAAC;QAEzC,qBAAqB;QACrB,MAAM,oBAAoB,SAAS,GAAG,CAAC,OAAO,GAAG,aAAa,GAAG,OAAO;QACxE,MAAM,aAAa,kBAAkB,OAAO,CAAC,YAAY;QACzD,IAAI,eAAe,QAAQ;YACzB,UAAU,SAAS,GAAG,CAAC,aAAa,GAAG,WAAW,CAAC,SAAS;QAC9D;QAEA,eAAe,UAAU,CAAC;QAE1B,sBAAsB;QACtB,IAAI,SAAS,aAAa;YACxB,MAAM,YAAY,SAAS,GAAG,CAAC,YAAY;YAC3C,SAAS,GAAG,CAAC,SAAS,CAAC,oBAAoB,YAAY;QACzD;QAEA,gCAAgC;QAChC,MAAM,eAAe,QAAQ,WAAW,GAAG,OAAO;QAClD,IAAI,UAAU;YACZ,oBAAoB;YACpB,MAAM,UAAU,SAAS,cAAc;YACvC,eAAe,GAAG;QACpB;QACA,SAAS,GAAG,CAAC,OAAO,CAClB,SAAS,GAAG,CAAC,OAAO,KAAK,KAAK,KAAM,SAAS,GAAG,CAAC,OAAO,KACvD,mBAAmB;QAEtB,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kBAAkB;QAChC,OAAO;IACT;AACF;AAKO,MAAM,iBAAiB,CAC5B,UACA,QACA,aACA,WACA,UAAgB,0BAA0B;;IAE1C,IAAI,CAAC,UAAU,KAAK;QAClB,OAAO;IACT;IAEA,IAAI;QACF,MAAM,MAAM,AAAC,OAAe,GAAG;QAC/B,IAAI,CAAC,KAAK;YACR,OAAO;QACT;QAEA,sBAAsB;QACtB,IAAI,iBAAiB,qBACjB,SAAS,GAAG,CAAC,SAAS,CAAC,sBACvB;QAEJ,IAAI,CAAC,gBAAgB;YACnB,iBAAiB,IAAI,YAAY,CAAC,OAAO,CAAC,SAAS,CAAC;YACpD,eAAe,MAAM,CAAC,SAAS,GAAG;YAClC,qBAAqB,eAAe,QAAQ;QAC9C;QAEA,cAAc;QACd,eAAe,KAAK;QAEpB,MAAM,aAAa,SAAS,GAAG,CAAC,aAAa;QAE7C,2BAA2B;QAC3B,IACE,aACA,UAAU,MAAM,IAChB,UAAU,MAAM,CAAC,EAAE,IACnB,UAAU,MAAM,CAAC,EAAE,CAAC,QAAQ,EAC5B;YACA,MAAM,cAA0B,EAAE;YAElC,qBAAqB;YACrB,UAAU,MAAM,CAAC,EAAE,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;gBACpC,IAAI,QAAQ,KAAK,EAAE;oBACjB,QAAQ,KAAK,CAAC,OAAO,CAAC,CAAC;wBACrB,OAAO,QAAQ,CAAC,OAAO,CAAC,CAAC,QAAQ;4BAC/B,IAAI,QAAQ,MAAM,GAAG;gCACnB,YAAY,IAAI,CACd,WAAW,OAAO,CAChB;oCAAC,OAAO,QAAQ,CAAC,MAAM;oCAAE,OAAO,QAAQ,CAAC,QAAQ,EAAE;iCAAC,EACpD;4BAGN;wBACF;oBACF;gBACF;YACF;YAEA,6BAA6B;YAC7B,IAAI,YAAY,MAAM,GAAG,GAAG;gBAC1B,MAAM,UAAU,CAAC,WAAW,EAAE,YAC3B,GAAG,CAAC,CAAC,QAAU,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE,KAAK,CAAC,EAAE,EAAE,EACxC,IAAI,CAAC,MAAM,CAAC,CAAC;gBAChB,IAAI,cAAc,IAAI,cAAc,CAAC,OAAO,CAAC;gBAC7C,eAAe,UAAU,CAAC;YAC5B;QACF;QAEA,iBAAiB;QACjB,MAAM,YAAY,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC;QAClD,IAAI,gBAAgB,IAAI,cAAc,CAAC,OAAO,CAAC;QAC/C,gBAAgB,WAAW,WAAW,CAAC,eAAe;QACtD,eAAe,UAAU,CAAC;QAE1B,iBAAiB;QACjB,MAAM,UAAU,CAAC,MAAM,EAAE,YAAY,CAAC,CAAC,CAAC,EAAE,YAAY,CAAC,CAAC,CAAC,CAAC;QAC1D,IAAI,cAAc,IAAI,cAAc,CAAC,OAAO,CAAC;QAC7C,cAAc,WAAW,WAAW,CAAC,aAAa;QAClD,eAAe,UAAU,CAAC;QAE1B,gBAAgB;QAChB,IAAI,aAAa,UAAU,MAAM,GAAG,GAAG;YACrC,UAAU,OAAO,CAAC,CAAC;gBACjB,MAAM,cAAc,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC;gBACxD,IAAI,kBAAkB,IAAI,cAAc,CAAC,OAAO,CAAC;gBACjD,kBAAkB,WAAW,WAAW,CAAC,iBAAiB;gBAC1D,eAAe,UAAU,CAAC;YAC5B;QACF;QAEA,kBAAkB;QAClB,eAAe,GAAG;QAElB,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,eAAe;QAC7B,OAAO;IACT;AACF;AAKO,MAAM,gBAAgB,OAC3B,UACA,WACA;IAEA,IAAI,CAAC,UAAU,OAAO;QACpB,OAAO;IACT;IAEA,IAAI;QACF,MAAM,aAAa,MAAM,SAAS,KAAK,CAAC,GAAG,CAAC,WAAW;QACvD,OAAO,CAAC,CAAC;IACX,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gBAAgB;QAC9B,OAAO;IACT;AACF;AAKO,MAAM,qBAAqB,CAAC;IACjC,IAAI,WAAW,OAAO,OAAO;IAC7B,IAAI,WAAW,OAAO,OAAO;IAC7B,IAAI,WAAW,MAAM,OAAO;IAC5B,IAAI,WAAW,MAAM,OAAO;IAC5B,OAAO;AACT;AAKO,MAAM,qBAAqB,CAAC;IACjC,IAAI,CAAC,UAAU,KAAK;QAClB,OAAO;IACT;IAEA,IAAI;QACF,IAAI,oBAAoB;YACtB,MAAM,iBAAiB,SAAS,GAAG,CAAC,SAAS,CAAC;YAC9C,IAAI,gBAAgB;gBAClB,eAAe,KAAK;YACtB;QACF;QACA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,mBAAmB;QACjC,OAAO;IACT;AACF;AAKO,MAAM,uBAAuB,CAAC;IACnC,IAAI,CAAC,UAAU,KAAK;QAClB,OAAO;IACT;IAEA,IAAI;QACF,IAAI,oBAAoB;YACtB,MAAM,iBAAiB,SAAS,GAAG,CAAC,SAAS,CAAC;YAC9C,IAAI,gBAAgB;gBAClB,eAAe,SAAS,CAAC,SAAS,GAAG;gBACrC,qBAAqB;YACvB;QACF;QACA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,sBAAsB;QACpC,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 1438, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/lib/hooks/use-scroll-to-bottom.ts"], "sourcesContent": ["import { useEffect, useRef, type RefObject } from \"react\";\r\n\r\nexport function useScrollToBottom<T extends HTMLElement>(): [\r\n  RefObject<T | null>,\r\n  RefObject<T | null>,\r\n] {\r\n  const containerRef = useRef<T | null>(null);\r\n  const endRef = useRef<T | null>(null);\r\n  const shouldScrollRef = useRef(true);\r\n\r\n  useEffect(() => {\r\n    const container = containerRef.current;\r\n    const end = endRef.current;\r\n\r\n    if (container && end) {\r\n      // Initial scroll\r\n      end.scrollIntoView({ behavior: \"instant\", block: \"end\" });\r\n\r\n      // Check if user has scrolled up\r\n      const handleScroll = () => {\r\n        if (!container) return;\r\n        \r\n        const isAtBottom = Math.abs(\r\n          (container.scrollHeight - container.scrollTop) - container.clientHeight\r\n        ) < 10;\r\n        \r\n        shouldScrollRef.current = isAtBottom;\r\n      };\r\n\r\n      const observer = new MutationObserver(() => {\r\n        // Only scroll if we're at the bottom or it's a new message\r\n        if (shouldScrollRef.current) {\r\n          end.scrollIntoView({ behavior: \"instant\", block: \"end\" });\r\n        }\r\n      });\r\n\r\n      observer.observe(container, {\r\n        childList: true,\r\n        subtree: true, // Watch nested changes\r\n        characterData: true, // Watch text changes\r\n      });\r\n\r\n      // Add scroll listener\r\n      container.addEventListener('scroll', handleScroll);\r\n\r\n      return () => {\r\n        observer.disconnect();\r\n        container.removeEventListener('scroll', handleScroll);\r\n      };\r\n    }\r\n  }, []);\r\n\r\n  return [containerRef, endRef];\r\n}"], "names": [], "mappings": ";;;AAAA;;AAEO,SAAS;IAId,MAAM,eAAe,CAAA,GAAA,8SAAA,CAAA,SAAM,AAAD,EAAY;IACtC,MAAM,SAAS,CAAA,GAAA,8SAAA,CAAA,SAAM,AAAD,EAAY;IAChC,MAAM,kBAAkB,CAAA,GAAA,8SAAA,CAAA,SAAM,AAAD,EAAE;IAE/B,CAAA,GAAA,8SAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,YAAY,aAAa,OAAO;QACtC,MAAM,MAAM,OAAO,OAAO;QAE1B,IAAI,aAAa,KAAK;YACpB,iBAAiB;YACjB,IAAI,cAAc,CAAC;gBAAE,UAAU;gBAAW,OAAO;YAAM;YAEvD,gCAAgC;YAChC,MAAM,eAAe;gBACnB,IAAI,CAAC,WAAW;gBAEhB,MAAM,aAAa,KAAK,GAAG,CACzB,AAAC,UAAU,YAAY,GAAG,UAAU,SAAS,GAAI,UAAU,YAAY,IACrE;gBAEJ,gBAAgB,OAAO,GAAG;YAC5B;YAEA,MAAM,WAAW,IAAI,iBAAiB;gBACpC,2DAA2D;gBAC3D,IAAI,gBAAgB,OAAO,EAAE;oBAC3B,IAAI,cAAc,CAAC;wBAAE,UAAU;wBAAW,OAAO;oBAAM;gBACzD;YACF;YAEA,SAAS,OAAO,CAAC,WAAW;gBAC1B,WAAW;gBACX,SAAS;gBACT,eAAe;YACjB;YAEA,sBAAsB;YACtB,UAAU,gBAAgB,CAAC,UAAU;YAErC,OAAO;gBACL,SAAS,UAAU;gBACnB,UAAU,mBAAmB,CAAC,UAAU;YAC1C;QACF;IACF,GAAG,EAAE;IAEL,OAAO;QAAC;QAAc;KAAO;AAC/B", "debugId": null}}, {"offset": {"line": 1495, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/lib/hooks/use-preview.ts"], "sourcesContent": ["import { useCallback, useMemo } from 'react';\r\nimport useS<PERSON> from 'swr';\r\n\r\nconst initialPreviewData: PreviewBlock = {\r\n  documentId: 'init',\r\n  content: '',\r\n  kind: 'text',\r\n  title: '',\r\n  status: 'idle',\r\n  isVisible: false,\r\n  boundingBox: {\r\n    top: 0,\r\n    left: 0,\r\n    width: 0,\r\n    height: 0,\r\n  },\r\n};\r\n\r\nexport interface PreviewBlock {\r\n  documentId: string;\r\n  content: string;\r\n  kind: string;\r\n  title: string;\r\n  status: 'idle' | 'streaming';\r\n  isVisible: boolean;\r\n  boundingBox: {\r\n    top: number;\r\n    left: number;\r\n    width: number;\r\n    height: number;\r\n  };\r\n}\r\n\r\n\r\nexport function usePreview() {\r\n  const { data: localPreview, mutate: setLocalPreview } = useSWR<PreviewBlock>(\r\n    'preview',\r\n    null,\r\n    {\r\n      fallbackData: initialPreviewData,\r\n    }\r\n  );\r\n\r\n  const preview = useMemo(() => {\r\n    if (!localPreview) return initialPreviewData;\r\n    return localPreview;\r\n  }, [localPreview]);\r\n\r\n  const setPreview = useCallback(\r\n    (updaterFn: PreviewBlock | ((currentPreview: PreviewBlock) => PreviewBlock)) => {\r\n      setLocalPreview((currentPreview) => {\r\n        const previewToUpdate = currentPreview || initialPreviewData;\r\n\r\n        if (typeof updaterFn === 'function') {\r\n          return updaterFn(previewToUpdate);\r\n        }\r\n\r\n        return updaterFn;\r\n      });\r\n    },\r\n    [setLocalPreview]\r\n  );\r\n\r\n  return useMemo(() => ({ preview, setPreview }), [preview, setPreview]);\r\n}"], "names": [], "mappings": ";;;AAAA;AACA;AAAA;;;AAEA,MAAM,qBAAmC;IACvC,YAAY;IACZ,SAAS;IACT,MAAM;IACN,OAAO;IACP,QAAQ;IACR,WAAW;IACX,aAAa;QACX,KAAK;QACL,MAAM;QACN,OAAO;QACP,QAAQ;IACV;AACF;AAkBO,SAAS;IACd,MAAM,EAAE,MAAM,YAAY,EAAE,QAAQ,eAAe,EAAE,GAAG,CAAA,GAAA,mOAAA,CAAA,UAAM,AAAD,EAC3D,WACA,MACA;QACE,cAAc;IAChB;IAGF,MAAM,UAAU,CAAA,GAAA,8SAAA,CAAA,UAAO,AAAD,EAAE;QACtB,IAAI,CAAC,cAAc,OAAO;QAC1B,OAAO;IACT,GAAG;QAAC;KAAa;IAEjB,MAAM,aAAa,CAAA,GAAA,8SAAA,CAAA,cAAW,AAAD,EAC3B,CAAC;QACC,gBAAgB,CAAC;YACf,MAAM,kBAAkB,kBAAkB;YAE1C,IAAI,OAAO,cAAc,YAAY;gBACnC,OAAO,UAAU;YACnB;YAEA,OAAO;QACT;IACF,GACA;QAAC;KAAgB;IAGnB,OAAO,CAAA,GAAA,8SAAA,CAAA,UAAO,AAAD,EAAE,IAAM,CAAC;YAAE;YAAS;QAAW,CAAC,GAAG;QAAC;QAAS;KAAW;AACvE", "debugId": null}}, {"offset": {"line": 1552, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/lib/hooks/use-user-message-id.ts"], "sourcesContent": ["'use client';\r\n\r\nimport useSWR from 'swr';\r\n\r\nexport function useUserMessageId() {\r\n  const { data: userMessageIdFromServer, mutate: setUserMessageIdFromServer } =\r\n    useSWR('userMessageIdFromServer', null);\r\n\r\n  return { userMessageIdFromServer, setUserMessageIdFromServer };\r\n}\r\n"], "names": [], "mappings": ";;;AAEA;AAAA;AAFA;;AAIO,SAAS;IACd,MAAM,EAAE,MAAM,uBAAuB,EAAE,QAAQ,0BAA0B,EAAE,GACzE,CAAA,GAAA,mOAAA,CAAA,UAAM,AAAD,EAAE,2BAA2B;IAEpC,OAAO;QAAE;QAAyB;IAA2B;AAC/D", "debugId": null}}, {"offset": {"line": 1572, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/lib/design-tokens.ts"], "sourcesContent": ["/**\r\n * 전문적인 디자인 토큰 시스템\r\n * 일관성 있는 UI/UX를 위한 디자인 시스템\r\n */\r\n\r\nexport const designTokens = {\r\n  // 색상 시스템 - 전문적이고 세련된 팔레트\r\n  colors: {\r\n    // Primary - 신뢰감 있는 블루 계열\r\n    primary: {\r\n      50: '#f0f9ff',\r\n      100: '#e0f2fe', \r\n      200: '#bae6fd',\r\n      300: '#7dd3fc',\r\n      400: '#38bdf8',\r\n      500: '#0ea5e9', // 메인 컬러\r\n      600: '#0284c7',\r\n      700: '#0369a1',\r\n      800: '#075985',\r\n      900: '#0c4a6e',\r\n    },\r\n    \r\n    // Success - 자연스러운 그린\r\n    success: {\r\n      50: '#f0fdf4',\r\n      100: '#dcfce7',\r\n      200: '#bbf7d0',\r\n      300: '#86efac',\r\n      400: '#4ade80',\r\n      500: '#22c55e',\r\n      600: '#16a34a',\r\n      700: '#15803d',\r\n      800: '#166534',\r\n      900: '#14532d',\r\n    },\r\n    \r\n    // Warning - 따뜻한 오렌지\r\n    warning: {\r\n      50: '#fffbeb',\r\n      100: '#fef3c7',\r\n      200: '#fde68a',\r\n      300: '#fcd34d',\r\n      400: '#fbbf24',\r\n      500: '#f59e0b',\r\n      600: '#d97706',\r\n      700: '#b45309',\r\n      800: '#92400e',\r\n      900: '#78350f',\r\n    },\r\n    \r\n    // Error - 부드러운 레드\r\n    error: {\r\n      50: '#fef2f2',\r\n      100: '#fee2e2',\r\n      200: '#fecaca',\r\n      300: '#fca5a5',\r\n      400: '#f87171',\r\n      500: '#ef4444',\r\n      600: '#dc2626',\r\n      700: '#b91c1c',\r\n      800: '#991b1b',\r\n      900: '#7f1d1d',\r\n    },\r\n    \r\n    // Neutral - 세련된 그레이 스케일\r\n    neutral: {\r\n      50: '#fafafa',\r\n      100: '#f5f5f5',\r\n      200: '#e5e5e5',\r\n      300: '#d4d4d4',\r\n      400: '#a3a3a3',\r\n      500: '#737373',\r\n      600: '#525252',\r\n      700: '#404040',\r\n      800: '#262626',\r\n      900: '#171717',\r\n    },\r\n  },\r\n  \r\n  // 타이포그래피 시스템\r\n  typography: {\r\n    fontFamily: {\r\n      sans: ['Inter', 'system-ui', 'sans-serif'],\r\n      mono: ['JetBrains Mono', 'monospace'],\r\n    },\r\n    fontSize: {\r\n      xs: ['0.75rem', { lineHeight: '1rem' }],\r\n      sm: ['0.875rem', { lineHeight: '1.25rem' }],\r\n      base: ['1rem', { lineHeight: '1.5rem' }],\r\n      lg: ['1.125rem', { lineHeight: '1.75rem' }],\r\n      xl: ['1.25rem', { lineHeight: '1.75rem' }],\r\n      '2xl': ['1.5rem', { lineHeight: '2rem' }],\r\n      '3xl': ['1.875rem', { lineHeight: '2.25rem' }],\r\n    },\r\n    fontWeight: {\r\n      normal: '400',\r\n      medium: '500',\r\n      semibold: '600',\r\n      bold: '700',\r\n    },\r\n  },\r\n  \r\n  // 간격 시스템\r\n  spacing: {\r\n    xs: '0.25rem',   // 4px\r\n    sm: '0.5rem',    // 8px\r\n    md: '0.75rem',   // 12px\r\n    lg: '1rem',      // 16px\r\n    xl: '1.5rem',    // 24px\r\n    '2xl': '2rem',   // 32px\r\n    '3xl': '3rem',   // 48px\r\n  },\r\n  \r\n  // 반지름 시스템\r\n  borderRadius: {\r\n    sm: '0.375rem',  // 6px\r\n    md: '0.5rem',    // 8px\r\n    lg: '0.75rem',   // 12px\r\n    xl: '1rem',      // 16px\r\n    '2xl': '1.5rem', // 24px\r\n  },\r\n  \r\n  // 그림자 시스템 - 깊이감 있는 전문적 그림자\r\n  shadows: {\r\n    sm: '0 1px 2px 0 rgb(0 0 0 / 0.05)',\r\n    md: '0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)',\r\n    lg: '0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)',\r\n    xl: '0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1)',\r\n    inner: 'inset 0 2px 4px 0 rgb(0 0 0 / 0.05)',\r\n  },\r\n  \r\n  // 애니메이션 시스템\r\n  animation: {\r\n    duration: {\r\n      fast: '150ms',\r\n      normal: '200ms',\r\n      slow: '300ms',\r\n    },\r\n    easing: {\r\n      ease: 'cubic-bezier(0.4, 0, 0.2, 1)',\r\n      easeIn: 'cubic-bezier(0.4, 0, 1, 1)',\r\n      easeOut: 'cubic-bezier(0, 0, 0.2, 1)',\r\n      easeInOut: 'cubic-bezier(0.4, 0, 0.2, 1)',\r\n    },\r\n  },\r\n} as const;\r\n\r\n// 컴포넌트별 스타일 프리셋\r\nexport const componentStyles = {\r\n  // 카드 스타일\r\n  card: {\r\n    base: `\r\n      bg-white/80 backdrop-blur-sm border border-neutral-200/60 \r\n      rounded-xl shadow-md hover:shadow-lg transition-all duration-200\r\n    `,\r\n    interactive: `\r\n      hover:border-neutral-300/80 hover:-translate-y-0.5 \r\n      cursor-pointer active:scale-[0.98]\r\n    `,\r\n    success: `\r\n      bg-gradient-to-br from-success-50/90 to-success-100/70 \r\n      border-success-200/60 hover:border-success-300/80\r\n    `,\r\n    warning: `\r\n      bg-gradient-to-br from-warning-50/90 to-warning-100/70 \r\n      border-warning-200/60 hover:border-warning-300/80\r\n    `,\r\n    error: `\r\n      bg-gradient-to-br from-error-50/90 to-error-100/70 \r\n      border-error-200/60 hover:border-error-300/80\r\n    `,\r\n  },\r\n  \r\n  // 버튼 스타일\r\n  button: {\r\n    primary: `\r\n      bg-primary-500 hover:bg-primary-600 text-white \r\n      shadow-md hover:shadow-lg active:scale-95\r\n      transition-all duration-200 font-medium\r\n    `,\r\n    secondary: `\r\n      bg-neutral-100 hover:bg-neutral-200 text-neutral-700 \r\n      border border-neutral-300 hover:border-neutral-400\r\n      transition-all duration-200 font-medium\r\n    `,\r\n    ghost: `\r\n      bg-transparent hover:bg-neutral-100 text-neutral-600 hover:text-neutral-800\r\n      transition-all duration-200 font-medium\r\n    `,\r\n  },\r\n  \r\n  // 배지 스타일\r\n  badge: {\r\n    success: `\r\n      bg-success-100 text-success-700 border border-success-200\r\n      font-medium text-xs px-2 py-1 rounded-md\r\n    `,\r\n    warning: `\r\n      bg-warning-100 text-warning-700 border border-warning-200\r\n      font-medium text-xs px-2 py-1 rounded-md\r\n    `,\r\n    info: `\r\n      bg-primary-100 text-primary-700 border border-primary-200\r\n      font-medium text-xs px-2 py-1 rounded-md\r\n    `,\r\n    neutral: `\r\n      bg-neutral-100 text-neutral-700 border border-neutral-200\r\n      font-medium text-xs px-2 py-1 rounded-md\r\n    `,\r\n  },\r\n  \r\n  // 아이콘 컨테이너\r\n  iconContainer: {\r\n    sm: `\r\n      flex h-6 w-6 items-center justify-center rounded-full\r\n      bg-neutral-100 text-neutral-600\r\n    `,\r\n    md: `\r\n      flex h-8 w-8 items-center justify-center rounded-full\r\n      bg-neutral-100 text-neutral-600\r\n    `,\r\n    lg: `\r\n      flex h-10 w-10 items-center justify-center rounded-full\r\n      bg-neutral-100 text-neutral-600\r\n    `,\r\n  },\r\n} as const;\r\n\r\n// 유틸리티 함수들\r\nexport const getColorClass = (color: keyof typeof designTokens.colors, shade: number = 500) => {\r\n  return `${color}-${shade}`;\r\n};\r\n\r\nexport const getSpacingClass = (size: keyof typeof designTokens.spacing) => {\r\n  return designTokens.spacing[size];\r\n};\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;AAEM,MAAM,eAAe;IAC1B,yBAAyB;IACzB,QAAQ;QACN,yBAAyB;QACzB,SAAS;YACP,IAAI;YACJ,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;QACP;QAEA,qBAAqB;QACrB,SAAS;YACP,IAAI;YACJ,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;QACP;QAEA,oBAAoB;QACpB,SAAS;YACP,IAAI;YACJ,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;QACP;QAEA,kBAAkB;QAClB,OAAO;YACL,IAAI;YACJ,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;QACP;QAEA,wBAAwB;QACxB,SAAS;YACP,IAAI;YACJ,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;QACP;IACF;IAEA,aAAa;IACb,YAAY;QACV,YAAY;YACV,MAAM;gBAAC;gBAAS;gBAAa;aAAa;YAC1C,MAAM;gBAAC;gBAAkB;aAAY;QACvC;QACA,UAAU;YACR,IAAI;gBAAC;gBAAW;oBAAE,YAAY;gBAAO;aAAE;YACvC,IAAI;gBAAC;gBAAY;oBAAE,YAAY;gBAAU;aAAE;YAC3C,MAAM;gBAAC;gBAAQ;oBAAE,YAAY;gBAAS;aAAE;YACxC,IAAI;gBAAC;gBAAY;oBAAE,YAAY;gBAAU;aAAE;YAC3C,IAAI;gBAAC;gBAAW;oBAAE,YAAY;gBAAU;aAAE;YAC1C,OAAO;gBAAC;gBAAU;oBAAE,YAAY;gBAAO;aAAE;YACzC,OAAO;gBAAC;gBAAY;oBAAE,YAAY;gBAAU;aAAE;QAChD;QACA,YAAY;YACV,QAAQ;YACR,QAAQ;YACR,UAAU;YACV,MAAM;QACR;IACF;IAEA,SAAS;IACT,SAAS;QACP,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,OAAO;QACP,OAAO;IACT;IAEA,UAAU;IACV,cAAc;QACZ,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,OAAO;IACT;IAEA,2BAA2B;IAC3B,SAAS;QACP,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,OAAO;IACT;IAEA,YAAY;IACZ,WAAW;QACT,UAAU;YACR,MAAM;YACN,QAAQ;YACR,MAAM;QACR;QACA,QAAQ;YACN,MAAM;YACN,QAAQ;YACR,SAAS;YACT,WAAW;QACb;IACF;AACF;AAGO,MAAM,kBAAkB;IAC7B,SAAS;IACT,MAAM;QACJ,MAAM,CAAC;;;IAGP,CAAC;QACD,aAAa,CAAC;;;IAGd,CAAC;QACD,SAAS,CAAC;;;IAGV,CAAC;QACD,SAAS,CAAC;;;IAGV,CAAC;QACD,OAAO,CAAC;;;IAGR,CAAC;IACH;IAEA,SAAS;IACT,QAAQ;QACN,SAAS,CAAC;;;;IAIV,CAAC;QACD,WAAW,CAAC;;;;IAIZ,CAAC;QACD,OAAO,CAAC;;;IAGR,CAAC;IACH;IAEA,SAAS;IACT,OAAO;QACL,SAAS,CAAC;;;IAGV,CAAC;QACD,SAAS,CAAC;;;IAGV,CAAC;QACD,MAAM,CAAC;;;IAGP,CAAC;QACD,SAAS,CAAC;;;IAGV,CAAC;IACH;IAEA,WAAW;IACX,eAAe;QACb,IAAI,CAAC;;;IAGL,CAAC;QACD,IAAI,CAAC;;;IAGL,CAAC;QACD,IAAI,CAAC;;;IAGL,CAAC;IACH;AACF;AAGO,MAAM,gBAAgB,CAAC,OAAyC,QAAgB,GAAG;IACxF,OAAO,GAAG,MAAM,CAAC,EAAE,OAAO;AAC5B;AAEO,MAAM,kBAAkB,CAAC;IAC9B,OAAO,aAAa,OAAO,CAAC,KAAK;AACnC", "debugId": null}}, {"offset": {"line": 1843, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/lib/hooks/use-server-health.ts"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect, useCallback } from 'react';\n\nexport interface ServerHealthStatus {\n  isHealthy: boolean;\n  isLoading: boolean;\n  error: string | null;\n  lastChecked: Date | null;\n  responseTime: number | null;\n}\n\nconst HEALTH_CHECK_URL = '/api/health';\nconst CHECK_INTERVAL = 30000; // 30초마다 체크\nconst TIMEOUT_DURATION = 5000; // 5초 타임아웃\n\nexport function useServerHealth(enabled: boolean = true, modelId?: string) {\n  const [status, setStatus] = useState<ServerHealthStatus>({\n    isHealthy: false,\n    isLoading: true,\n    error: null,\n    lastChecked: null,\n    responseTime: null,\n  });\n\n  const checkHealth = useCallback(async () => {\n    if (!enabled) return;\n\n    setStatus(prev => ({ ...prev, isLoading: true, error: null }));\n\n    const startTime = Date.now();\n\n    try {\n      const controller = new AbortController();\n      const timeoutId = setTimeout(() => controller.abort(), TIMEOUT_DURATION);\n\n      // 모델 ID가 있으면 쿼리 파라미터로 전달\n      const url = modelId ? `${HEALTH_CHECK_URL}?modelId=${encodeURIComponent(modelId)}` : HEALTH_CHECK_URL;\n\n      const response = await fetch(url, {\n        method: 'GET',\n        signal: controller.signal,\n        headers: {\n          'Content-Type': 'application/json',\n        },\n      });\n\n      clearTimeout(timeoutId);\n      const clientResponseTime = Date.now() - startTime;\n\n      if (response.ok) {\n        const data = await response.json();\n        setStatus({\n          isHealthy: data.status === 'healthy',\n          isLoading: false,\n          error: null,\n          lastChecked: new Date(),\n          responseTime: data.responseTime || clientResponseTime,\n        });\n      } else {\n        const errorData = await response.json().catch(() => ({}));\n        throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);\n      }\n    } catch (error) {\n      const responseTime = Date.now() - startTime;\n      let errorMessage = 'Unknown error';\n\n      if (error instanceof Error) {\n        if (error.name === 'AbortError') {\n          errorMessage = 'Request timeout';\n        } else if (error.message.includes('fetch')) {\n          errorMessage = 'Network error';\n        } else {\n          errorMessage = error.message;\n        }\n      }\n\n      setStatus({\n        isHealthy: false,\n        isLoading: false,\n        error: errorMessage,\n        lastChecked: new Date(),\n        responseTime,\n      });\n    }\n  }, [enabled, modelId]);\n\n  // 초기 체크 및 주기적 체크\n  useEffect(() => {\n    if (!enabled) return;\n\n    // 즉시 체크\n    checkHealth();\n\n    // 주기적 체크\n    const interval = setInterval(checkHealth, CHECK_INTERVAL);\n\n    return () => {\n      clearInterval(interval);\n    };\n  }, [checkHealth, enabled]);\n\n  // 수동 새로고침\n  const refresh = useCallback(() => {\n    checkHealth();\n  }, [checkHealth]);\n\n  return {\n    ...status,\n    refresh,\n  };\n}\n"], "names": [], "mappings": ";;;AAEA;AAFA;;AAYA,MAAM,mBAAmB;AACzB,MAAM,iBAAiB,OAAO,WAAW;AACzC,MAAM,mBAAmB,MAAM,UAAU;AAElC,SAAS,gBAAgB,UAAmB,IAAI,EAAE,OAAgB;IACvE,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,8SAAA,CAAA,WAAQ,AAAD,EAAsB;QACvD,WAAW;QACX,WAAW;QACX,OAAO;QACP,aAAa;QACb,cAAc;IAChB;IAEA,MAAM,cAAc,CAAA,GAAA,8SAAA,CAAA,cAAW,AAAD,EAAE;QAC9B,IAAI,CAAC,SAAS;QAEd,UAAU,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,WAAW;gBAAM,OAAO;YAAK,CAAC;QAE5D,MAAM,YAAY,KAAK,GAAG;QAE1B,IAAI;YACF,MAAM,aAAa,IAAI;YACvB,MAAM,YAAY,WAAW,IAAM,WAAW,KAAK,IAAI;YAEvD,yBAAyB;YACzB,MAAM,MAAM,UAAU,GAAG,iBAAiB,SAAS,EAAE,mBAAmB,UAAU,GAAG;YAErF,MAAM,WAAW,MAAM,MAAM,KAAK;gBAChC,QAAQ;gBACR,QAAQ,WAAW,MAAM;gBACzB,SAAS;oBACP,gBAAgB;gBAClB;YACF;YAEA,aAAa;YACb,MAAM,qBAAqB,KAAK,GAAG,KAAK;YAExC,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,UAAU;oBACR,WAAW,KAAK,MAAM,KAAK;oBAC3B,WAAW;oBACX,OAAO;oBACP,aAAa,IAAI;oBACjB,cAAc,KAAK,YAAY,IAAI;gBACrC;YACF,OAAO;gBACL,MAAM,YAAY,MAAM,SAAS,IAAI,GAAG,KAAK,CAAC,IAAM,CAAC,CAAC,CAAC;gBACvD,MAAM,IAAI,MAAM,UAAU,KAAK,IAAI,CAAC,KAAK,EAAE,SAAS,MAAM,CAAC,EAAE,EAAE,SAAS,UAAU,EAAE;YACtF;QACF,EAAE,OAAO,OAAO;YACd,MAAM,eAAe,KAAK,GAAG,KAAK;YAClC,IAAI,eAAe;YAEnB,IAAI,iBAAiB,OAAO;gBAC1B,IAAI,MAAM,IAAI,KAAK,cAAc;oBAC/B,eAAe;gBACjB,OAAO,IAAI,MAAM,OAAO,CAAC,QAAQ,CAAC,UAAU;oBAC1C,eAAe;gBACjB,OAAO;oBACL,eAAe,MAAM,OAAO;gBAC9B;YACF;YAEA,UAAU;gBACR,WAAW;gBACX,WAAW;gBACX,OAAO;gBACP,aAAa,IAAI;gBACjB;YACF;QACF;IACF,GAAG;QAAC;QAAS;KAAQ;IAErB,iBAAiB;IACjB,CAAA,GAAA,8SAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,SAAS;QAEd,QAAQ;QACR;QAEA,SAAS;QACT,MAAM,WAAW,YAAY,aAAa;QAE1C,OAAO;YACL,cAAc;QAChB;IACF,GAAG;QAAC;QAAa;KAAQ;IAEzB,UAAU;IACV,MAAM,UAAU,CAAA,GAAA,8SAAA,CAAA,cAAW,AAAD,EAAE;QAC1B;IACF,GAAG;QAAC;KAAY;IAEhB,OAAO;QACL,GAAG,MAAM;QACT;IACF;AACF", "debugId": null}}, {"offset": {"line": 1950, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/hooks/use-layer-configs.ts"], "sourcesContent": ["import { useLayerManager as useLayerManagerProvider } from \"@/providers/tool-invocation-provider\";\r\nimport type { GeoJSONLayerProps, LayerProps } from \"@geon-map/odf\";\r\nimport type { ManagedLayerProps } from \"@/types/layer-manager\";\r\n\r\n/**\r\n * @deprecated Use useLayerManager instead for centralized layer management\r\n * This hook is kept for backward compatibility\r\n */\r\nexport const useLayerConfigs = (): LayerProps[] => {\r\n  const { layers } = useLayerManagerProvider();\r\n\r\n  // ManagedLayerProps를 LayerProps로 안전하게 변환\r\n  return layers.map((layer: ManagedLayerProps): LayerProps => {\r\n    const { source, toolCallId, ...baseProps } = layer;\r\n\r\n    // 레이어 타입에 따른 적절한 변환\r\n    if (layer.type === \"geojson\") {\r\n      return {\r\n        id: baseProps.id,\r\n        type: baseProps.type,\r\n        data: baseProps.data, // GeoJSON FeatureCollection 직접 전달\r\n        visible: baseProps.visible ?? true, // 명시적으로 visible 속성 전달\r\n        service: baseProps.service,\r\n        opacity: baseProps.opacity,\r\n        zIndex: baseProps.zIndex,\r\n        bbox: baseProps.bbox,\r\n        autoFit: baseProps.autoFit,\r\n        renderOptions: baseProps.style ? {\r\n          style: baseProps.style\r\n        } : undefined,\r\n        // service: baseProps.service || \"geojson\",\r\n        dataProjectionCode: baseProps.dataProjectionCode || \"EPSG:5186\",\r\n        featureProjectionCode: baseProps.featureProjectionCode || \"EPSG:5186\",\r\n      } as GeoJSONLayerProps;\r\n    }\r\n\r\n    // geoserver 레이어의 경우\r\n    if (layer.type === \"geoserver\") {\r\n      return {\r\n        id: baseProps.id,\r\n        type: \"geoserver\",\r\n        server: baseProps.server || \"\",\r\n        layer: baseProps.layer || \"\",\r\n        service: baseProps.service || \"wfs\",\r\n        info: baseProps.info || {\r\n          lyrId: baseProps.id,\r\n          lyrNm: baseProps.name || \"Unknown Layer\",\r\n        },\r\n        name: baseProps.name,\r\n        visible: baseProps.visible ?? true, // 명시적으로 visible 속성 전달\r\n        opacity: baseProps.opacity,\r\n        zIndex: baseProps.zIndex,\r\n        filter: baseProps.filter,\r\n        bbox: baseProps.bbox,\r\n        autoFit: baseProps.autoFit,\r\n        method: baseProps.method,\r\n        crtfckey: baseProps.crtfckey,\r\n        projection: baseProps.projection,\r\n        geometryType: baseProps.geometryType,\r\n        serviceTy: baseProps.serviceTy,\r\n        renderOptions: baseProps.style ? {\r\n          style: baseProps.style\r\n        } : baseProps.renderOptions,\r\n      } as LayerProps;\r\n    }\r\n\r\n    // 기타 레이어 타입들 (api, kml, csv 등)\r\n    return {\r\n      ...baseProps,\r\n      // 필수 속성들 보장\r\n      type: baseProps.type,\r\n      id: baseProps.id,\r\n      visible: baseProps.visible ?? true, // 명시적으로 visible 속성 전달\r\n      autoFit: baseProps.autoFit,\r\n      fitDuration: 100\r\n    } as LayerProps;\r\n  });\r\n};\r\n\r\n/**\r\n * 새로운 중앙집중식 레이어 관리 훅\r\n * 레이어 생성, 필터링, 스타일링을 모두 처리\r\n */\r\nexport const useLayerManager = () => {\r\n  return useLayerManagerProvider();\r\n};\r\n"], "names": [], "mappings": ";;;;AAAA;;AAQO,MAAM,kBAAkB;IAC7B,MAAM,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,4IAAA,CAAA,kBAAuB,AAAD;IAEzC,yCAAyC;IACzC,OAAO,OAAO,GAAG,CAAC,CAAC;QACjB,MAAM,EAAE,MAAM,EAAE,UAAU,EAAE,GAAG,WAAW,GAAG;QAE7C,oBAAoB;QACpB,IAAI,MAAM,IAAI,KAAK,WAAW;YAC5B,OAAO;gBACL,IAAI,UAAU,EAAE;gBAChB,MAAM,UAAU,IAAI;gBACpB,MAAM,UAAU,IAAI;gBACpB,SAAS,UAAU,OAAO,IAAI;gBAC9B,SAAS,UAAU,OAAO;gBAC1B,SAAS,UAAU,OAAO;gBAC1B,QAAQ,UAAU,MAAM;gBACxB,MAAM,UAAU,IAAI;gBACpB,SAAS,UAAU,OAAO;gBAC1B,eAAe,UAAU,KAAK,GAAG;oBAC/B,OAAO,UAAU,KAAK;gBACxB,IAAI;gBACJ,2CAA2C;gBAC3C,oBAAoB,UAAU,kBAAkB,IAAI;gBACpD,uBAAuB,UAAU,qBAAqB,IAAI;YAC5D;QACF;QAEA,oBAAoB;QACpB,IAAI,MAAM,IAAI,KAAK,aAAa;YAC9B,OAAO;gBACL,IAAI,UAAU,EAAE;gBAChB,MAAM;gBACN,QAAQ,UAAU,MAAM,IAAI;gBAC5B,OAAO,UAAU,KAAK,IAAI;gBAC1B,SAAS,UAAU,OAAO,IAAI;gBAC9B,MAAM,UAAU,IAAI,IAAI;oBACtB,OAAO,UAAU,EAAE;oBACnB,OAAO,UAAU,IAAI,IAAI;gBAC3B;gBACA,MAAM,UAAU,IAAI;gBACpB,SAAS,UAAU,OAAO,IAAI;gBAC9B,SAAS,UAAU,OAAO;gBAC1B,QAAQ,UAAU,MAAM;gBACxB,QAAQ,UAAU,MAAM;gBACxB,MAAM,UAAU,IAAI;gBACpB,SAAS,UAAU,OAAO;gBAC1B,QAAQ,UAAU,MAAM;gBACxB,UAAU,UAAU,QAAQ;gBAC5B,YAAY,UAAU,UAAU;gBAChC,cAAc,UAAU,YAAY;gBACpC,WAAW,UAAU,SAAS;gBAC9B,eAAe,UAAU,KAAK,GAAG;oBAC/B,OAAO,UAAU,KAAK;gBACxB,IAAI,UAAU,aAAa;YAC7B;QACF;QAEA,+BAA+B;QAC/B,OAAO;YACL,GAAG,SAAS;YACZ,YAAY;YACZ,MAAM,UAAU,IAAI;YACpB,IAAI,UAAU,EAAE;YAChB,SAAS,UAAU,OAAO,IAAI;YAC9B,SAAS,UAAU,OAAO;YAC1B,aAAa;QACf;IACF;AACF;AAMO,MAAM,kBAAkB;IAC7B,OAAO,CAAA,GAAA,4IAAA,CAAA,kBAAuB,AAAD;AAC/B", "debugId": null}}, {"offset": {"line": 2031, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/app/%28map%29/actions.ts"], "sourcesContent": ["'use server';\r\n\r\nimport { geon } from '@/lib/ai';\r\nimport { openai } from '@ai-sdk/openai';\r\nimport { VisibilityType } from '@/components/visibility-selector';\r\nimport { deleteMessagesByChatIdAfterTimestamp, getMessageById, updateChatVisiblityById } from '@/lib/db/queries';\r\nimport { CoreUserMessage, generateText } from 'ai';\r\nimport { cookies } from 'next/headers';\r\n\r\nexport async function saveModelId(model: string) {\r\n  const cookieStore = await cookies();\r\n  cookieStore.set('model-id', model);\r\n}\r\nexport async function saveDevModelId(model: string) {\r\n  const cookieStore = await cookies();\r\n  cookieStore.set('dev-model-id', model);\r\n}\r\n\r\nexport async function generateTitleFromUserMessage({\r\n  message,\r\n}: {\r\n  message: CoreUserMessage;\r\n}) {\r\n  const { text: title } = await generateText({\r\n    model: openai('gpt-4o-mini'),\r\n    system: `\\n\r\n    - you will generate a short title based on the first message a user begins a conversation with\r\n    - ensure it is not more than 80 characters long\r\n    - the title should be a summary of the user's message\r\n    - do not use quotes or colons`,\r\n    prompt: JSON.stringify(message),\r\n  });\r\n\r\n  return title;\r\n}\r\n\r\nexport async function deleteTrailingMessages({ id }: { id: string }) {\r\n  const [message] = await getMessageById({ id });\r\n\r\n  await deleteMessagesByChatIdAfterTimestamp({\r\n    chatId: message.chatId,\r\n    timestamp: message.createdAt,\r\n  });\r\n}\r\n\r\nexport async function updateChatVisibility({\r\n  chatId,\r\n  visibility,\r\n}: {\r\n  chatId: string;\r\n  visibility: VisibilityType;\r\n}) {\r\n  await updateChatVisiblityById({ chatId, visibility });\r\n}\r\n"], "names": [], "mappings": ";;;;;;IAoCsB,yBAAA,WAAA,GAAA,CAAA,GAAA,+TAAA,CAAA,wBAAA,EAAA,8CAAA,+TAAA,CAAA,aAAA,EAAA,KAAA,GAAA,+TAAA,CAAA,mBAAA,EAAA", "debugId": null}}]}